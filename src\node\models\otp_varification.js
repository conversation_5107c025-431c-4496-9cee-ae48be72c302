const Sequelize = require("sequelize");
module.exports = (sequelize) => {
  return sequelize.define("otp_varification", {
    id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      unique: true
    },
    store_id: {
      allowNull:false,
      type: Sequelize.INTEGER,
      references: {
        model: 'stores',
        key: 'id'
      },
    },
    mobile: {
      type: Sequelize.STRING
    },
    secret_token: {
      type: Sequelize.STRING
    },
    otp: {
        type: Sequelize.STRING
      }
  },
    {
      indexes: [
        {
          unique: true,
          fields: ['store_id', 'mobile']
        }
      ]
    });
};