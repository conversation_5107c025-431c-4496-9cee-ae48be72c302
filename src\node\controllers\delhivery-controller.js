const CONFIG = require('../config/config_' + [process.env.NODE_ENV || 'local'] + '.json');
//Axios Call
const axios = require('axios')
//database connection
const dbConnection = require("../models");
const path = require("path");
const fs = require("fs");
const Fulfillment = require("../shopifyObjects/Fulfillment");
const FulfillmentEvent = require('../shopifyObjects/FulfillmentEvents');
const pdf = require("pdf-creator-node");
const uploadImage = require("./ftp-controller");
const pdfMerger = require("pdf-merger-js");
const s3 = require("../helper/awsClient");
const sapController = require('./sap_controller');
const gstValidationController = require('./gstValidation-controller')
const refundController = require('./refund-controller')
const logger = require('../helper/appLoger')
const orderHistoryHelper = require('../helper/orderHistory')
const { getPaymentId, fetchRateForEmi } = require('./razorpay-controller');
const orderSyncController = require("./ordersync-controller");
const FulfillmentOrder = require('../shopifyObjects/FullfillmentOrder');
const { registerShipmentForTracking } = require('./clickpost-controller');
const extendedWarrantyServiceController = require('./extendedWarranty/extendedWarrantyService-controller');
const { Op } = require("sequelize");
const zippeeController = require('./zippee-controller');
const options = {
    headers: {
        "Accept": "application/json",
        "Authorization": 'Token ' + CONFIG.delhivery.token,
    }
}

// reverse flow of delhivery
exports.reverseShipment = async (item) => {
    try {
        let skuShipment = await dbConnection.mpsShipment.findOne({ where: { sku: item.sku } })
        const shopResponse = await dbConnection.shop.findOne({ where: { id: item.store_id } })
        const paramObj = {
            orderId: item.shopify_order_id,
            plantCode: item.plant_code,
            orderData: item,
            shopResponse: shopResponse.dataValues
        }
        if (skuShipment) {
            await this.reverseMpsShipment(paramObj)
        } else {
            await this.reverseSingleShipment(paramObj)
        }
    } catch (err) {
        console.log('Error reverseShipment=====>', err)
    }
}
// reverse single shipment
exports.reverseSingleShipment = (paramObj) => {
    return new Promise(async (resolve, reject) => {
        let { orderData } = paramObj
        let reqDataRes = await getDelhiveryObj(paramObj, shipmentType = "single", type = "Return")
        const delhiveryRes = await delhiveryRequest(orderData, reqDataRes.data, platformType = "DELHIVERY-ReverseSingle")
        const package = delhiveryRes.data.packages.length > 0 ? delhiveryRes.data.packages[0] : null
        let delhiveryObj = {
            store_id: orderData.store_id,
            order_id: orderData.shopify_order_id,
            status: package ? package.status : "",
            reference_number: package ? package.refnum : "",
            waybill_number: package && package.waybill ? package.waybill : "",
            response_message: delhiveryRes.data.rmk ? delhiveryRes.data.rmk : null,
            packing_slip_url: "",
            line_item_id: orderData.line_item_id,
            order_name: orderData.order_name,
            cfa_name: reqDataRes.cfaName,
            is_return: "1",
            return_order_name: orderData.return_order_name
        }
        if (package && package.remarks.length > 0) {
            delhiveryObj.response_message = package.remarks[0]
        }
        await upsertDelhiveryLog(delhiveryObj, orderData.order_name, isReturn = true)
        if (package && package.waybill) {
            let clickpostObj = clickpostReqObject(reqDataRes.data, orderData, package.waybill, "reverse")
            registerShipmentForTracking(clickpostObj)
            const packingSlipUrl = await generatePackingSlip(package.waybill, orderData.order_name)
            delhiveryObj.packing_slip_url = packingSlipUrl
            delhiveryObj.status = package.status
            delhiveryObj.response_message = null
            await upsertDelhiveryLog(delhiveryObj, orderData.order_name, isReturn = true)
            await dbConnection.orderItemSplit.update({ order_status: "Returned", return_applied_status: '1', is_return: "1", delhivery_status: "Pushed", middleware_status: "RETURN APPROVED", shipment_status: 'manifested', waybill_number: package.waybill }, { where: { order_name: orderData.order_name } })
            resolve({ status: true, data: delhiveryObj })
        } else {
            await dbConnection.orderItemSplit.update({ order_status: "Returned", return_applied_status: '1', is_return: "1", delhivery_status: "Failed", middleware_status: "FAILED", failed_reason: delhiveryRes?.data?.rmk }, { where: { order_name: orderData.order_name } })
            resolve({ status: false })
        }
    })
}
// reverse MPS shipment 
exports.reverseMpsShipment = (mpsObj) => {
    return new Promise(async (resolve, reject) => {
        let { orderData } = mpsObj
        let reqDataRes = await getDelhiveryObj(mpsObj, shipmentType = "mps", type = "Return")
        let delhiveryRes = await delhiveryRequest(orderData, reqDataRes.data, platformType = "DELHIVERY-ReverseMps")
        const package = delhiveryRes.data.packages.length > 0 ? delhiveryRes.data.packages[0] : null
        const delhiveryObj = {
            store_id: orderData.store_id,
            order_id: orderData.shopify_order_id,
            status: package ? package.status : "",
            reference_number: package ? package.refnum : "",
            waybill_number: reqDataRes.waybillNumbers.waybillNumbers,
            response_message: delhiveryRes.data.rmk ? delhiveryRes.data.rmk : null,
            packing_slip_url: "",
            line_item_id: orderData.line_item_id,
            order_name: orderData.order_name,
            cfa_name: reqDataRes.cfaName,
            is_return: "1",
            return_order_name: orderData.return_order_name
        }
        if (package && package.remarks.length > 0) {
            delhiveryObj.response_message = package.remarks[0]
        }
        await upsertDelhiveryLog(delhiveryObj, orderData.order_name, isReturn = true)
        // waybill number then update into order_split table with Completed status
        if (package && package.status != "Fail") {
            let clickpostObj = clickpostReqObject(reqDataRes.data, orderData, reqDataRes.waybillNumbers.waybillNumbers, "reverse")
            registerShipmentForTracking(clickpostObj)
            const packingSlipUrl = await generatePackingSlip(reqDataRes.waybillNumbers.waybillNumbers, orderData.order_name)
            delhiveryObj.packing_slip_url = packingSlipUrl
            delhiveryObj.status = package.status
            delhiveryObj.response_message = null

            await upsertDelhiveryLog(delhiveryObj, orderData.order_name, isReturn = true)
            await dbConnection.orderItemSplit.update({ order_status: "Returned", return_applied_status: '1', is_return: "1", delhivery_status: "Pushed", shipment_status: 'manifested', middleware_status: "RETURN APPROVED", waybill_number: package.waybill }, { where: { order_name: orderData.order_name } })
            resolve({ status: true, data: delhiveryObj })
        } else {
            await dbConnection.orderItemSplit.update({ order_status: "Returned", return_applied_status: '1', is_return: "1", delhivery_status: "Failed", middleware_status: "FAILED", failed_reason: delhiveryRes?.data?.rmk }, { where: { order_name: orderData.order_name } })
            resolve({ status: false })
        }
    })
}
// get waybill numbers 
let getWaybillNumber = async () => {
    try {
        let waybill = await axios.get(CONFIG.delhivery.waybill_url, options)
        let waybillArr = []
        waybillArr.push(waybill.data.split(",")[0])
        waybillArr.push(waybill.data.split(",")[1])
        return { waybill: waybillArr, waybillNumbers: waybill.data }
    } catch (err) {
        console.log('Error reverseShipment=====>', err)
        return
    }
}

// Reverse order in delhivery
exports.reverseOrderDelhivery = async (orderName) => {
    try {
        let orderData = await this.getorderSplitDataById(orderName)
        if (orderData.status) {
            orderData = orderData.data
            console.log("===================Push Data in 3PL First=====================" + orderName + "========\n")
            await this.reverseShipment(orderData)
            return
        }
        return
    } catch (err) {
        throw new Error(err)
    }
}

// update fulfillment Event
exports.updateFulfillmentEvent = async (paramObj) => {
    return new Promise(async (resolve, reject) => {
        try {
            let { orderId, fulfillmentId, shopResponse, shipmentStatus } = paramObj
            const fulfillData = {
                "status": shipmentStatus
            }
            let objFulfillmentEvent = new FulfillmentEvent(shopResponse.myshopify_domain, shopResponse.token, orderId, fulfillmentId)
            let fulfillmentEventRes = await objFulfillmentEvent.create(fulfillData)
            console.log('fulfillmentEventRes===>', fulfillmentEventRes)
            if (fulfillmentEventRes.data) {
                resolve(fulfillmentEventRes.data)
            }
        } catch (err) {
            resolve(err)
        }
    })
}

//SAP Billing Number Not Null in cancel order on SAP
exports.isSapBillNotNull = async (order, flag, sapObj) => {
    return new Promise(async (resolve, reject) => {
        try {
            let orderObject = {}
            const sapDataRes = await dbConnection.sapLog.findOne({ where: { order_name: order.order_name, is_cancel: "1" } })

            let giftCardAmout = parseFloat(order.order_amount) * parseFloat(order.gift_card_value)
            order.order_amount = parseFloat(order.order_amount) - giftCardAmout

            let sapParam = {
                orderData: order,
                docType: "ZWSR",
                plantCode: order.plant_code,
                reason: "Y02",
                billNo: order.sap_billing_number,
                rate: 0
            }
            if (!flag) {
                let cancelDelhiveryResponse = await this.cancelOrderDelhivery(order.waybill_number, order)
            } else {
                orderObject.delhivery_status = "Cancel"
            }
            let reqSapData = await sapController.getSapReqBody(sapParam)
            if (reqSapData.status) {
                const sapData = await sapController.pushOrderOnSAP(reqSapData.data)

                let helperObj = {
                    shopify_order_id: order.shopify_order_id,
                    shopify_order_name: order.shopify_order_name,
                    order_name: order.order_name,
                    platform_type: "SAP-CANCEL",
                    request_data: JSON.stringify(reqSapData.data),
                    response_status_code: sapData.status_code,
                    response_data: sapData.data ? JSON.stringify(sapData.data) : JSON.stringify(sapData)
                }
                await orderHistoryHelper.insertOrderHistory(helperObj)

                if (sapData.status) {
                    sapObj.sap_delivery_number = sapData.data.SAP_DLY_NUM,
                        sapObj.sap_order_number = sapData.data.SAP_SO_NUM,
                        sapObj.shopify_order_name = sapData.data.SHOPIFY_NUM,
                        sapObj.response_message = sapData?.data?.MESSAGE?.length > 0 ? sapData.data.MESSAGE[0].MESSAGE : "Didn't get any response from SAP"
                    const orderObject = {
                        sap_billing_number: null,
                        delhivery_status: "Cancel",
                        sap_delivery_number: sapData.data.SAP_DLY_NUM,
                        sap_order_number: sapData.data.SAP_SO_NUM,
                        sap_status: "Pushed"
                    }

                    if (sapDataRes) {
                        await dbConnection.sapLog.update(sapObj, { where: { order_name: order.order_name, is_cancel: '1' } })
                    } else {
                        await dbConnection.sapLog.create(sapObj)
                    }
                    await dbConnection.orderItemSplit.update(orderObject, { where: { order_name: order.order_name } })
                } else {
                    await dbConnection.orderItemSplit.update({ sap_status: "Failed", middleware_status: "FAILED", failed_reason: sapData?.data?.MESSAGE?.length > 0 ? sapData.data.MESSAGE[0].MESSAGE : "Didn't get any response from SAP" }, { where: { order_name: order.order_name } })
                    sapObj.response_message = sapData?.data?.MESSAGE?.length > 0 ? sapData.data.MESSAGE[0].MESSAGE : "Didn't get any response from SAP"
                    sapDataRes ? await dbConnection.sapLog.update(sapObj, { where: { order_name: order.order_name, is_cancel: "1" } }) : await dbConnection.sapLog.create(sapObj)
                }
            }
            else {
                console.log("Bililing number eslse part====")
                resolve({ status: false })
            }
            resolve({ status: true })
        } catch (err) {
            console.log('Error isSapBillNotNull=====>', err)
        }
    })
}

//SAP Billing Number Null in cancel order on SAP
exports.isSapBillNull = async (order, sapObj) => {
    return new Promise(async (resolve, reject) => {
        try {
            const delhiveryDataRes = await dbConnection.delhiveryLog.findOne({ where: { order_name: order.order_name, is_cancel: "1" } })
            const sapDataRes = await dbConnection.sapLog.findOne({ where: { order_name: order.order_name, is_cancel: "1" } })

            const delhiveryObj = {
                store_id: order.store_id,
                order_id: order.shopify_order_id,
                response_message: "Order is Canceled",
                packing_slip_url: "",
                line_item_id: order.line_item_id,
                rto_delivered_date: new Date().toISOString(),
                order_name: order.order_name,
                is_cancel: "1"
            }

            if (order.waybill_number != null && order.waybill_number != "null" && order.delhivery_status != "Cancel") {
                let cancelDelhiveryResponse = await this.cancelOrderDelhivery(order.waybill_number, order)
            } else {
                await dbConnection.orderItemSplit.update({ delhivery_status: "Cancel", middleware_status: "REFUND PENDING" }, { where: { order_name: order.order_name } })
                if (delhiveryDataRes) {
                    await dbConnection.delhiveryLog.update(delhiveryObj, { where: { order_name: order.order_name, is_cancel: "1" } })
                } else {
                    await dbConnection.delhiveryLog.create(delhiveryObj)
                }
            }
            console.log(order.logistics_partner, "inside sap bill null")
            if (order.is_cancel == "1" && (order.sap_billing_number == null || order.sap_billing_number == "null")) {
                console.log("sap bill null")
                await this.sapDelivery(order.order_name)
            } else {
                await dbConnection.orderItemSplit.update({ sap_status: "Cancel", sap_billing_number: null }, { where: { order_name: order.order_name } })
                if (sapDataRes) {
                    await dbConnection.sapLog.update({ is_cancel: "1" }, { where: { order_name: order.order_name, is_cancel: "1" } })
                } else {
                    await dbConnection.sapLog.create(sapObj)
                }
                console.log("in else part sap delivery")
                await this.sapDelivery(order.order_name, "1")
            }
            resolve({ status: true })
        } catch (err) {
            console.log('Error isSapBillNull=====>', err)
        }
    })
}

//order cancel api
exports.cancelOrderDelhivery = async (waybillNumber, order) => {
    return new Promise(async (resolve, reject) => {
        try {
            if (order.logistics_partner === "Zippee") {
                await zippeeController.cancelOrderOnZippee(order);
                return resolve({ status: true });
            }
            let waybillNumbers = waybillNumber?.split(",")
            for (waybill of waybillNumbers) {
                let data = {
                    "waybill": waybill,
                    "cancellation": "true"
                }
                let axiosConfig = {
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Token ' + CONFIG.delhivery.token
                    }
                };
                const cancelRes = await axios.post(CONFIG.delhivery.cancel_url, data, axiosConfig)
                // const cancelRes = {
                //     data:{
                //         status:true,
                //         remark:"Delhivery is Canceled"
                //     }
                // }
                console.log("cancelRes=====>>", cancelRes)
                let helperObj = {
                    shopify_order_id: order.shopify_order_id,
                    shopify_order_name: order.shopify_order_name,
                    order_name: order.order_name,
                    platform_type: "DELHIVERY-Cancel",
                    request_data: JSON.stringify(data),
                    response_status_code: cancelRes.status,
                    response_data: cancelRes.data ? JSON.stringify(cancelRes.data) : JSON.stringify(cancelRes)
                }
                await orderHistoryHelper.insertOrderHistory(helperObj)
                let delhiveryObj = {
                    store_id: order.store_id,
                    order_id: order.shopify_order_id,
                    waybill_number: order.waybill_number,
                    response_message: cancelRes.data.remark ? cancelRes.data.remark : null,
                    packing_slip_url: "",
                    line_item_id: order.line_item_id,
                    order_name: order.order_name,
                    rto_delivered_date: new Date().toISOString(),
                    is_cancel: "1"
                }
                const dlvRes = await dbConnection.delhiveryLog.findOne({ where: { order_name: order.order_name, is_cancel: "1" } })
                if (cancelRes.data.status == true) {
                    console.log('in if cancelRes')
                    if (dlvRes) {
                        await dbConnection.delhiveryLog.update(delhiveryObj, { where: { order_name: order.order_name, is_cancel: "1" } })
                    } else {
                        await dbConnection.delhiveryLog.create(delhiveryObj)
                    }
                    await dbConnection.orderItemSplit.update({ delhivery_status: "Cancel" }, { where: { order_name: order.order_name } })
                    resolve({ status: true })
                } else {
                    console.log('in else cancelRes')
                    if (dlvRes) {
                        await dbConnection.delhiveryLog.update({ response_message: "Order is not Canceled from Delhivery" }, { where: { order_name: order.order_name, is_cancel: "1" } })
                    } else {
                        delhiveryObj.response_message = "Order is not Canceled from Delhivery"
                        await dbConnection.delhiveryLog.create(delhiveryObj)
                    }
                    await dbConnection.orderItemSplit.update({ delhivery_status: "failed", middleware_status: "FAILED" }, { where: { order_name: order.order_name } })
                    resolve({ status: false })
                }
            }
        } catch (err) {
            console.log('Error cancelOrderDelhivery=====>', err)
        }
    })
}

// cancel order on SAP
exports.cancelOrder = async (orderName, flag = false) => {
    try {
        let orderData = await this.getorderSplitDataById(orderName)
        if (orderData.status) {
            let orderObj = {}
            let order = orderData.data
            orderObj.shipment_status = "cancel"
            orderObj.is_replaceable = "0"
            orderObj.is_cancel = "1"
            orderObj.order_status = "Cancel"
            orderObj.is_pickup = "1"
            orderObj.is_order_hold = "0"
            orderObj.order_cancel_date = new Date().toISOString();
            if (order.sap_status == "Pending") {
                orderObj.sap_status = "Cancel"
                orderObj.delhivery_status = "Cancel"
                orderObj.middleware_status = "CANCELLED"
            }
            if (order.middleware_status == "READY TO SHIP") {
                orderObj.middleware_status = "CANCELLED"
            }
            await dbConnection.orderItemSplit.update(orderObj, { where: { order_name: orderName } })

            //if this order is extended warranty then cancel the order from extended warranty service
            if (order.is_extended_warranty == "1") {
                await extendedWarrantyServiceController.cancelWarrantyOrder(orderName)
            }
            const sapObj = {
                store_id: order.store_id,
                order_id: order.shopify_order_id,
                sap_billing_number: null,
                line_item_id: order.line_item_id,
                order_name: order.order_name,
                plant_code: order.plant_code,
                is_cancel: "1"
            }

            if (order.financial_status == "paid") {
                let shopResponse = await dbConnection.shop.findOne({ where: { id: order.store_id } })
                const payId = await getPaymentId(shopResponse.dataValues.myshopify_domain, shopResponse.dataValues.token, order.shopify_order_id)
                if (payId.status) {
                    let OrderObj = {}
                    if (payId.payId) {
                        OrderObj.checkout_id = payId.payId
                    }
                    if (payId?.gateway == 'SNAPMINT') {
                        OrderObj.gateway = 'SNAPMINT'
                    }
                    if (Object.keys(orderObj).length > 0) {
                        await dbConnection.orderItemSplit.update(OrderObj, { where: { order_name: order.order_name } })
                    }
                }
            }

            if (order.sap_billing_number != null && order.sap_billing_number != "null") {
                let isSapBillNotNull = await this.isSapBillNotNull(order, flag, sapObj)
            } else {
                console.log("inside Else part=============")
                console.log("waybill no check ===>>", order?.waybill_number)
                let isSapBillNull = await this.isSapBillNull(order, sapObj)
            }
        } else {
            console.log("else part=====>")
        }
    } catch (err) {
        console.log("cancel order Error====>", err)
    }
}

exports.getorderSplitDataById = (orderName) => {
    return new Promise(async (resolve, reject) => {
        try {
            let getOrderDataByName = await dbConnection.orderItemSplit.findOne({
                where: { order_name: orderName },
                // raw: true,
                // nest: true,
            })
            if (getOrderDataByName) {
                let orderCustomer = await dbConnection.orderCustomer.findOne({ where: { order_id: getOrderDataByName.dataValues.shopify_order_id } })
                let orderAddress = await dbConnection.orderAddress.findOne({ where: { order_id: getOrderDataByName.dataValues.shopify_order_id } })
                let orderData = getOrderDataByName.dataValues
                let orderCustomerArr = []
                let orderAddressArr = []
                orderCustomer ? orderCustomerArr.push(orderCustomer.dataValues) : []
                orderAddress ? orderAddressArr.push(orderAddress.dataValues) : []
                orderData.orderCustomers = orderCustomerArr
                orderData.orderAddresses = orderAddressArr
                resolve({ status: true, data: orderData })
            } else {
                resolve({ status: false })
            }
        } catch (err) {
            console.log('Error getorderSplitDataById=====>', err)
        }
    })
}

exports.getorderSplitDataByI = (orderName) => {
    return new Promise(async (resolve, reject) => {
        try {
            let getOrderDataById = await dbConnection.orderItemSplit.findAll({
                where: { order_name: orderName },
                include: [
                    {
                        model: dbConnection.orderCustomer, as: 'orderCustomers'
                    },
                    {
                        model: dbConnection.orderAddress, as: 'orderAddresses'
                    }
                ]
            })
            if (getOrderDataById) {
                resolve({ status: true, data: getOrderDataById })
            } else {
                reject({ status: false })
            }
        } catch (err) {
            console.log('Error getorderSplitDataByI=====>', err)
        }
    })
}

exports.pushOrderDelhiveryAndSap = async (item, plantCode) => {
    try {
        if (item.is_order_hold == '0') {
            const eligibility = await this.checkOrderPushEligibility(item.order_name);
            console.log('checkOrderPushEligibility ', item.order_name, eligibility)
            if (!eligibility) return false;
            const insertLog = await dbConnection.orderProcessing.create({ order_name: item.order_name });
            let processingStatus = "failed";
            const orderAddresses = item.orderAddresses.length > 0 ? item.orderAddresses[0] : null
            console.log("in push order function call====>>>>>>>>>", orderAddresses.zip_code, " ", item.sku)
            if (orderAddresses != null) {
                const checkLogisticPartnerPincode = await zippeeController.checkZippeeOrder(item.sku, orderAddresses.zip_code, plantCode);
                if (checkLogisticPartnerPincode?.status) {
                    let cfaResponse = {
                        status: false,
                        data: { plantCode: "" }
                    }
                    if (!plantCode && checkLogisticPartnerPincode?.zippee) {
                        plantCode = checkLogisticPartnerPincode?.plantCode
                    }
                    if (plantCode != null) {
                        cfaResponse.status = true;
                        cfaResponse.data.plantCode = plantCode
                    } else {
                        cfaResponse = await this.distanceMatrixApi(orderAddresses.zip_code, orderAddresses.city, item.sku, orderAddresses.address1);
                    }
                    if (cfaResponse.status) {
                        await this.localStockChange(item.sku, cfaResponse.data.plantCode)
                        await this.cfaOrderLimitManage(cfaResponse.data.plantCode, true)
                        // call sap object pass cfa plant_code
                        let sapParam = {
                            orderData: item,
                            docType: "ZWEB",
                            plantCode: cfaResponse.data.plantCode,
                            rate: 0
                        }

                        if (item.gstin != null && item.gstin != "null" && item.gstin != '' && item.is_gst_Valid == '1') {
                            let gstRes = await gstValidationController.pushToCustomerMaster(item)
                            if (gstRes.status) {
                                sapParam.CustomerNum = gstRes.data.CustomerNum
                                sapParam.Gstin = item.gstin // gstRes.data.Gstin
                            }
                        }
                        let shopResponse = await dbConnection.shop.findOne({ where: { id: item.store_id } })
                        let orderResData = await orderSyncController.getorderSplitDataByName(item.order_name)
                        let orderData = JSON.parse(JSON.stringify(orderResData.data))
                        if (item.financial_status == "paid") {
                            const payId = await getPaymentId(shopResponse.dataValues.myshopify_domain, shopResponse.dataValues.token, item.shopify_order_id)
                            let giftCardAmout = parseFloat(orderData.order_amount) * parseFloat(orderData.gift_card_value)
                            orderData.order_amount = parseFloat(orderData.order_amount) - giftCardAmout
                            if (payId.status) {
                                const rate = await fetchRateForEmi(payId.payId)
                                if (rate.status) {
                                    let discount = (parseFloat(item.order_amount) * 0.047)
                                    sapParam.rate = discount
                                }
                                payId.payId ? orderData.checkout_id = payId.payId : ""
                                payId.payId ? await dbConnection.orderItemSplit.update({ checkout_id: payId.payId }, { where: { order_name: item.order_name } }) : ""

                                if (payId?.gateway == 'SNAPMINT') {
                                    await dbConnection.orderItemSplit.update({ gateway: 'SNAPMINT' }, { where: { order_name: item.order_name } })
                                    orderData.gateway = 'SNAPMINT'
                                }
                            }
                        }
                        sapParam.orderData = orderData
                        let reqSapData = await sapController.getSapReqBody(sapParam)
                        if (reqSapData.status) {
                            const sapData = await sapController.pushOrderOnSAP(reqSapData.data)

                            let helperObj = {
                                shopify_order_id: orderData.shopify_order_id,
                                shopify_order_name: orderData.shopify_order_name,
                                order_name: orderData.order_name,
                                platform_type: "SAP",
                                request_data: JSON.stringify(reqSapData.data),
                                response_status_code: sapData.status_code,
                                response_data: sapData.data ? JSON.stringify(sapData.data) : JSON.stringify(sapData)
                            }
                            await orderHistoryHelper.insertOrderHistory(helperObj)
                            const sapObj = {
                                store_id: item.store_id,
                                order_id: item.shopify_order_id,
                                sap_billing_number: null,
                                line_item_id: item.line_item_id,
                                order_name: item.order_name,
                                plant_code: cfaResponse.data.plantCode
                            }
                            const sapDataRes = await dbConnection.sapLog.findOne({ where: { order_name: item.order_name } })
                            if (sapData.status) {
                                processingStatus = "success";
                                // sap log data insert in database
                                sapObj.sap_delivery_number = sapData.data.SAP_DLY_NUM
                                sapObj.sap_order_number = sapData.data.SAP_SO_NUM
                                sapObj.shopify_order_name = sapData.data.SHOPIFY_NUM
                                sapObj.response_message = sapData?.data?.MESSAGE?.length > 0 ? sapData.data.MESSAGE[0].MESSAGE : "Didn't get any response from SAP"
                                const orderData = {
                                    sap_order_number: sapData.data.SAP_SO_NUM,
                                    sap_delivery_number: sapData.data.SAP_DLY_NUM,
                                    sap_status: "Pushed",
                                    order_status: "Completed",
                                    is_pickup: "1",
                                    middleware_status: "PROCESSING",
                                    plant_code: sapParam.plantCode
                                }
                                if (sapDataRes) {
                                    await dbConnection.sapLog.update(sapObj, { where: { order_name: item.order_name } })
                                } else {
                                    await dbConnection.sapLog.create(sapObj)
                                }
                                await dbConnection.orderItemSplit.update(orderData, { where: { order_name: item.order_name } })
                                item.plant_code = sapParam.plantCode
                                //call function to push order on delhivery/Zippee
                                if (checkLogisticPartnerPincode?.zippee) {
                                    await zippeeController.pushOrderOnZippee(item)
                                } else {
                                    this.pushOrderOnDelhivery(item)
                                }
                            } else {
                                await this.localStockPlus(item.sku, sapParam.plantCode)
                                await this.cfaOrderLimitManage(sapParam.plantCode)
                                await dbConnection.orderItemSplit.update({ sap_status: "Failed", middleware_status: "FAILED", failed_reason: sapData?.data?.MESSAGE?.length > 0 ? sapData.data.MESSAGE[0].MESSAGE : "Didn't get any response from SAP" }, { where: { order_name: item.order_name } })
                                sapObj.response_message = sapData?.data?.MESSAGE?.length > 0 ? sapData.data.MESSAGE[0].MESSAGE : "Didn't get any response from SAP"
                                sapDataRes ? await dbConnection.sapLog.update(sapObj, { where: { order_name: item.order_name } }) : await dbConnection.sapLog.create(sapObj)
                            }
                        } else {
                            await this.localStockPlus(item.sku, sapParam.plantCode)
                            await this.cfaOrderLimitManage(sapParam.plantCode)
                            console.log('sap obj errr')
                        }
                        await dbConnection.orderProcessing.update({ status: processingStatus }, { where: { id: insertLog.id } });
                    } else {
                        console.log('out of stock')
                        await dbConnection.orderItemSplit.update({ order_status: 'Out Of Stock', middleware_status: 'OUT OF STOCK' }, { where: { order_name: item.order_name } })
                    }

                } else {
                    console.log('pin code not available')
                    await dbConnection.orderItemSplit.update({ order_status: 'PincodeNotAvailable', middleware_status: 'FAILED', failed_reason: 'Pin code not serviceable' }, { where: { order_name: item.order_name } })
                }
            } else {
                console.log('address not found')
            }
        } else {
            console.log("order is on hold", item?.order_name);
        }
        return
    } catch (err) {
        console.log('errrr=======>', err)
        logger.error("delhivery controller catch" + err)
        return
    }
}

exports.pushOrderOnDelhivery = async (item) => {
    try {
        let skuShipment = await dbConnection.mpsShipment.findOne({ where: { sku: item.sku } })
        const shopResponse = await dbConnection.shop.findOne({ where: { id: item.store_id } })

        const shipObj = {
            shopResponse: shopResponse.dataValues,
            orderId: item.shopify_order_id,
            plantCode: item.plant_code,
            orderData: item
        }

        if (skuShipment) {
            await mpsOrderShipment(shipObj)
        } else {
            await singleShipment(shipObj)
        }
        return
    } catch (err) {
        console.log('err==>', err)
        return
    }
}

let mpsOrderShipment = (mpsObj) => {
    return new Promise(async (resolve, reject) => {
        try {
            let { orderData, orderId, shopResponse } = mpsObj
            let reqDataRes = await getDelhiveryObj(mpsObj, shipmentType = "mps", type = null)
            const delhiveryRes = await delhiveryRequest(orderData, reqDataRes.data, platformType = "DELHIVERY-Mps")
            const package = delhiveryRes.data.packages.length > 0 ? delhiveryRes.data.packages[0] : null
            let delhiveryObj = {
                store_id: orderData.store_id,
                order_id: orderId,
                status: package ? package.status : "",
                reference_number: package ? package.refnum : "",
                waybill_number: reqDataRes.waybillNumbers.waybillNumbers,
                response_message: delhiveryRes.data.rmk ? delhiveryRes.data.rmk : null,
                packing_slip_url: "",
                line_item_id: orderData.line_item_id,
                order_name: orderData.order_name,
                cfa_name: reqDataRes.cfaName
            }
            if (package && package.remarks.length > 0) {
                delhiveryObj.response_message = package.remarks[0]
            }
            await upsertDelhiveryLog(delhiveryObj, orderData.order_name, isReturn = false)
            // waybill number then update into order_split table with Completed status
            if (package && package.status != 'Fail') {
                let clickpostObj = clickpostReqObject(reqDataRes.data, orderData, reqDataRes.waybillNumbers.waybillNumbers, 'forward')
                registerShipmentForTracking(clickpostObj)
                let packingSlipUrl = await generatePackingSlip(reqDataRes.waybillNumbers.waybillNumbers, orderData.order_name)
                const paramObj = {
                    line_item_id: orderData.line_item_id,
                    price: orderData.order_amount,
                    orderId: orderId,
                    orderRef: orderData.id,
                    storeId: orderData.store_id,
                    status: "confirmed",
                    waybill: reqDataRes.waybillNumbers.waybillNumbers,
                    shopResponse: shopResponse,
                    logistics_partner: "Delhivery"
                }
                delhiveryObj.packing_slip_url = packingSlipUrl
                delhiveryObj.status = package.status
                delhiveryObj.response_message = null
                await upsertDelhiveryLog(delhiveryObj, orderData.order_name, isReturn = false)
                await dbConnection.orderItemSplit.update({ delhivery_status: "Pushed", middleware_status: "PROCESSING", shipment_status: 'manifested', waybill_number: reqDataRes.waybillNumbers.waybillNumbers }, { where: { order_name: orderData.order_name } })
                setTimeout(async () => {
                    await this.createFulfillment(paramObj);
                }, 15000);

                //call for extened warranties
                await extendedWarrantyServiceController.createOrderOnSAPforWarranty(orderData.order_name)
                resolve({ status: true, data: delhiveryObj })
            } else {
                console.log('failed')
                await dbConnection.orderItemSplit.update({ delhivery_status: "Failed", middleware_status: "FAILED", failed_reason: delhiveryRes?.data?.rmk }, { where: { order_name: orderData.order_name } })
                resolve({ status: false })
            }
        } catch (err) {
            console.log('Error mpsOrderShipment=====>', err)
        }
    })
}

exports.fetchFulfillmentOrder = async (orderId, lineItemId, shopResponse) => {
    try {
        let fullfillObj = new FulfillmentOrder(shopResponse.myshopify_domain, shopResponse.token);
        let { status, data } = await fullfillObj.get(orderId)
        if (!status) throw new Error(data);
        let response;
        for (resData of data) {
            if (resData.status != 'closed' && resData.status != 'cancelled' && resData.status != 'on_hold' && resData.assigned_location_id !== "68148330553" && resData.assigned_location_id !== "68155342905") {
                let lineItemData = resData.line_items.find(({ line_item_id }) => line_item_id == lineItemId)
                response = lineItemData ? lineItemData : null
            }
        }
        if (!response) throw new Error('No fullfill Order found')
        return response
    } catch (error) {
        console.log("Error fetchFulfillmentOrder--", error);
        return null;
    }
}

exports.createFulfillment = async (paramObj) => {
    return new Promise(async (resolve, reject) => {
        try {
            let { line_item_id, order_amount, orderId, orderRef, storeId, status, waybill, shopResponse, logistics_partner } = paramObj
            let fulfillOrderData = await this.fetchFulfillmentOrder(orderId, line_item_id, shopResponse);
            let fulfillmentData = await dbConnection.fulfillmentService.findOne({ where: { service_name: "SYMPHONY" } })
            let waybillNumber = waybill?.split(",");

            let orderExist = await dbConnection.fulfillmentItem.findOne({ where: { order_id: orderRef } })
            if (orderExist) {
                let updateFulfillBody = { "tracking_info": { "number": waybillNumber.length > 1 ? waybillNumber : waybillNumber[0], "company": logistics_partner } }
                let objFulfillment = new Fulfillment(shopResponse.myshopify_domain, shopResponse.token, orderId)
                // let fulfillmentRes = waybillNumber.length > 1 ? await objFulfillment.updateByGraph(orderExist.dataValues.fulfillment_id, updateFulfillBody) : await objFulfillment.update(orderExist.dataValues.fulfillment_id, updateFulfillBody)
                let fulfillmentRes = await objFulfillment.updateByGraph(orderExist.dataValues.fulfillment_id, updateFulfillBody)
                let fulfillmentObj = {
                    fulfillment_id: fulfillmentRes.data.id,
                    fulfillment_status: fulfillmentRes.data.line_items[0].fulfillment_status,
                    tracking_company: fulfillmentRes.data.tracking_company,
                    tracking_url: fulfillmentRes.data.tracking_url,
                    location_id: fulfillmentRes.data.location_id,
                    tracking_number: waybill,
                    fulfillment_created_at: fulfillmentRes.data.created_at,
                    fulfillment_updated_at: fulfillmentRes.data.updated_at
                }
                await dbConnection.fulfillmentItem.update(fulfillmentObj, { where: { order_id: orderRef } })
                resolve()
            } else if (fulfillOrderData) {
                let fulfillData = {
                    "location_id": fulfillmentData ? fulfillmentData.dataValues.location_id : "",
                    "shipment_status": status,
                    "tracking_info": { "number": waybillNumber[0], "company": logistics_partner },
                    "line_items_by_fulfillment_order": [
                        {
                            "fulfillment_order_id": fulfillOrderData.fulfillment_order_id,
                            "fulfillment_order_line_items": [
                                {
                                    "id": fulfillOrderData.id,
                                    "quantity": 1
                                }
                            ]
                        }
                    ],
                }
                let objFulfillment = new Fulfillment(shopResponse.myshopify_domain, shopResponse.token, orderId)
                // let fulfillmentRes = waybillNumber.length > 1 ? await objFulfillment.createByGraph(fulfillOrderData.fulfillment_order_id, fulfillOrderData.id, waybillNumber) : await objFulfillment.create(orderId, fulfillData)
                let fulfillmentRes = await objFulfillment.createByGraph(fulfillOrderData.fulfillment_order_id, fulfillOrderData.id, waybillNumber)
                if (fulfillmentRes.data) {
                    let fulfillmentObj = {
                        store_id: shopResponse.id,
                        order_id: orderRef,
                        fulfillment_id: fulfillmentRes.data.id,
                        fulfillment_status: fulfillmentRes.data.line_items[0].fulfillment_status,
                        tracking_company: logistics_partner,
                        tracking_url: fulfillmentRes.data.tracking_url,
                        location_id: fulfillmentRes.data.location_id,
                        tracking_number: waybill,
                        line_item_id: line_item_id,
                        price: order_amount,
                        fulfillment_created_at: fulfillmentRes.data.created_at,
                        fulfillment_updated_at: fulfillmentRes.data.updated_at

                    }
                    const paramObj = {
                        orderId: orderId,
                        fulfillmentId: fulfillmentRes.data.id,
                        shopResponse: shopResponse,
                        shipmentStatus: status,
                        logistics_partner: logistics_partner,
                    }
                    await dbConnection.fulfillmentItem.create(fulfillmentObj)
                    await dbConnection.orderItemSplit.update({ fulfillment_status: "fulfilled" }, { where: { id: orderRef } });
                    await this.createFulfillmentEvent(paramObj)

                    resolve()
                } else {
                    console.log('fulfillment not created')
                    await dbConnection.orderItemSplit.update({ fulfillment_status: "failed" }, { where: { id: orderRef } });
                    resolve()
                }
            } else {
                console.log('not create fullfiledment')
                resolve()
            }
        } catch (err) {
            console.log("Error==>", err)
            resolve()
        }
    })
}

exports.createFulfillmentEvent = async (paramObj) => {
    return new Promise(async (resolve, reject) => {
        try {
            let { orderId, fulfillmentId, shopResponse, shipmentStatus } = paramObj
            const fulfillData = {
                "status": shipmentStatus
            }
            let objFulfillmentEvent = new FulfillmentEvent(shopResponse.myshopify_domain, shopResponse.token, orderId, fulfillmentId)
            let fulfillmentEventRes = await objFulfillmentEvent.create(fulfillData)
            if (fulfillmentEventRes) {
                resolve()
            } else {
                if (fulfillmentEventRes.status == 422 || fulfillmentEventRes.status == "422")
                    console.log("already lineitem is had an event")
            }
        } catch (err) {
            console.log("Error createFulfillmentEvent", err)
            resolve(err)
        }
    })
}

let getDelhiveryObj = (paramObj, shipmentType = null, type = null) => {
    return new Promise(async (resolve, reject) => {
        try {
            let { orderData } = paramObj
            const cfaResponse = await dbConnection.cfaPlantLocation.findOne({ where: { plant_code: orderData.plant_code } })
            let cfaName = cfaResponse ? cfaResponse.dataValues.plant_name : ""
            let shipmentArr = []

            let delhivery = {
                pickup_location: {
                    "name": process.env.NODE_ENV == "production" ? cfaName : CONFIG.delhivery.CFA_NAME,
                    // "name": CONFIG.delhivery.CFA_NAME, // client-warehouse-name-as-registered-with-delhivery ->  mandatory
                }
            }
            let dataObj = {}
            if (shipmentType == "mps") {
                let waybillNumbers = {}
                waybillNumbers = await getWaybillNumber()

                // latest updated Delhivery request body
                for (let i = 0; i < 2; i++) {
                    dataObj = await delhiveryObject(orderData, cfaResponse, cfaName, type)
                    dataObj = {
                        ...dataObj,
                        "mps_amount": orderData.order_status == "Replacement" ? "0" : orderData.gateway != "Cash on Delivery (COD)" ? "0" : orderData.discount != null && orderData.discount != "null" ? orderData.order_amount - parseFloat(orderData.discount) : orderData.order_amount,
                        "waybill": waybillNumbers.waybill[i],
                        "shipment_type": "MPS",
                        "mps_children": "2",
                        "master_id": waybillNumbers.waybill[0]
                    }
                    shipmentArr.push(dataObj)
                }
                delhivery.shipments = shipmentArr
                resolve({ data: delhivery, waybillNumbers: waybillNumbers, cfaName: cfaName })
            } else {
                dataObj = await delhiveryObject(orderData, cfaResponse, cfaName, type)
                shipmentArr.push(dataObj)
                delhivery.shipments = shipmentArr
                resolve({ data: delhivery, cfaName: cfaName })
            }
        } catch (err) {
            console.log('Error getDelhiveryObj=====>', err)
        }
    })
}

//create delhiveryObject
let delhiveryObject = async (orderData, cfaResponse, cfaName, type) => {
    try {

        const orderAddress = orderData.orderAddresses.length > 0 ? orderData.orderAddresses : []
        const add1 = orderAddress.length > 0 ? orderAddress[0].address1 : ""
        const add2 = orderAddress.length > 0 ? orderAddress[0].address2 != null && orderAddress[0].address2 != "null" ? orderAddress[0].address2 : "" : ""

        dataObj = {
            "return_name": process.env.NODE_ENV == "production" ? cfaName : CONFIG.delhivery.CFA_NAME,
            "country": orderAddress.length > 0 ? orderAddress[0].country : "",
            "order": orderData.order_name,
            "phone": orderAddress.length > 0 ? orderAddress[0].phone.replaceAll(' ', '') : "", // mandatory
            "products_desc": orderData.product_title.replace(/[^a-zA-Z0-9,\- ]/g, ''), // Description of product which is used in shipping label
            "cod_amount": "0",
            "name": orderAddress.length > 0 ? orderAddress[0].first_name.replace(/[^a-zA-Z0-9,\- ]/g, '') + " " + orderAddress[0]?.last_name?.replace(/[^a-zA-Z0-9,\- ]/g, '') : "",
            "order_date": orderData.order_created_at,
            "total_amount": orderData.discount != null && orderData.discount != "null" ? orderData.order_amount - parseFloat(orderData.discount) : orderData.order_amount,
            "add": add1.replace(/[^a-zA-Z0-9,\- ]/g, '') + " " + add2.replace(/[^a-zA-Z0-9,\- ]/g, ''), //mandatory
            "pin": orderAddress.length > 0 ? orderAddress[0].zip_code : "", //mandatory,
            "quantity": "1",
            "fragile_shipment": "true",
            "payment_mode": "Prepaid",
            "state": orderAddress.length > 0 ? orderAddress[0].province.replace(/&/g, 'and') : "",
            "city": orderAddress.length > 0 ? orderAddress[0].city : "",
            "client": CONFIG.delhivery.CFA_CLIENT
        }

        if (type == "Return" || orderData.is_return == "1") {
            dataObj = {
                ...dataObj,
                "payment_mode": "Pickup",
                "order": orderData.return_order_name,
                "return_add": cfaResponse ? (cfaResponse.dataValues.address != "" || cfaResponse.dataValues.address != null) ? cfaResponse.dataValues.address.replace(/[^a-zA-Z0-9,\- ]/g, '') : "" : "", //(Mandatory)
                "return_city": cfaResponse ? cfaResponse.dataValues.city : "", //(Mandatory)
                "return_country": cfaResponse ? cfaResponse.dataValues.country : "", //(Mandatory)
                "return_phone": cfaResponse ? (cfaResponse.dataValues.phone != "" || cfaResponse.dataValues.phone != null) ? cfaResponse.dataValues.phone.replaceAll(' ', '') : "" : "", //(Mandatory)
                "return_pin": cfaResponse ? cfaResponse.dataValues.pincode : "", //(Mandatory)
                "return_state": cfaResponse ? cfaResponse.dataValues.state : "", //(Mandatory)
            }
        }
        return dataObj
    } catch (err) {
        console.log('errrr=====>', err)
        return
    }
}

//single shipment create
let singleShipment = (paramObj) => {
    return new Promise(async (resolve, reject) => {
        try {
            let { shopResponse, orderId, orderData } = paramObj
            let reqDataRes = await getDelhiveryObj(paramObj, shipmentType = "single", type = null)
            const delhiveryRes = await delhiveryRequest(orderData, reqDataRes.data, platformType = "DELHIVERY-Single")
            const package = delhiveryRes.data.packages.length > 0 ? delhiveryRes.data.packages[0] : null
            let delhiveryObj = {
                store_id: orderData.store_id,
                order_id: orderId,
                status: package ? package.status : "",
                reference_number: package ? package.refnum : "",
                waybill_number: package && package.waybill ? package.waybill : "",
                response_message: delhiveryRes.data.rmk ? delhiveryRes.data.rmk : null,
                packing_slip_url: "",
                line_item_id: orderData.line_item_id,
                order_name: orderData.order_name,
                cfa_name: reqDataRes.cfaName
            }
            if (package && package.remarks.length > 0) {
                delhiveryObj.response_message = package.remarks[0]
            }
            await upsertDelhiveryLog(delhiveryObj, orderData.order_name, isReturn = false)
            if (package && package.waybill) {
                let clickpostObj = clickpostReqObject(reqDataRes.data, orderData, package.waybill, 'forward')
                registerShipmentForTracking(clickpostObj)
                let packingSlipUrl = await generatePackingSlip(package.waybill, orderData.order_name)
                const paramObj = {
                    line_item_id: orderData.line_item_id,
                    price: orderData.order_amount,
                    orderId: orderId,
                    orderRef: orderData.id,
                    shopResponse: shopResponse,
                    storeId: orderData.store_id,
                    status: "confirmed",
                    waybill: package.waybill,
                    logistics_partner: "Delhivery"
                }
                delhiveryObj.packing_slip_url = packingSlipUrl
                delhiveryObj.status = package.status
                delhiveryObj.response_message = null
                await upsertDelhiveryLog(delhiveryObj, orderData.order_name, isReturn = false)
                await dbConnection.orderItemSplit.update({ delhivery_status: "Pushed", middleware_status: "PROCESSING", shipment_status: 'manifested', waybill_number: package.waybill }, { where: { order_name: orderData.order_name } })
                setTimeout(async () => {
                    await this.createFulfillment(paramObj);
                }, 15000);

                //call for extened warranties
                await extendedWarrantyServiceController.createOrderOnSAPforWarranty(orderData.order_name)
                resolve({ status: true, data: delhiveryObj })
            } else {
                await dbConnection.orderItemSplit.update({ delhivery_status: "Failed", middleware_status: "FAILED", failed_reason: delhiveryObj.response_message }, { where: { order_name: orderData.order_name } })
                resolve({ status: false })
            }
        } catch (err) {
            console.log('Error singleShipment=====>', err)
        }
    })
}

// SAP delivery API call
exports.sapDelivery = (orderName, reason) => {
    return new Promise(async (resolve, reject) => {
        try {
            console.log("order namee===>>>", orderName)
            const orderData = await dbConnection.orderItemSplit.findOne({ where: { order_name: orderName } })
            if (orderData) {
                let deliveryObj = []
                let whereObj
                if (orderData.dataValues.order_status == "Returned") {
                    let checkData = await dbConnection.delhiveryLog.findOne({ where: { order_name: orderName, is_return: "1", return_order_name: orderData.dataValues.return_order_name } })
                    if (checkData) {
                        whereObj = {
                            order_name: orderName,
                            is_return: "1",
                            return_order_name: orderData.dataValues.return_order_name
                        }
                    } else {
                        whereObj = {
                            order_name: orderName,
                            is_return: "1"
                        }
                    }
                } else {
                    whereObj = {
                        order_name: orderName,
                    }
                }
                let packingSlipUrl = '';
                let packingSlipData = await dbConnection.delhiveryLog.findOne({ where: whereObj })
                packingSlipData = JSON.parse(JSON.stringify(packingSlipData))
                if (packingSlipData && packingSlipData.packing_slip_url != null && packingSlipData.packing_slip_url != "") {
                    packingSlipUrl = packingSlipData.packing_slip_url
                } else {
                    if (orderData.dataValues.logistics_partner === "Zippee") {
                        packingSlipUrl = await zippeeController.zippeePackingSlip(orderData.dataValues.waybill_number, orderName)
                    } else {
                        packingSlipUrl = await generatePackingSlip(orderData.dataValues.waybill_number, orderName)
                    }
                    await dbConnection.delhiveryLog.update({ packing_slip_url: packingSlipUrl }, { where: { waybill_number: orderData.dataValues.waybill_number } })
                }
                if (!packingSlipUrl) {
                    return resolve({ status: false })
                }

                var obj = {
                    "SHOPIFY_NUM": orderData.dataValues.shopify_order_name,
                    "SAP_SO_NUM": orderData.dataValues.sap_order_number,
                    "SAP_DLVY_NUM": orderData.dataValues.sap_delivery_number,
                    "AWB_NUM": orderData.dataValues.waybill_number,
                    "DEL_REASON": reason ? reason : "",
                    "SHIPPING_URL": packingSlipUrl ? packingSlipUrl : ''
                }

                deliveryObj.push(obj)

                const deliveryRes = await sapController.getSapBillingNumber(deliveryObj, orderData.dataValues.order_status, orderData.dataValues.order_name)
                let helperObj = {
                    shopify_order_id: orderData.dataValues.shopify_order_id,
                    shopify_order_name: orderData.dataValues.shopify_order_name,
                    order_name: orderData.dataValues.order_name,
                    platform_type: orderData.dataValues.order_status == "Cancel" ? "SAP-Cancel-sapBillingNumber" : "SAP-sapBillingNumber",
                    request_data: JSON.stringify(deliveryObj),
                    response_status_code: deliveryRes.status_code,
                    response_data: deliveryRes?.data ? (typeof deliveryRes.data === 'object' ? JSON.stringify(deliveryRes.data) : deliveryRes.data) : JSON.stringify(deliveryRes)
                }

                await orderHistoryHelper.insertOrderHistory(helperObj)
                if (deliveryRes.status) {
                    await this.updateSapLogdata(orderData, deliveryRes, orderName)
                } else {
                    if (deliveryRes.isCancel) {
                        let middlewareStatus = "REFUND PENDING"
                        if (orderData.shipment_status == "in_transit" || orderData.shipment_status == "out_for_delivery") {
                            middlewareStatus = "RTO IN TRANSIT"
                        }
                        let orderObj = {
                            sap_status: "Cancel",
                            middleware_status: middlewareStatus,
                            order_cancel_date: orderData.order_cancel_date != null ? orderData.order_cancel_date : new Date().toISOString()
                        }
                        await dbConnection.orderItemSplit.update(orderObj, { where: { order_name: orderName } })
                        resolve({ status: true })
                    } else {
                        await dbConnection.sapLog.update({ response_message: "Billing Number is not Generated" }, { where: { order_name: orderName } })
                        resolve({ status: false })
                    }
                }
                resolve({ status: true })
            }
        } catch (err) {
            console.log('Error sapDelivery=====>', err)
        }
    })
}

exports.updateSapLogdata = async (orderData, deliveryRes, orderName) => {
    return new Promise(async (resolve, reject) => {
        try {
            let orderObj = {
                product_serial_number: deliveryRes.data.SerialNum,
                sap_billing_number: deliveryRes.data.SapBillNum,
                sap_status: "Invoiced",
                is_pickup: "1",
                sap_invoice_date: new Date().toISOString()
            }
            if (orderData.dataValues.is_cancel == "1") {
                orderObj.sap_status = "Cancel"
                orderObj.delhivery_status = "Cancel"
                orderObj.middleware_status = "REFUND PENDING"
                orderObj.order_status = "Cancel"
                if (orderData.financial_status == "paid") {
                    await refundController.createCNOrderRefund(orderName)
                }
                await dbConnection.sapLog.update({ sap_billing_number: orderObj.sap_billing_number }, { where: { order_name: orderName, is_cancel: "1" } })
            } else if (orderData.dataValues.is_return == '1') {
                let saplog = await dbConnection.sapLog.findOne({ where: { is_return: "1", return_order_name: orderData.dataValues.return_order_name } })
                orderObj.middleware_status = "REFUND PENDING"
                let sapObj = {
                    store_id: orderData.dataValues.store_id,
                    sap_billing_number: deliveryRes.data.SapBillNum ? deliveryRes.data.SapBillNum : null,
                    order_id: orderData.dataValues.shopify_order_id,
                    order_name: orderData.dataValues.order_name,
                    plant_code: orderData.dataValues.plant_code,
                    line_item_id: orderData.dataValues.line_item_id,
                    shopify_order_name: deliveryRes.data.ShopifyNum,
                    response_message: "Billing Number is Generated",
                    cn_created_date: new Date().toISOString(),
                    return_order_name: orderData.dataValues.return_order_name,
                    is_return: "1"
                }
                if (saplog) {
                    await dbConnection.sapLog.update(sapObj, { where: { order_name: orderName, is_return: "1", return_order_name: orderData.dataValues.return_order_name } })
                } else {
                    await dbConnection.sapLog.create(sapObj)
                }
            }
            else {
                orderObj.middleware_status = "READY TO SHIP"
                await dbConnection.sapLog.update({ sap_billing_number: orderObj.sap_billing_number }, { where: { order_name: orderName, is_cancel: "0" } })
            }
            await dbConnection.orderItemSplit.update(orderObj, { where: { order_name: orderName } })
            resolve()
        } catch (err) {
            resolve()
            console.log('Error updateSapLogdata=====>', err)
        }
    })
}
// upsert Delivery obj
let upsertDelhiveryLog = (delhiveryObj, ordreName, isReturn) => {
    return new Promise(async (resolve, reject) => {
        try {
            // delihvery log data in database
            let delhiveryDataRes
            if (isReturn) {
                delhiveryDataRes = delhiveryObj.return_order_name != null ? await dbConnection.delhiveryLog.findOne({ where: { order_name: ordreName, is_return: "1", return_order_name: delhiveryObj.return_order_name } })
                    : await dbConnection.delhiveryLog.findOne({ where: { order_name: ordreName, is_return: "1" } })
                if (delhiveryDataRes) {
                    delhiveryObj.return_order_name != null ? await dbConnection.delhiveryLog.update(delhiveryObj, { where: { order_name: ordreName, is_return: "1", return_order_name: delhiveryObj.return_order_name } })
                        : await dbConnection.delhiveryLog.update(delhiveryObj, { where: { order_name: ordreName, is_return: "1" } })
                } else {
                    await dbConnection.delhiveryLog.create(delhiveryObj)
                }
            } else {
                delhiveryDataRes = await dbConnection.delhiveryLog.findOne({ where: { order_name: ordreName } })
                if (delhiveryDataRes) {
                    await dbConnection.delhiveryLog.update(delhiveryObj, { where: { order_name: ordreName } })
                } else {
                    await dbConnection.delhiveryLog.create(delhiveryObj)
                }
            }
            resolve()
        } catch (err) {
            console.log('Error upsertDelhiveryLog=====>', err)
        }
    })
}

// update local stock change 
exports.localStockChange = (sku, pinCode) => {
    return new Promise(async (resolve, reject) => {
        try {
            const getStock = await dbConnection.cfaStockMapping.findOne({ where: { sku: sku, pincode_group: pinCode } })
            if (getStock) {
                if (parseInt(getStock.local_stock) > 0) {
                    let updateStock = parseInt(getStock.local_stock) - 1;
                    let updateLocalStock = await dbConnection.cfaStockMapping.update({ local_stock: updateStock }, { where: { sku: sku, pincode_group: pinCode } })
                    resolve()
                } else {
                    resolve({ data: "out of stock" })
                }
            } else {
                resolve({ data: "out of stock" })
            }
        } catch (err) {
            console.log('Error localStockChange=====>', err)
        }
    })
}

//local stock plus
exports.localStockPlus = (sku, pinCode) => {
    return new Promise(async (resolve, reject) => {
        try {
            const getStock = await dbConnection.cfaStockMapping.findOne({ where: { sku: sku, pincode_group: pinCode } })
            if (getStock) {
                if (parseInt(getStock.local_stock) > 0) {
                    let updateStock = parseInt(getStock.local_stock) + 1;
                    let updateLocalStock = await dbConnection.cfaStockMapping.update({ local_stock: updateStock }, { where: { sku: sku, pincode_group: pinCode } })
                    resolve()
                } else {
                    console.log("Out of stock===>")
                    resolve({ data: "out of stock" })
                }
            } else {
                resolve({ data: "out of stock" })
            }
        } catch (err) {
            console.log('Error localStockPlus=====>', err)
        }
    })
}


exports.cfaOrderLimitManage = (plantCode, flag = false) => {
    return new Promise(async (resolve, reject) => {
        try {
            const getCfaPlan = await dbConnection.cfaPlantLocation.findOne({
                where: {
                    is_active: 1,
                    plant_code: plantCode,
                    order_limit: { [Op.gt]: 0 },
                },
            });
            let updateOrderLimit
            if (getCfaPlan) {
                if (flag) {
                    updateOrderLimit = parseInt(getCfaPlan.total_order) + 1;
                } else {
                    updateOrderLimit = parseInt(getCfaPlan.total_order) - 1;
                }
                const updateLimit = await dbConnection.cfaPlantLocation.update(
                    { total_order: updateOrderLimit },
                    {
                        where: {
                            is_active: 1,
                            plant_code: plantCode,
                            order_limit: { [Op.gt]: 0 },
                        },
                    }
                );
                resolve();
            }
            resolve();
        } catch (err) {
            console.log("Error cfaOrderLimitManage=====>", err);
        }
    });
};

let generatePackingSlip = async (wayBillNum, orderName) => {
    try {
        // let invoicePdf = "https://slipinvoice.s3.ap-south-1.amazonaws.com/invoiceFiles/4979510001643.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAZI5PGVP2G6VZHHVM%2F20211223%2Fap-south-1%2Fs3%2Faws4_request&X-Amz-Date=20211223T035205Z&X-Amz-Expires=900&X-Amz-Signature=db382fe8244a6a9d4a9b3c38d5269fba63baa3195c8d1a4680fbc001462a698c&X-Amz-SignedHeaders=host"
        let invoicePdf = ''
        let merger = new pdfMerger();

        let apiOptions = {
            'headers': {
                'Authorization': 'Token ' + CONFIG.delhivery.token
            }
        }
        let response = await axios.get(CONFIG.delhivery.billing_url + `?wbns=${wayBillNum}`, apiOptions)
        let data = response.data
        if (data.packages.length > 1) {
            let filePath = path.resolve(__dirname, '../template/multiInvoice.html')
            let packageTypeA = "Master Package"
            let packageTypeB = "Child Package"
            let wbn = wayBillNum.split(",")
            let pdfPath = await packingSlip(data.packages[0], wbn[0], orderName, filePath, packageTypeA)
            let otherPdfPath = await packingSlip(data.packages[1], wbn[1], orderName, filePath, packageTypeB)
            await merger.add(pdfPath);
            await merger.add(otherPdfPath)
            let mergePath = path.resolve(__dirname, `../uploads/merged_${wbn[0]}_${wbn[1]}.pdf`);
            await merger.save(mergePath);
            invoicePdf = await this.invoiceUpload(mergePath, wayBillNum)
        }
        else {
            let filePath = path.resolve(__dirname, '../template/invoice.html')
            let pdfPath = await packingSlip(data.packages[0], wayBillNum, orderName, filePath)
            invoicePdf = await this.invoiceUpload(pdfPath, wayBillNum)
        }
        return invoicePdf
    } catch (err) {
        console.log('eror pdf=====>', err)
        return
    }
}

let packingSlip = async (package, i, orderName, filePath, packageName) => {
    try {
        let html = await fs.readFileSync(filePath, "utf8");
        orderName = orderName.replace('-R', '')
        let tableData = await dbConnection.orderItemSplit.findOne({ where: { order_name: orderName } })
        tableData = JSON.parse(JSON.stringify(tableData))
        let price = tableData.discount != null && tableData.discount != "null" ? tableData.order_amount - parseFloat(tableData.discount) : tableData.order_amount
        let newPrice = price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")
        let barcodeUrl = await uploadImage.imageUpload(package.barcode)
        let orderIdBarcodeUrl = await uploadImage.imageUpload(package.oid_barcode)
        let cfaData = await dbConnection.cfaPlantLocation.findOne({ where: { plant_code: tableData.plant_code } })
        cfaData = JSON.parse(JSON.stringify(cfaData))

        let users = [{
            packageType: packageName,
            pin: package.pin,
            sortCode: package.sort_code,
            product: tableData.product_title,
            sellerName: cfaData.plant_name,
            sellerAdd: cfaData.address,
            returnAdd: cfaData.address,
            name: package.name,
            address: package.address,
            payment: package.pt,
            destination: package.destination,
            price: newPrice,
            barcode: barcodeUrl,
            orderIdBarcodeUrl: orderIdBarcodeUrl,
            waybillNum: package.mwn
        }]

        let options = {
            format: "A4",
            orientation: "portrait"
        }
        let pdfPath = path.resolve(__dirname, `../uploads/output-${i}.pdf`)
        let document = {
            html: html,
            data: {
                users: users,
            },
            path: pdfPath,
            type: "",
        };
        const pdfCreate = await pdf.create(document, options);
        return pdfPath
    } catch (err) {
        console.log('errrr=====>', err)
        return
    }
}

exports.invoiceUpload = async (filePath, wayBill) => {
    try {
        console.log("invoice upload")
        const fileContent = fs.readFileSync(filePath);
        var data = {
            Bucket: CONFIG.aws.bucketName,
            Key: "invoiceFiles/" + wayBill + ".pdf",
            Body: fileContent,
            ContentEncoding: 'base64',
            ACL: 'public-read',
            ContentType: 'application/pdf'
        };
        console.log("data===>", data)

        const awsData = await s3.upload(data).promise();
        console.log(awsData, "awsData")
        let invoiceUrl = awsData.Location
        console.log(invoiceUrl, "invoiceUrl")
        fs.readdir(path.resolve(__dirname, '../uploads'), (err, files) => {
            if (err) throw err;
        });
        return invoiceUrl
    } catch (err) {
        console.log('err0rrrr', err)
        return
    }
}

exports.pushCustomerMaster = async (req, res) => {
    try {
        const { orderName, gstn } = req.body
        let orderData = await this.getorderSplitDataById(orderName)
        let gstRes = await gstValidationController.pushToCustomerMasterByManual(orderData.data, gstn)
        res.status(200).send(orderData.data)
    } catch (err) {
        console.log('Error pushCustomerMaster=====>', err)
    }
}

exports.distanceMatrixApi = (pincode, city, sku, addressName) => {
    return new Promise(async (resolve) => {
        let address = addressName.replace(/[^a-zA-Z0-9,\- ]/g, '');
        let pincodeArr = [];

        let activeCfa = await dbConnection.cfaPlantLocation.findAll({
            where: {
                is_active: 1,
                [Op.or]: [
                    { order_limit: { [Op.gt]: dbConnection.sequelize.col('total_order') } },
                    { order_limit: null }
                ]
            },
            attributes: ['plant_code']
        });
        let activePlantCodes = activeCfa.map(plan => plan.plant_code);
        let cfaStockResponse = await dbConnection.cfaStockMapping.findAll({ where: { sku: sku, is_active: 1, pincode_group: { [Op.in]: activePlantCodes } } });

        if (cfaStockResponse && cfaStockResponse.length > 0) {
            for (let i = 0; i < cfaStockResponse.length; i++) {
                if (cfaStockResponse[i].dataValues.local_stock != null && cfaStockResponse[i].dataValues.local_stock != 0 && cfaStockResponse[i].dataValues.local_stock != '') {
                    let pincodeGroup = cfaStockResponse[i].dataValues.pincode_group;
                    let cfaPlantResponse = await dbConnection.cfaPlantLocation.findOne({ where: { plant_code: pincodeGroup } });
                    if (cfaPlantResponse) {
                        pincodeArr.push(cfaPlantResponse.dataValues.pincode + " " + cfaPlantResponse.dataValues.city);
                    }
                }
            }
            if (pincodeArr.length > 1) {
                let originPincodes = pincodeArr.toString().replaceAll(',', '|');
                let destinationPincode = address + " " + pincode + " " + city;
                let distanceMatrixRes = await axios.get(CONFIG.googleDistanceMatrixApi.url + `?origins=${originPincodes}&destinations=${destinationPincode}&key=${CONFIG.googleDistanceMatrixApi.apiKey}`);
                if (distanceMatrixRes.data.destination_addresses.length > 0 && distanceMatrixRes.data.destination_addresses[0] != '' && distanceMatrixRes.data.rows[0].elements[0].status != 'ZERO_RESULTS') {
                    let minData = await distanceGoogleData(distanceMatrixRes, pincodeArr);
                    minData.status ? resolve({ status: true, data: minData.data }) : resolve({ status: false });
                }
                else {
                    let destinationPincode = pincode + " " + city
                    let distanceMatrixRes = await axios.get(CONFIG.googleDistanceMatrixApi.url + `?origins=${originPincodes}&destinations=${destinationPincode}&key=${CONFIG.googleDistanceMatrixApi.apiKey}`);
                    if (distanceMatrixRes.data.destination_addresses.length > 0) {
                        let minData = await distanceGoogleData(distanceMatrixRes, pincodeArr);
                        console.log("Min Data22---", minData);
                        minData.status ? resolve({ status: true, data: minData.data }) : resolve({ status: false });
                    } else {
                        console.log("REQUEST_DENIED---")
                        resolve({ status: false })
                    }
                }
            } else if (pincodeArr.length == '1' || pincodeArr.length == 1) {
                let plantCodeValue = await dbConnection.cfaPlantLocation.findOne({ where: { pincode: pincodeArr[0].split(' ')[0] } });
                resolve({ status: true, data: { plantCode: plantCodeValue.dataValues.plant_code } })
            }
            else {
                console.log("pincodeArr.length==>", pincodeArr.length, "------", JSON.stringify(pincodeArr))
                console.log("NO DATA FOUND IN CFA PLANT LOCATION TABLE---")
                resolve({ status: false })
            }
        } else {
            console.log("In cfaStockMapping no matching sku found----")
            resolve({ status: false })
        }
    })
}

let distanceGoogleData = (distanceMatrixRes, pincodeArr) => {
    return new Promise(async (resolve) => {
        let distanceArr = distanceMatrixRes.data.rows, distObj, allDistArr = [];
        for (let i = 0; i < distanceArr.length; i++) {
            let data = distanceArr[i].elements;
            for (let index = 0; index < data.length; index++) {
                if (data[index].distance) {
                    distObj = {
                        pincode: pincodeArr[i],
                        value: data[index].distance.value
                    }
                    allDistArr.push(distObj)
                }
            }
        }
        if (allDistArr.length > 0) {
            let min = allDistArr[0].value, pincodeMin = allDistArr[0].pincode;
            for (let k = 0; k < allDistArr.length; k++) {
                if (allDistArr[k].value < min) {
                    min = allDistArr[k].value;
                    pincodeMin = allDistArr[k].pincode
                }
            }
            let plantCodeValue = await dbConnection.cfaPlantLocation.findOne({ where: { pincode: pincodeMin.split(' ')[0] } });
            resolve({ status: true, data: { plantCode: plantCodeValue.dataValues.plant_code } })
        } else {
            console.log('Not Found Value---')
            resolve({ status: false })
        }
    })
}

//
let delhiveryRequest = async (orderData, dataObject, platformType) => {
    try {
        const delhiveryRes = await axios.post(CONFIG.delhivery.post_url, 'format=json&data=' + JSON.stringify(dataObject), options);

        let helperObj = {
            shopify_order_id: orderData.shopify_order_id,
            shopify_order_name: orderData.shopify_order_name,
            order_name: orderData.order_name,
            platform_type: platformType,
            request_data: JSON.stringify(dataObject),
            response_status_code: delhiveryRes.status,
            response_data: delhiveryRes.data
                ? JSON.stringify(delhiveryRes.data)
                : JSON.stringify(delhiveryRes),
        };
        await orderHistoryHelper.insertOrderHistory(helperObj);

        return delhiveryRes;
    } catch (err) {
        console.log("Error delhiveryRes---", err);
        return
    }
}

let clickpostReqObject = (delhiveryData, orderData, wayBill, shipmentType) => {
    let clickpostObject = {}
    if (shipmentType == "forward") {
        clickpostObject = {
            "drop_name": delhiveryData?.shipments[0]?.name,
            "drop_phone": delhiveryData?.shipments[0]?.phone.replace('+91', ''),
            "drop_email": orderData?.orderCustomers.length > 0 ? orderData.orderCustomers[0]?.customer_email : '',
            "order_id": orderData.shopify_order_name.replace('#', ''),
            "order_date": orderData.order_created_at,
            "waybillStr": wayBill,
            "drop_add": delhiveryData?.shipments[0]?.add,
            "pick_add": "Symphony Warehouse",
            "order_name": orderData.order_name,
            "pick_name": "Symphony Warehouse",
            "pick_phone": "***********",
            "pick_email": "<EMAIL>",
            "account_code": CONFIG.clickpost.account_code,
            "courier_partner": CONFIG.clickpost.courier_partner
        }
    } else {
        clickpostObject = {
            "drop_name": "Symphony Warehouse",
            "drop_phone": "***********",
            "drop_email": "<EMAIL>",
            "order_id": orderData.shopify_order_name.replace('#', ''),
            "order_date": orderData.order_created_at,
            "waybillStr": wayBill,
            "drop_add": "Symphony Warehouse",
            "order_name": orderData.order_name,
            "pick_name": delhiveryData?.shipments[0]?.name,
            "pick_phone": delhiveryData?.shipments[0]?.phone.replace('+91', ''),
            "pick_email": orderData?.orderCustomers.length > 0 ? orderData.orderCustomers[0]?.customer_email : '',
            "account_code": CONFIG.clickpost.reverse_account_code,
            "courier_partner": CONFIG.clickpost.reverse_courier_partner,
            "pick_add": delhiveryData?.shipments[0]?.add
        }
    }
    return clickpostObject;
}

//fullfill orders by api call only
// exports.fulfillOrders = async (req, res) => {
//     try {
//         let { shop } = req.query;
//         let { orderName } = req.body;
//         let shopResponse = await dbConnection.shop.findOne({ where: { myshopify_domain: shop }, raw: true })
//         let options = { store_id: shopResponse.id };
//         if (orderName.length) {
//             options.order_name = {
//                 [Op.or]: orderName
//             }
//         }
//         let orderData = await dbConnection.orderItemSplit.findAll({
//             where: options
//         })
//         for (let i = 0; i < orderData.length; i++) {
//             try {
//                 const paramObj = {
//                     line_item_id: orderData[i].line_item_id,
//                     price: orderData[i].order_amount,
//                     orderId: orderData[i].shopify_order_id,
//                     orderRef: orderData[i].id,
//                     storeId: orderData[i].store_id,
//                     status: "confirmed",
//                     waybill: orderData[i].waybill_number,
//                     shopResponse: shopResponse,
//                 }
//                 await this.createFulfillment(paramObj)
//             } catch (error) {
//                 console.log("Error create ======", error);
//             }
//         }
//         res.status(200).send({ "data": orderData })
//     } catch (error) {
//         console.log("Error fulfillOrders---", error);
//         res.status(400).send({ "error": error })
//     }
// }

//create fulfillment event if order is already fulfilled
// exports.fulfillmentEventCreation = async (req, res) => {
//     try {
//         let { orderName } = req.body;
//         let { shop, shipmentStatus } = req.query;
//         let shopResponse = await dbConnection.shop.findOne({ where: { myshopify_domain: shop }, raw: true })
//         let itemResp = await dbConnection.orderItemSplit.findAll({ where: { order_name: orderName } })
//         for (let i = 0; i < itemResp.length; i++) {
//             try {
//                 let fullfillResp = await dbConnection.fulfillmentItem.findOne({ where: { order_id: itemResp[i].id }, raw: true })
//                 if (fullfillResp) {
//                     const paramObj = {
//                         orderId: itemResp[i].shopify_order_id,
//                         fulfillmentId: fullfillResp.fulfillment_id,
//                         shopResponse: shopResponse,
//                         shipmentStatus: shipmentStatus
//                     }
//                     this.createFulfillmentEvent(paramObj)
//                 }
//             } catch (error) {
//                 console.log("Error in loop fulfillmentEventCreation", error);
//             }
//         }
//         res.status(200).send({ data: itemResp })
//     } catch (error) {
//         console.log("Error fulfillmentEventCreation", error);
//         res.status(400).send({ "error": error })
//     }
// }

exports.checkOrderPushEligibility = async (order_name) => {
    try {
        // Calculate the time 30 minutes ago
        const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);

        const order = await dbConnection.orderProcessing.findOne({
            where: {
                order_name,
                createdAt: {
                    [Op.gt]: thirtyMinutesAgo
                }
            },
        });

        return !order; // If order is not found, return true; else, return false
    } catch (error) {
        console.log("Check Order Push Eligibility Error -> ", error);
        return false;
    }
}