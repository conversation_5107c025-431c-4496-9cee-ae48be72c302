// utils/zippeeClient.js

const axios = require('axios');
const CONFIG = require('../config/config_' + [process.env.NODE_ENV || 'local'] + '.json');

const AUTH_HEADER = CONFIG.zippee.authHeader;

const zippeeClient = async (url, payload, method) => {
  try {
    console.log("Payload:", payload);
    const headers = {
      'WWW-Authenticate': `BASIC ${AUTH_HEADER}`,
      'Content-Type': 'application/json'
    };
    console.log("URL:", url);
    const response = await axios({
      method: method.toLowerCase(),
      url,
      headers,
      data: payload
    });

    return response;
  } catch (error) {
    console.error(`Zippee API Error [${url}]:`, error.response?.data || error.message);
    throw error;
  }
};

module.exports = zippeeClient;
