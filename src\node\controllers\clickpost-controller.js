let axios = require("axios")
const CONFIG = require('../config/config_' + [process.env.NODE_ENV || 'local'] + '.json');
const moment = require("moment")

exports.registerShipmentForTracking = async (clcikpostObject) => {
    let waybill = clcikpostObject.waybillStr?.split(",")
    for (let i = 0; i < waybill.length; i++) {
        try {
            let resp = await axios.post(`${CONFIG.clickpost.url}?key=${CONFIG.clickpost.key}`, {
                "waybill": waybill[i],
                "courier_partner": clcikpostObject.courier_partner,
                "account_code": clcikpostObject.account_code,
                "shipment_info": {
                    "drop_address": clcikpostObject.drop_add,
                    "reference_number": clcikpostObject.order_name,
                    "order_id": clcikpostObject.order_id,
                },
                "pickup_info": {
                    "name": clcikpostObject.pick_name,
                    "email": clcikpostObject.pick_email != 'null' && clcikpostObject.pick_email != null ?clcikpostObject.pick_email:'<EMAIL>' ,
                    "phone": clcikpostObject.pick_phone,
                    "adderss":clcikpostObject.pick_add
                },
                "drop_info": {
                    "name": clcikpostObject.drop_name,
                    "email": clcikpostObject.drop_email != 'null' && clcikpostObject.drop_email != null ?clcikpostObject.drop_email:'<EMAIL>',
                    "phone": clcikpostObject.drop_phone, 
                    "address": clcikpostObject.drop_add
                },
                "consumer_details": {
                    "name": clcikpostObject.drop_name,
                    "phone": clcikpostObject.drop_phone,
                    "email": clcikpostObject.drop_email != 'null' && clcikpostObject.drop_email != null ?clcikpostObject.drop_email:'<EMAIL>'
                },
                "additional": {
                    "order_date": moment.utc(clcikpostObject.order_date).format('YYYY-MM-DD'),
                    "order_id": clcikpostObject.order_id
                }
            })
            console.log("registerShipmentForTracking---",JSON.stringify({
                "waybill": waybill[i],
                "courier_partner": clcikpostObject.courier_partner,
                "account_code": clcikpostObject.account_code,
                "shipment_info": {
                    "drop_address": clcikpostObject.drop_add,
                    "reference_number": clcikpostObject.order_name,
                    "order_id": clcikpostObject.order_id,
                },
                "pickup_info": {
                    "name": clcikpostObject.pick_name,
                    "email": clcikpostObject.pick_email != 'null' && clcikpostObject.pick_email != null ?clcikpostObject.pick_email:'<EMAIL>' ,
                    "phone": clcikpostObject.pick_phone,
                    "adderss":clcikpostObject.pick_add
                },
                "drop_info": {
                    "name": clcikpostObject.drop_name,
                    "email": clcikpostObject.drop_email != 'null' && clcikpostObject.drop_email != null ?clcikpostObject.drop_email:'<EMAIL>',
                    "phone": clcikpostObject.drop_phone, 
                    "address": clcikpostObject.drop_add
                },
                "consumer_details": {
                    "name": clcikpostObject.drop_name,
                    "phone": clcikpostObject.drop_phone,
                    "email": clcikpostObject.drop_email != 'null' && clcikpostObject.drop_email != null ?clcikpostObject.drop_email:'<EMAIL>'
                },
                "additional": {
                    "order_date": moment.utc(clcikpostObject.order_date).format('YYYY-MM-DD'),
                    "order_id": clcikpostObject.order_id
                }
            }))
        } catch (error) {
            console.log("Error from clcik post", error)
        }
    }
}