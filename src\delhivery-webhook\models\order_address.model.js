const Sequelize = require("sequelize");
module.exports = (sequelize) => {
  return sequelize.define("order_address", {
    id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      unique: true
    },
    store_id: {
      allowNull:false,
      type: Sequelize.INTEGER,
      references: {
        model: 'stores',
        key: 'id'
      },
    },
    address1: {
      type: Sequelize.STRING,
    },
    billing_address1: {
      type: Sequelize.STRING,
    },
    address2: {
      type: Sequelize.STRING
    },
    billing_address2: {
      type: Sequelize.STRING,
    },
    city: {
      type: Sequelize.STRING,
    },
    billing_city: {
      type: Sequelize.STRING,
    },
    company: {
      type: Sequelize.STRING,
    },
    billing_company: {
      type: Sequelize.STRING,
    },
    country: {
      type: Sequelize.STRING,
    },
    billing_country: {
      type: Sequelize.STRING,
    },
    first_name: {
      type: Sequelize.STRING,
    },
    billing_first_name: {
      type: Sequelize.STRING,
    },
    last_name: {
      type: Sequelize.STRING
    },
    billing_last_name: {
      type: Sequelize.STRING
    },
    name: {
      type: Sequelize.STRING,
    },
    billing_name: {
      type: Sequelize.STRING,
    },
    is_shipping_addresss: {
      allowNull:false,
      type: Sequelize.DataTypes.ENUM('1', '0'),
      defaultValue: '0'
    },
    latitude: {
      type: Sequelize.STRING,
    },
    longitude: {
      type: Sequelize.STRING
    },
    order_id: {
      allowNull:false,
      type: Sequelize.STRING,
      // references: {
      //   model: 'order_item_splits',
      //   key: 'id'
      // },
    },
    phone: {
      type: Sequelize.STRING
    },
    billing_phone: {
      type: Sequelize.STRING
    },
    province: {
      type: Sequelize.STRING,
    },
    billing_province: {
      type: Sequelize.STRING,
    },
    province_code: {
      type: Sequelize.STRING
    },
    billing_province_code: {
      type: Sequelize.STRING
    },
    country_code:{
      type:Sequelize.STRING
    },
    billing_country_code:{
      type:Sequelize.STRING
    },
    zip_code: {
      type: Sequelize.STRING,
    },
    billing_zip_code: {
      type: Sequelize.STRING,
    }
  },
    {
      indexes: [
        {
          unique: true,
          fields: ['store_id', 'order_id']
        }
      ]
    });
};