const { default: ShopifyApi, DataType, ApiVersion } = require('@shopify/shopify-api');
const axios = require("axios");

module.exports = class Transaction {
    constructor(shop, token) {
        this.client = new ShopifyApi.Clients.Rest(shop, token)
        this.options = {
            headers: {
                'X-Shopify-Access-Token': token,
                'Content-Type': 'application/json'
            }
        }
        this.shop = shop
    }
    get = (orderId) => {
        return new Promise(async (resolve,reject) => {
            await this.client.get({
                path:`orders/${orderId}/transactions`,
            }).then(response => {
                resolve({data:response.body.transactions})
            }).catch(err => {
                console.log("Errror===>",err)
                resolve({error:err})})
        })
    }
}
