// const { default: ShopifyApi, DataType, ApiVersion } = require('@shopify/shopify-api');
const axios = require("axios");
// const { shopifyGraphqlClient2025 } = require('../helper/shopifyGraphQLClient.js');
module.exports = class Transaction {
    constructor(shop, token) {
        // this.client = new ShopifyApi.Clients.Rest(shop, token)
        // below line is for latest version of @shopify/shopify-api()
        // this.graphqlClient = shopifyGraphqlClient2025({ shop, accessToken: token });
        this.options = {
            headers: {
                'X-Shopify-Access-Token': token,
                'Content-Type': 'application/json'
            }
        }
        this.shop = shop
    }
    get = (orderId) => {
        return new Promise(async (resolve,reject) => {
            /*
            await this.client.get({
                path:`orders/${orderId}/transactions`,
            }).then(response => {
                resolve({data:response.body.transactions})
            }).catch(err => {
                console.log("Errror===>",err)
                resolve({error:err})})
            */

            const query = `
                query getOrderTransactions($id: ID!) {
                    order(id: $id) {
                        id
                        transactions {
                            id
                            authorizationCode
                            receiptJson
                            order {
                                sourceName
                            }
                            paymentId
                            status
                            kind
                            test
                            gateway
                        }
                    }
                }
            `;

            try {
                const response = await axios.post(
                    `https://${this.shop}/admin/api/2025-07/graphql.json`,
                    {
                        query,
                        variables: {
                            id: `gid://shopify/Order/${orderId}`
                        }
                    },
                    {
                        headers: this.options.headers
                    }
                );

                console.log('okaayyyy',response.data)
                if (response.data?.data?.order) {
                    resolve({ data: response.data.data.order.transactions });
                } else {
                    resolve({ data: [] }); // No transactions found
                }
            } catch (err) {
                console.log("GraphQL Error===>", err);
                resolve({ error: err });
            }
        })
    }
} 