import { _pagination } from "../config/settings"
import { debounce } from 'lodash';
import moment from 'moment-timezone';


export const setPagination = function (page = _pagination.page, perpage = _pagination.perpage, totalpages = 0, total = 0) {
  let pagination = _pagination;
  const from = ((page * perpage) - perpage) + 1;
  const to = totalpages === page ? total : perpage * page;
  const hasPrevious = page > 1;
  const hasNext = totalpages > page;
  let showing = total > 0 ? `Showing ${from} to ${to} of ${total} entries` : null;
  pagination = { ...pagination, hasNext: hasNext, hasPrevious: hasPrevious, page: page, perpage: perpage, showing: showing }
  return pagination;
}


export const priceConversion = (price) => {
  return price ? parseFloat(price).toFixed(2) : price;
}

//Date Conversion
export function dateConversion(datetime) {

  //new add
  const DateConvert = moment.utc(datetime).tz('Asia/Kolkata').format('DD MMM YYYY, hh:mm A');
  return DateConvert;

  // const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun",
  //   "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"
  // ];Hours (12 hour time used with a A.)
  // const date = new Date(String(istTime));
  // var hours = date.getHours();
  // console.log('hours created date',hours)
  // var ampm = hours >= 12 ? 'PM' : 'AM';
  // var DateConvert =
  //   ("00" + (date.getDate())).slice(-2)
  //   + " " + ("00" + monthNames[(date.getMonth())]).slice(-3)
  //   + " " + date.getFullYear() + ", "
  //   + ("00" + date.getHours()).slice(-2) + ":"
  //   + ("00" + date.getMinutes()).slice(-2) + ' ' + ampm;

  // return DateConvert;
}


export function debounceEvent(...args) {
  debounceEvent = debounce(...args);
  return e => {
    e.perstist();
    return debounceEvent(e);
  }
}

export function isEmpty(value) {
  if (Array.isArray(value)) {
    return value.length === 0;
  } else {
    return value === '' || value == null;
  }
}


export function disambiguateLabel(key, value) {
  switch (key) {
    case "status":
      return value.join(", ");
    case "fulfillmentStatus":
      return value.join(", ");
    case "paymentStatus":
      return value.join(", ");
    case "isGstValid":
      return (gstValid.find((ele) => ele.value == (value[0] || value)).label) || value
    default:
      return value;
  }
}
export function fileTypes() {
  let types = ['image/gif', 'image/jpeg', 'image/png', 'image/jpg', 'image/tif', 'image/tiff', 'application/vnd.ms-excel', '.svg', '.js', '.css', '.html', '.ttf', '.otf', '.fnt', '.woff', '.woff2', '.jfproj', '.fnt', '.pfa', '.fot', '.sfd', '.vlw', '.pfb', '.gxf', '.odttf', '.etx', '.fon', '.chr', '.ttc', '.vfb', '.bdf', '.pmt', '.gf', '.amfm', '.pfm', '.mf', '.abf', '.compositefont', '.gdr', '.vnf', '.mxf', '.pcf', '.xfn', '.bf', '.sfp', '.pf2', '.glif', '.tfm', '.pfr', '.afm', '.tte', '.dfont', '.xft', '.acfm', '.eot', '.pk', '.ffil', '.suit', '.nftr', '.txf', '.euf', '.mcf', '.cha', '.lwfn', '.ufo', '.t65', '.ytf', '.f3f', '.pft', '.fea', '.sft']
  return types
}
export function updateFileTypes() {
  let types = ['image/gif', 'image/jpeg', 'image/png', 'image/jpg', 'image/tif', 'image/tiff', 'application/vnd.ms-excel', '.svg', '.ttf', '.otf', '.fnt', '.woff', '.woff2', '.jfproj', '.fnt', '.pfa', '.fot', '.sfd', '.vlw', '.pfb', '.gxf', '.odttf', '.etx', '.fon', '.chr', '.ttc', '.vfb', '.bdf', '.pmt', '.gf', '.amfm', '.pfm', '.mf', '.abf', '.compositefont', '.gdr', '.vnf', '.mxf', '.pcf', '.xfn', '.bf', '.sfp', '.pf2', '.glif', '.tfm', '.pfr', '.afm', '.tte', '.dfont', '.xft', '.acfm', '.eot', '.pk', '.ffil', '.suit', '.nftr', '.txf', '.euf', '.mcf', '.cha', '.lwfn', '.ufo', '.t65', '.ytf', '.f3f', '.pft', '.fea', '.sft']
  return types
}




export const StatusArray = {
  //payment status
  "paid": { color: "complete", lable: "Paid", value: "paid" },
  "unpaid": { color: "warning", lable: "Unpaid", value: "unpaid" },

  //fulfilment status
  "unfulfilled": { color: "attention", lable: "Unfulfilled", value: "unfulfilled", progress: "incomplete" },
  "partially": { color: "partiallyComplete", lable: "Partially Fulfilled", value: "partially fulfilled", progress: "partiallyComplete" },
  "fulfilled": { color: "complete", lable: "Fulfilled", value: "fulfilled", progress: "complete" },

  //order status
  "Completed": { color: "success", lable: "Completed", value: "completed" },

  "Out Of Stock": { color: "critical", lable: "Out Of Stock", value: "Out of Stock" },
  "Inprocess": { color: "partiallyComplete", lable: "Inprocess", value: "Inprocess" },
  "PincodeNotAvailable": { color: "partiallyComplete", lable: "PincodeNotAvailable", value: "PincodeNotAvailable" },
  "Refund": { color: "partiallyComplete", lable: "Refund", value: "Refund" },
  "Replacement": { color: "partiallyComplete", lable: "Replacement", value: "Replacement" },
  "Returned": { color: "partiallyComplete", lable: "Returned", value: "Returned" },
  "Cancel": { color: "partiallyComplete", lable: "Cancel", value: "Cancel" },

  //sap and Delivery Status
  "Pushed": { color: "complete", lable: "Pushed", value: "Pushed" },
  "Invoiced": { color: "success", lable: "Invoiced", value: "Invoiced" },


  //shipment status
  "inTransit": { color: "attention", lable: "In Transit", value: "inTransit", progress: "incomplete" },
  "dispatched": { color: "partiallyComplete", lable: "Dispatched", value: "dispatched", progress: "partiallyComplete" },
  "delivered": { color: "success", lable: "Delivered", value: "delivered", progress: "complete" },

  //common status
  "Pending": { color: "warning", lable: "Pending", value: "pending" },
  "Failed": { color: "critical", lable: "Failed", value: "failed" },
}

export const gstValid = [
  { label: "Valid", value: 1 },
  { label: "Invalid", value: 0 },
]

export const modelArray = [
  {"value":"Select","label":"Select Model"},
  { "value": "CLOUD", "label": "CLOUD" },
  { "value": "DIET 12 T", "label": "DIET 12 T" },
  { "value": "Diet 12i", "label": "Diet 12i" },
  { "value": "DIET 22 T", "label": "DIET 22 T" },
  { "value": "Diet 22i", "label": "Diet 22i" },
  { "value": "DIET 35 T", "label": "DIET 35 T" },
  { "value": "Diet 3D 20i", "label": "Diet 3D 20i" },
  { "value": "Diet 3D 30i", "label": "Diet 3D 30i" },
  { "value": "Diet 3D 40i", "label": "Diet 3D 40i" },
  { "value": "Diet 3D 55i+", "label": "Diet 3D 55i+" },
  { "value": "DIET 50i black", "label": "DIET 50i black" },
  { "value": "Diet 8i", "label": "Diet 8i" },
  { "value": "DUET", "label": "DUET" },
  { "value": "DUET I-S", "label": "DUET I-S" },
  { "value": "Hi cool 45T", "label": "Hi cool 45T" },
  { "value": "HI COOL i", "label": "HI COOL i" },
  { "value": "HI FLO", "label": "HI FLO" },
  { "value": "ICE CUBE 17", "label": "ICE CUBE 17" },
  { "value": "ICE CUBE 20", "label": "ICE CUBE 20" },
  { "value": "ICE CUBE 27", "label": "ICE CUBE 27" },
  { "value": "JUMBO 41 G", "label": "JUMBO 41 G" },
  { "value": "JUMBO 45 +", "label": "JUMBO 45 +" },
  { "value": "JUMBO 45 DB", "label": "JUMBO 45 DB" },
  { "value": "JUMBO 51 G", "label": "JUMBO 51 G" },
  { "value": "JUMBO 65 +", "label": "JUMBO 65 +" },
  { "value": "JUMBO 65 + (Trolley)", "label": "JUMBO 65 + (Trolley)" },
  { "value": "JUMBO 65 DB", "label": "JUMBO 65 DB" },
  { "value": "JUMBO 70 G", "label": "JUMBO 70 G" },
  { "value": "KAIZEN 122 DB", "label": "KAIZEN 122 DB" },
  { "value": "KAIZEN 122DB G", "label": "KAIZEN 122DB G" },
  { "value": "KAIZEN DB 151", "label": "KAIZEN DB 151" },
  { "value": "KAIZEN DB 151 G", "label": "KAIZEN DB 151 G" },
  { "value": "MOVICOOL DD 125", "label": "MOVICOOL DD 125" },
  { "value": "MOVICOOL L125", "label": "MOVICOOL L125" },
  { "value": "MOVICOOL L200I", "label": "MOVICOOL L200I" },
  { "value": "MOVICOOL XL100 G", "label": "MOVICOOL XL100 G" },
  { "value": "MOVICOOL XL200 G", "label": "MOVICOOL XL200 G" },
  { "value": "MOVICOOL XL200 I", "label": "MOVICOOL XL200 I" },
  { "value": "MOVICOOL XXL", "label": "MOVICOOL XXL" },
  { "value": "MOVICOOL XXL KIT", "label": "MOVICOOL XXL KIT" },
  { "value": "NINJA", "label": "NINJA" },
  { "value": "NINJA 30", "label": "NINJA 30" },
  { "value": "SIESTA", "label": "SIESTA" },
  { "value": "SIESTA 70 XL", "label": "SIESTA 70 XL" },
  { "value": "SIESTA 70XL G", "label": "SIESTA 70XL G" },
  { "value": "SIESTA G", "label": "SIESTA G" },
  { "value": "SILVER", "label": "SILVER" },
  { "value": "SILVER I ", "label": "SILVER I" },
  { "value": "STORM 100 I - G", "label": "STORM 100 I - G" },
  { "value": "STORM 100I", "label": "STORM 100I" },
  { "value": "STORM 70 I - G", "label": "STORM 70 I - G" },
  { "value": "STORM 70 XL", "label": "STORM 70 XL" },
  { "value": "STORM 70 XL - G", "label": "STORM 70 XL - G" },
  { "value": "STORM 70I", "label": "STORM 70I" },
  { "value": "Sumo 115XL", "label": "Sumo 115XL" },
  { "value": "SUMO 70", "label": "SUMO 70" },
  { "value": "SUMO 70 G", "label": "SUMO 70 G" },
  { "value": "SUMO 70XL- G", "label": "SUMO 70XL- G" },
  { "value": "Sumo 75XL", "label": "Sumo 75XL" },
  { "value": "Sumo 75XL DD", "label": "Sumo 75XL DD" },
  { "value": "SUMO i", "label": "SUMO i" },
  { "value": "SUMO i G", "label": "SUMO i G" },
  { "value": "SUMO JR.", "label": "SUMO JR." },
  { "value": "SUMO JR. G", "label": "SUMO JR. G" },
  { "value": "TOUCH 110", "label": "TOUCH 110" },
  { "value": "TOUCH 20", "label": "TOUCH 20" },
  { "value": "TOUCH 35", "label": "TOUCH 35" },
  { "value": "TOUCH 55", "label": "TOUCH 55" },
  { "value": "TOUCH 80", "label": "TOUCH 80" },
  { "value": "Venti-Cool 20U", "label": "Venti-Cool 20U" },
  { "value": "Venti-Cool 25U", "label": "Venti-Cool 25U" },
  { "value": "Winter +", "label": "Winter +" },
  { "value": "Winter 80XL i+", "label": "Winter 80XL i+" },
  { "value": "Winter 80XL+", "label": "Winter 80XL+" },
  { "value": "SURROUND", "label": "SURROUND" },
  { "value": "SURROUND I", "label": "SURROUND I" },
  { "value": "Symphony Sauna (10L, White & Grey)", "label": "Symphony Sauna (10L, White & Grey)" },
  { "value": "Symphony Sauna (15L, White & Grey)", "label": "Symphony Sauna (15L, White & Grey)" },
  { "value": "Symphony Sauna (25L, White & Grey)", "label": "Symphony Sauna (25L, White & Grey)" },
  { "value": "Symphony Soul (10L, White & Grey)", "label": "Symphony Soul (10L, White & Grey)" },
  { "value": "Symphony Soul (15L, White & Grey)", "label": "Symphony Soul (15L, White & Grey)" },
  { "value": "Symphony Soul (25L, White & Grey)", "label": "Symphony Soul (25L, White & Grey)" },
  { "value": "Symphony SPA & AI-Powered Water Heater (10L, White & Grey)", "label": "Symphony SPA & AI-Powered Water Heater (10L, White & Grey)" },
  { "value": "Symphony SPA & AI-Powered Water Heater (15L, White & Grey)", "label": "Symphony SPA & AI-Powered Water Heater (15L, White & Grey)" },
  { "value": "Symphony SPA & AI-Powered Water Heater (25L, White & Grey)", "label": "Symphony SPA & AI-Powered Water Heater (25L, White & Grey)" },
]


export const statesArr = [
  { label: "Andaman and Nicobar Islands", value: "AN" },
  { label: "Andhra Pradesh", value: "AP" },
  { label: "Arunachal Pradesh", value: "AR" },
  { label: "Assam", value: "AS" },
  { label: "Bihar", value: "BR" },
  { label: "Chandigarh", value: "CH" },
  { label: "Chhattisgarh", value: "CG" },
  { label: "Dadra and Nagar Haveli", value: "DN" },
  { label: "Daman and Diu", value: "DD" },
  { label: "Delhi", value: "DL" },
  { label: "Goa", value: "GA" },
  { label: "Gujarat", value: "GJ" },
  { label: "Haryana", value: "HR" },
  { label: "Himachal Pradesh", value: "HP" },
  { label: "Jammu and Kashmir", value: "JK" },
  { label: "Jharkhand", value: "JH" },
  { label: "Karnataka", value: "KA" },
  { label: "Kerala", value: "KL" },
  { label: "Ladakh", value: "LA" },
  { label: "Lakshadweep", value: "LD" },
  { label: "Madhya Pradesh", value: "MP" },
  { label: "Maharashtra", value: "MH" },
  { label: "Manipur", value: "MN" },
  { label: "Meghalaya", value: "ML" },
  { label: "Mizoram", value: "MZ" },
  { label: "Nagaland", value: "NL" },
  { label: "Odisha", value: "OR" },
  { label: "Puducherry", value: "PY" },
  { label: "Punjab", value: "PB" },
  { label: "Rajasthan", value: "RJ" },
  { label: "Sikkim", value: "SK" },
  { label: "Tamil Nadu", value: "TN" },
  { label: "Telangana", value: "TS" },
  { label: "Tripura", value: "TR" },
  { label: "Uttar Pradesh", value: "UP" },
  { label: "Uttarakhand", value: "UK" },
  { label: "West Bengal", value: "WB" },
]

export const actionMsg = {
  "Approved": "Order is no more on hold",
  "Rejected": "Order will procees without GST",
  "Edit": "Gst edited successfully",
  "EditBilling": "Billing address updated successfully"
}

export const textFieldArr = {
  'billing_address1': "Address",
  'billing_address2': "Apartment, suite, etc.",
  'billing_city': "City",
  'billing_company': "Company",
  'billing_first_name': "First name",
  'billing_last_name': "Last name",
  'billing_phone': "Phone",
  'billing_province_code': "State",
  'billing_zip_code': "PIN code",
}

export const textServiceRequestArr = {
  'address1': "Address",
  'city': "City",
  'company': "Company",
  'first_name': "First name",
  'last_name': "Last name",
  'phone': "Phone",
  'province_code': "State",
  'zip_code': "PIN code",
  'customer_email': "Email",
  'model_type':"Select Model",
  'product_serial_number':"Product Serial Number",
  'reason':'Complain Reason'
}

export function checkNullOrUndefined(input) {
  let value = input == 'undefined' || input == undefined || input == 'null' || input == null ? '' : input;
  return value
}