import { orderTypes } from './orderTypes';

export const getOrdersListRequest = (data) => ({
    type: orderTypes.GET_ORDERS_LIST_REQUEST,
    payload: data
})

export const getReturnOrdersListRequest = (data) => ({
    type: orderTypes.GET_RETURN_ORDERS_LIST_REQUEST,
    payload: data
})

export const getOrdersListSuccess = (data) => ({
    type: orderTypes.GET_ORDERS_LIST_SUCCESS,
    payload: data,
});

export const syncOrderRequest = (orderSyncData) => ({
    type: orderTypes.GET_ORDERS_SYNC_REQUEST,
    payload: orderSyncData
})

export const syncOrderSuccess = (orderSyncData) => ({
    type: orderTypes.GET_ORDERS_SYNC_SUCCESS,
    payload: orderSyncData
})
export const syncOrderError = (data) => ({
    type: orderTypes.GET_ORDERS_SYNC_ERROR,
    payload: data
})
export const pushOrdersRequest = (data) => ({
    type: orderTypes.PUSH_ORDERS_REQUEST,
    payload: data
})

export const pushForWarrantyRequest = (data) => ({
    type: orderTypes.PUSH_FOR_WARRANTY_REQUEST,
    payload: data
})

export const pushForWarrantySuccess = (data) => ({
    type: orderTypes.PUSH_FOR_WARRANTY_SUCCESS,
    payload: data
})
export const pushForWarrantyError = (data) => ({
    type: orderTypes.PUSH_FOR_WARRANTY_ERROR,
    payload: data
})
export const pushOrdersSuccess = (data) => ({
    type: orderTypes.PUSH_ORDERS_SUCCESS,
    payload: data
})
export const pushOrdersError = (data) => ({
    type: orderTypes.PUSH_ORDERS_ERROR,
    payload: data
})

export const cancelOrdersRequest = (data) => ({
    type: orderTypes.CANCEL_ORDERS_REQUEST,
    payload: data
})
export const cancelOrdersSuccess = (data) => ({
    type: orderTypes.CANCEL_ORDERS_SUCCESS,
    payload: data
})
export const cancelOrdersError = (data) => ({
    type: orderTypes.CANCEL_ORDERS_ERROR,
    payload: data
})

export const replacementOrdersRequest = (data) => ({
    type: orderTypes.REPLACEMENT_ORDERS_REQUEST,
    payload: data
})
export const replacementOrdersSuccess = (data) => ({
    type: orderTypes.REPLACEMENT_ORDERS_SUCCESS,
    payload: data
})
export const replacementOrdersError = (data) => ({
    type: orderTypes.REPLACEMENT_ORDERS_ERROR,
    payload: data
})
export const orderReturnRequest = (data) => ({
    type: orderTypes.ORDER_RETURN_REQUEST,
    payload: data
})
export const orderReturnSuccess = (data) => ({
    type: orderTypes.ORDER_RETURN_SUCCESS,
    payload: data
})
export const orderReturnError = (data) => ({
    type: orderTypes.ORDER_RETURN_ERROR,
    payload: data
})

export const createExportOrdersRequest = (orderData) => ({
    type: orderTypes.EXPORT_ORDERS_REQUEST,
    payload: orderData
})

export const createExportOrdersSuccess = (orderData) => ({
    type: orderTypes.EXPORT_ORDERS_SUCCESS,
    payload: orderData
})

export const createExportOrdersError = (orderData) => ({
    type: orderTypes.EXPORT_ORDERS_ERROR,
    payload: orderData
})

export const submitOrderCFARequest = (data) => ({
    type: orderTypes.SUBMIT_ORDER_CFA_REQUEST,
    payload: data
})
export const submitOrderCFASuccess = (data) => ({
    type: orderTypes.SUBMIT_ORDER_CFA_SUCCESS,
    payload: data
})
export const submitOrderCFAError = (data) => ({
    type: orderTypes.SUBMIT_ORDER_CFA_ERROR,
    payload: data
})

export const createRefundOrdersRequest = (data) => ({
    type: orderTypes.REFUND_ORDERS_REQUEST,
    payload: data
})
export const createRefundOrdersSuccess = (data) => ({
    type: orderTypes.REFUND_ORDERS_SUCCESS,
    payload: data
})
export const createRefundOrdersError = (data) => ({
    type: orderTypes.REFUND_ORDERS_ERROR,
    payload: data
})

export const returnOrderRequest = (data) => ({
    type: orderTypes.RETURN_ORDER_REQUEST,
    payload: data
})
export const returnOrderRequestSuccess = (data) => ({
    type: orderTypes.RETURN_ORDER_REQUEST_SUCCESS,
    payload: data
})
export const returnOrderRequestError = (data) => ({
    type: orderTypes.RETURN_ORDER_REQUEST_ERROR,
    payload: data
})

export const exportOrderReturnRequest = (data) => ({
    type: orderTypes.EXPORT_ORDER_RETURN_REQUEST,
    payload: data
})
export const exportOrderReturnSuccess = (data) => ({
    type: orderTypes.EXPORT_ORDER_RETURN_SUCCESS,
    payload: data
})
export const exportOrderReturnError = (data) => ({
    type: orderTypes.EXPORT_ORDER_RETURN_ERROR,
    payload: data
})

export const exportOrderFailedRequest = (data) => ({
    type: orderTypes.EXPORT_FAILED_ORDERS_REQUEST,
    payload: data
})

export const exportOrderFailedSuccess = (data) => ({
    type: orderTypes.EXPORT_FAILED_ORDERS_SUCCESS,
    payload: data
})
export const exportOrderFailedError = (data) => ({
    type: orderTypes.EXPORT_FAILED_ORDERS_ERROR,
    payload: data
})

export const saveOrderDetailsRequest = (data) => ({
    type: orderTypes.SAVE_ORDER_DETAILS_REQUEST,
    payload: data
})
export const saveOrderDetailsSuccess = (data) => ({
    type: orderTypes.SAVE_ORDER_DETAILS_SUCCESS,
    payload: data
})
export const saveOrderDetailsError = (data) => ({
    type: orderTypes.SAVE_ORDER_DETAILS_ERROR,
    payload: data
})

export const handleSubmitServiceRequest = (data) => ({
    type: orderTypes.SUBMIT_SERVICE_REQUEST,
    payload: data
})
export const handleSubmitServiceSuccess = (data) => ({
    type: orderTypes.SUBMIT_SERVICE_SUCCESS,
    payload: data
})
export const handleSubmitServiceError = (data) => ({
    type: orderTypes.SUBMIT_SERVICE_ERROR,
    payload: data
})

export const getAllFailedOrdersRequest = (data) => ({
    type: orderTypes.GET_FAILED_ORDERS_REQUEST,
    payload: data
})

export const getAllFailedOrdersSuccess = (data) => ({
    type: orderTypes.GET_FAILED_ORDERS_SUCCESS,
    payload: data,
});

export const getAllWarrantyOrdersSuccess = (data) => ({
    type: orderTypes.GET_WARRANTY_ORDERS_SUCCESS,
    payload: data,
});
export const getAllWarrantyOrdersRequest = (data) => ({
    type: orderTypes.GET_WARRANTY_ORDERS_REQUEST,
    payload: data
})

export const exportWarrantyOrdersRequest = (orderData) => ({
    type: orderTypes.EXPORT_WARRANTY_ORDERS_REQUEST,
    payload: orderData
})

export const exportWarrantyOrdersSuccess = (data) => ({
    type: orderTypes.EXPORT_WARRANTY_ORDERS_SUCCESS,
    payload: data
})
export const exportWarrantyOrdersError = (data) => ({
    type: orderTypes.EXPORT_WARRANTY_ORDERS_ERROR,
    payload: data
})

export const saveRefundFileRequest = (data) => ({
    type: orderTypes.SEND_REFUND_FILE_REQUEST,
    payload: data
})

export const saveRefundFileSuccess = (data) => ({
    type: orderTypes.SEND_REFUND_FILE_SUCCESS,
    payload: data,
});

export const saveRefundFileError = (data) => ({
    type: orderTypes.SEND_REFUND_FILE_ERROR,
    payload: data,
});