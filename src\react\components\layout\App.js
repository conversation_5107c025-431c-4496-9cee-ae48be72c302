import React, { Component } from 'react';
import { AppProvider, Frame } from '@shopify/polaris';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import { Provider as AppBridgeProvider } from '@shopify/app-bridge-react';
import Routes from './Routes';
import { Provider } from 'react-redux';
import store from '../../redux/Store';
import TopBarMarkup from './TopBarMarkup';
import NavigationMarkup from './NavigationMarkup';
import ToastMarkup from './ToastMarkup';
import en from '@shopify/polaris/locales/en.json';
import { shopConfig } from '../../config/settings';
import { setAuthorization } from '../../config/auth';
import { Loading } from '@shopify/app-bridge-react';
const theme = {
  colors: {
    topBar: {
      background: '#37474f',
    },
  },
  logo: {
    width: 200,
    topBarSource:
      'https://cdn.shopify.com/s/files/1/1905/9639/files/<EMAIL>',
    accessibilityLabel: 'Symphony',
  },
};

class App extends Component {
  constructor(props) {
    super(props);
    this.state = {
      mobileNavigationActive: false,
      isLogin: false,
    }
  }
  componentDidMount() {
    // setAuthorization((authUser) => {authUser ? this.setState({ isLogin: true }) : this.setState({ isLogin: false }) ;});
  }
  render() {
    const { mobileNavigationActive } = this.state;
    return (<div style={{ height: '500px' }}>
      <AppProvider
        theme={theme}
        i18n = {en}
      >
        <Provider store={store}>
        {/* <AppBridgeProvider config={shopConfig} > */}
          <BrowserRouter>
          {/* {this.state.isLogin ? */}
               
               <Frame
              topBar={<TopBarMarkup mobileNavigationActive={false} />}
              showMobileNavigation={mobileNavigationActive}
              // navigation={<NavigationMarkup />}
            >
              <Routes />
                <ToastMarkup /> 
            </Frame>
           {/* : 
           <>
             <Loading />
           </>
         } */}
           
          </BrowserRouter>
          {/* </AppBridgeProvider> */}
        </Provider>
      </AppProvider>
    </div>);
  }
}

export default App;
