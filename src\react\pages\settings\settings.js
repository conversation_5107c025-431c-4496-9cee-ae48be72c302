import React, { Component } from 'react';
import {
    Page,
    Layout,
    Card,
    Icon,
    Modal,
    DropZone,
    Stack,
    Caption,
    DataTable,
    SkeletonBodyText,
    Button,
    TextContainer,
} from '@shopify/polaris';
import { SearchMajor, NoteMajor, DeleteMinor } from '@shopify/polaris-icons';
import { isEmpty } from '../../helpers/appHelpers';
import { connect } from 'react-redux';
import { getCloudSkuListRequest, getCoverSkuListRequest, saveCloudSkuFileRequest, saveCoverSkuFileRequest, coverExportRequest, cloudExportRequest, deleteCoverRequest, deleteCloudRequest } from '../../redux/settings/settingsActions';
import '../../../react/App.css';

export class settings extends Component {
    constructor(props) {
        super(props);
        this.state = {
            coverSkuActive: false,
            cloudSkuActive: false,
            isSaveCoverDisabled: true,
            isSaveCloudDisabled: true,
            coverfiles: [],
            cloudfiles: [],
            importCoverButtonLoading: false,
            importCloudButtonLoading: false,
            deleteSkuModalLoading: false,
            coverPageLoading: false,
            cloudPageLoading: false,
            cloudErrorMessage: null,
            coverErrorMessage: null,
            coverRejectedFiles: [],
            cloudRejectedFiles: [],
            deleteModalActive: false,
            skuId: null,
            skuValue: null,
            modalHeader: null,
        }
    }
    componentDidMount() {
        document.title = "Settings";
        this.getAllCoverSkuData();
        this.getAllCloudSkuData();
    }

    getAllCoverSkuData = () => {
        this.setState({ coverPageLoading: true })
        let { getCoverSkuList } = this.props;
        getCoverSkuList({
            callback: () => {
                if (!this.props.error) {
                    let { coverloading } = this.props;
                    this.setState({ coverPageLoading: coverloading })
                }
            }
        });
    }
    getAllCloudSkuData = () => {
        this.setState({ cloudPageLoading: true })
        let { getCloudSkuList } = this.props;
        getCloudSkuList({
            callback: () => {
                if (!this.props.error) {
                    let { cloudloading } = this.props;
                    this.setState({ cloudPageLoading: cloudloading })
                }
            }
        });
    }
    cloudSkuListMarkup = () => {
        const { cloudloading, cloudSkuListData } = this.props;
        let rows = [];
        if (!cloudloading) {
            const dataObj = cloudSkuListData.data.data;
            if (!isEmpty(dataObj)) {
                dataObj.forEach((sku) => {
                    const row = [
                        sku.child_sku,
                        sku.cfa,
                        <Button
                            monochrome
                            plain
                            size="slim"
                            removeUnderline={true}
                            onClick={() => { this.cloudDelete(sku.id, sku.child_sku); }}
                        >
                            <Icon source={DeleteMinor} />
                            <span className="action-button-text">Delete</span>
                        </Button>
                    ];
                    rows.push(row);
                });
            } else {
                return (<div className="search_div">
                    <Icon source={SearchMajor} color="base" />
                    <div>
                        <span className="content_span">No data found</span>
                    </div>
                </div>);
            }
        } else {
            rows = [];
            for (var i = 0; i <= 2; i++) {
                rows.push([
                    <SkeletonBodyText lines={1} />,
                    <SkeletonBodyText lines={1} />,
                    <SkeletonBodyText lines={1} />
                ]);
            }
        }
        return (
            <DataTable
                verticalAlign="middle"
                columnContentTypes={["text", "text"]}
                headings={[
                    <b>Child SKU</b>,
                    <b>CFA</b>,
                    <b>Action</b>
                ]}
                rows={rows}
            />
        );
    }

    coverSkuListMarkup = () => {
        const { coverloading, coverSkuListData } = this.props;
        let rows = [];
        if (!coverloading) {
            const dataObj = coverSkuListData.data.data;
            if (!isEmpty(dataObj)) {
                dataObj.forEach((sku) => {
                    const row = [
                        sku.master_sku,
                        sku.child_sku,
                        sku.cfa,
                        <Button
                            monochrome
                            plain
                            size="slim"
                            removeUnderline={true}
                            onClick={() => { this.coverDelete(sku.id, sku.master_sku); }}
                        >
                            <Icon source={DeleteMinor} />
                            <span className="action-button-text">Delete</span>
                        </Button>
                    ];
                    rows.push(row);
                }, this);
            } else {
                return (<div className="search_div">
                    <Icon source={SearchMajor} color="base" />
                    <div>
                        <span className="content_span">No data found</span>
                    </div>
                </div>);
            }
        } else {
            rows = [];
            for (var i = 0; i <= 2; i++) {
                rows.push([
                    <SkeletonBodyText lines={1} />,
                    <SkeletonBodyText lines={1} />,
                    <SkeletonBodyText lines={1} />
                ]);
            }
        }

        return (
            <DataTable
                verticalAlign="middle"
                columnContentTypes={["text", "text", "text"]}
                headings={[
                    <b>Master SKU</b>,
                    <b>Child SKU</b>,
                    <b>CFA</b>,
                    <b>Action</b>
                ]}
                rows={rows}
            />
        );
    };

    cloudSkuModalActive = () => {
        this.setState({ cloudSkuActive: !this.state.cloudSkuActive, cloudfiles: [] })
    }
    coverSkuModalActive = () => {
        this.setState({ coverSkuActive: !this.state.coverSkuActive, coverfiles: [] })
    }
    coverSkuSubmit = () => {
        this.setState({ importCoverButtonLoading: true });
        var files = this.state.coverfiles;
        if (files[0].type == 'xlsx' || files[0].type == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
            let formData = new FormData();
            if (!isEmpty(files)) {
                formData.append('file', files[0]);
                let { saveCoverSkuFile } = this.props;
                saveCoverSkuFile({
                    formData,
                    callback: () => {
                        if (!this.props.error) {
                            this.setState({ importCoverButtonLoading: false, coverfiles: [], coverSkuActive: false },
                                () => {
                                    this.getAllCoverSkuData();
                                });
                        } else {
                            this.setState({ importCoverButtonLoading: false });
                        }
                    },
                });
            }
        } else {
            this.setState({ coverErrorMessage: "Only xlsx file is allowed", importCoverButtonLoading: false })
        }
    }
    cloudSkuSubmit = () => {
        this.setState({ importCloudButtonLoading: true });
        var files = this.state.cloudfiles;
        if (files[0].type == 'xlsx' || files[0].type == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
            let formData = new FormData();
            if (!isEmpty(files)) {
                formData.append('file', files[0]);
                let { saveCloudSkuFile } = this.props;
                saveCloudSkuFile({
                    formData,
                    callback: () => {
                        if (!this.props.error) {
                            this.setState({ importCloudButtonLoading: false, cloudfiles: [], cloudSkuActive: false },
                                () => {
                                    this.getAllCloudSkuData();
                                });
                        } else {
                            this.setState({ importCloudButtonLoading: false });
                        }
                    },
                });
            }
        } else {
            this.setState({ cloudErrorMessage: "Only xlsx file is allowed", importCloudButtonLoading: false })
        }
    }

    handleCoverDrop = (_droppedFiles, files, rejectedFiles) => {
        if (rejectedFiles.length > 0) {
            this.setState({ isSaveCoverDisabled: true })
            this.setState({ coverRejectedFiles: rejectedFiles })
        } else {
            this.setState({ coverfiles: files })
            this.setState({ isSaveCoverDisabled: false })
        }
    }
    handleCloudDrop = (_droppedFiles, files, rejectedFiles) => {
        if (rejectedFiles.length > 0) {
            this.setState({ isSaveCloudDisabled: true })
            this.setState({ cloudRejectedFiles: rejectedFiles })
        } else {
            this.setState({ cloudfiles: files })
            this.setState({ isSaveCloudDisabled: false })
        }
    }
    coverSkuExport = () => {
        let { coverExport } = this.props;
        coverExport({
            callback: () => {
                if (!this.props.error) {
                    var response = this.props.coverExportResponse;
                    const downloadUrl = window.URL.createObjectURL(
                        new Blob([response.data])
                    );
                    const link = document.createElement("a");
                    link.href = downloadUrl;
                    var name = "sku";
                    link.setAttribute("download", name + ".xlsx");
                    document.body.appendChild(link);
                    link.click();
                    link.remove();
                    this.getAllCoverSkuData();
                } else {
                    console.log("error==>>>>", this.props.error)
                }
            },
        });
    }
    cloudSkuExport = () => {
        let { cloudExport } = this.props;
        cloudExport({
            callback: () => {
                if (!this.props.error) {
                    var response = this.props.cloudExportResponse;
                    const downloadUrl = window.URL.createObjectURL(
                        new Blob([response.data])
                    );
                    const link = document.createElement("a");
                    link.href = downloadUrl;
                    var name = "sku";
                    link.setAttribute("download", name + ".xlsx");
                    document.body.appendChild(link);
                    link.click();
                    link.remove();
                    this.getAllCoverSkuData();
                } else {
                    console.log("error==>>>>", this.props.error)
                }
            },
        });
    }
    cloudDelete = (cloudSkuID, childSku) => {
        this.setState({
            skuId: cloudSkuID,
            skuValue: childSku,
            modalHeader: 'Remove Cloud SKU',
            deleteModalActive: true,
        });
    }

    coverDelete = (coverSkuID, masterSku) => {
        this.setState({
            skuId: coverSkuID,
            skuValue: masterSku,
            modalHeader: 'Remove Cover SKU',
            deleteModalActive: true,
        });
    }

    deleteSkuData = () => {
        this.setState({ deleteSkuModalLoading: true })
        if (this.state.modalHeader == 'Remove Cover SKU') {
            const request = {
                id: this.state.skuId,
                master_sku: this.state.skuValue
            }
            let { deleteCoverSku } = this.props;
            deleteCoverSku({
                request,
                callback: () => {
                    if (!this.props.error) {
                        this.setState({ deleteSkuModalLoading: false, deleteModalActive: false },
                            () => {
                                this.getAllCoverSkuData();
                            })

                    }else{
                        this.setState({deleteSkuModalLoading:false})
                    }
                }
            })
        } else if (this.state.modalHeader == 'Remove Cloud SKU') {
            const request = {
                id: this.state.skuId,
                child_sku: this.state.skuValue
            }
            let { deleteCloudSku } = this.props;
            deleteCloudSku({
                request,
                callback: () => {
                    if (!this.props.error) {
                        let { skuModalLoader } = this.props;
                        this.setState({ deleteSkuModalLoading: skuModalLoader, deleteModalActive: false },
                            () => {
                                this.getAllCloudSkuData();
                            })
                    } else {
                        let { skuModalLoader } = this.props;
                        this.setState({ deleteSkuModalLoading: skuModalLoader });
                    }
                }
            })
        }
    }
    deleteSkuModal = () => {
        this.setState({ deleteModalActive: false })
    }
    render() {
        const hasCoverError = this.state.coverRejectedFiles.length > 0;
        const hasCloudError = this.state.cloudRejectedFiles.length > 0;
        const coverErrorMessage = hasCoverError && <Banner title="Only xlsx file is allowed" status="critical"></Banner>
        const cloudErrorMessage = hasCloudError && <Banner title="Only xlsx file is allowed" status="critical"></Banner>
        return (
            <div>
                <Page>
                    <Layout>
                        <Layout.Section>
                            {/* <Card title="Cover SKU" actions={[{ content: 'Import', onAction: () => { this.coverSkuModalActive() } }, { content: 'Export', onAction: () => { this.coverSkuExport() } }]} sectioned>
                                {this.coverSkuListMarkup()}
                            </Card> */}
                            <Card title="Installation Required SKUs" actions={[{ content: 'Import', onAction: () => { this.cloudSkuModalActive() } }, { content: 'Export', onAction: () => { this.cloudSkuExport() } }]} sectioned>
                                {this.cloudSkuListMarkup()}
                            </Card>
                        </Layout.Section>
                    </Layout>
                    <Modal
                        title="Add File"
                        open={this.state.coverSkuActive}
                        onClose={this.coverSkuModalActive}
                        primaryAction={{
                            content: 'Save',
                            onAction: this.coverSkuSubmit,
                            disabled: this.state.isSaveCoverDisabled
                        }}
                        secondaryActions={{
                            content: 'Cancel',
                            onAction: this.coverSkuModalActive
                        }}
                    >
                        <Modal.Section>
                            <Stack >
                                {this.state.coverfiles !== '' ? (
                                    this.state.coverfiles.map((file, index) => (
                                        <Stack key={index}>
                                            <Icon source={NoteMajor} color="base" />
                                            <div>
                                                {file.name}<Caption>{file.size} bytes</Caption>
                                            </div>
                                        </Stack>
                                    ))
                                ) : ""}
                                {coverErrorMessage}
                            </Stack>
                            <DropZone
                                accept=".xlsx"
                                allowMultiple={true}
                                type="file"
                                error={true}
                                onDrop={this.handleCoverDrop}
                            >
                                <DropZone.FileUpload />
                            </DropZone>
                        </Modal.Section>
                    </Modal>
                    <Modal
                        title="Add File"
                        open={this.state.cloudSkuActive}
                        onClose={this.cloudSkuModalActive}
                        primaryAction={{
                            content: 'Save',
                            onAction: this.cloudSkuSubmit,
                            disabled: this.state.isSaveCloudDisabled
                        }}
                        secondaryActions={{
                            content: 'Cancel',
                            onAction: this.cloudSkuModalActive
                        }}
                    >
                        <Modal.Section>
                            <Stack >
                                {this.state.cloudfiles !== '' ? (
                                    this.state.cloudfiles.map((file, index) => (
                                        <Stack key={index}>
                                            <Icon source={NoteMajor} color="base" />
                                            <div>
                                                {file.name}<Caption>{file.size} bytes</Caption>
                                            </div>
                                        </Stack>
                                    ))
                                ) : ""}
                                {cloudErrorMessage}
                            </Stack>
                            <DropZone
                                accept=".xlsx"
                                allowMultiple={true}
                                type="file"
                                error={true}
                                onDrop={this.handleCloudDrop}
                            >
                                <DropZone.FileUpload />
                            </DropZone>
                        </Modal.Section>
                    </Modal>
                    <Modal
                        title={this.state.modalHeader}
                        open={this.state.deleteModalActive}
                        onClose={this.deleteSkuModal}
                        primaryAction={{
                            content: 'Yes',
                            loading: this.state.deleteSkuModalLoading,
                            onAction: this.deleteSkuData
                        }}
                        secondaryActions={{
                            content: 'No',
                            onAction: this.deleteSkuModal
                        }}
                    >
                        <Modal.Section>
                            <TextContainer>
                                <p>{'Are you sure you want to remove this?'}</p>
                            </TextContainer>
                        </Modal.Section>
                    </Modal>
                </Page>
            </div >
        )
    }
}
const mapStateToProps = (state) => ({
    cloudloading: state.settings.cloudloading,
    coverloading: state.settings.coverloading,
    cloudSkuListData: state.settings.cloudSkuListData,
    coverSkuListData: state.settings.coverSkuListData,
    coverExportResponse: state.settings.coverSkuExport,
    cloudExportResponse: state.settings.cloudSkuExport,
    skuModalLoader: state.settings.skuModalLoader
});
const mapDispatchToProps = (dispatch) => ({
    getCloudSkuList: (cloudSkuData) => dispatch(getCloudSkuListRequest(cloudSkuData)),
    getCoverSkuList: (coverSkuData) => dispatch(getCoverSkuListRequest(coverSkuData)),
    saveCloudSkuFile: (cloudSkuFile) => dispatch(saveCloudSkuFileRequest(cloudSkuFile)),
    saveCoverSkuFile: (coverSkuFile) => dispatch(saveCoverSkuFileRequest(coverSkuFile)),
    coverExport: (coverSkuExport) => dispatch(coverExportRequest(coverSkuExport)),
    cloudExport: (cloudSkuExport) => dispatch(cloudExportRequest(cloudSkuExport)),
    deleteCoverSku: (coverData) => dispatch(deleteCoverRequest(coverData)),
    deleteCloudSku: (cloudData) => dispatch(deleteCloudRequest(cloudData))
});
export default connect(mapStateToProps, mapDispatchToProps)(settings);