let { con, BASE_URL, WARRANTY_PRODUCTS } = require('./sqlConnection')
let axios = require('axios')

exports.shopifyOrders = async (store_id, order) => {
  let dataObj = {
    store_id: store_id,
    shopify_customer_id: order.customer ? order.customer.id : null,
    shopify_order_id: order.id,
    order_number: order.number,
    order_name: order.name,
    order_created_at: order.created_at,
    order_updated_at: order.updated_at,
    order_cancelled_at: order.cancelled_at,
    total_price: order.total_price,
    subtotal_price: order.subtotal_price,
    total_weight: order.total_weight,
    total_discount_amount: order.total_discounts,
    gateway: order.gateway,
    fulfillment_status: order.fulfillment_status || 'unfulfilled',
  }

  const createDate = new Date(dataObj.order_created_at).toISOString().slice(0, 19).replace('T', ' ');
  const updateDate = new Date(dataObj.order_updated_at).toISOString().slice(0, 19).replace('T', ' ');
  const cancelDate = new Date(dataObj.order_cancelled_at).toISOString().slice(0, 19).replace('T', ' ');


  con.query(`SELECT * FROM orders WHERE shopify_order_id = '${order.id}'`, async (err, orderResult) => {
    if (orderResult.length > 0) {
      con.query(`UPDATE orders SET store_id='${store_id}',shopify_customer_id='${dataObj.shopify_customer_id}',shopify_order_id='${dataObj.shopify_order_id}',order_number='${dataObj.order_number}',order_name='${dataObj.order_name}',order_created_at='${createDate}',order_updated_at='${updateDate}',order_cancelled_at='${cancelDate}',total_price='${dataObj.total_price}',subtotal_price='${dataObj.subtotal_price}',total_weight='${dataObj.total_weight}',total_discount_amount='${dataObj.total_discount_amount}',gateway='${dataObj.gateway}',fulfillment_status = '${dataObj.fulfillment_status}' WHERE shopify_order_id ='${order.id}' `, (err, res) => {
        if (err) throw err;
      })
    } else {

      con.query(`INSERT INTO orders (store_id,shopify_order_id,shopify_customer_id,order_number,order_name,order_created_at,order_updated_at,order_cancelled_at,total_price,subtotal_price,total_weight,total_discount_amount,gateway,fulfillment_status,createdAt,updatedAt) VALUES ('${store_id}','${dataObj.shopify_order_id}','${dataObj.shopify_customer_id}','${dataObj.order_number}','${dataObj.order_name}','${createDate}','${dataObj.order_updated_at}','${dataObj.order_cancelled_at}','${dataObj.total_price}','${dataObj.subtotal_price}','${dataObj.total_weight}','${dataObj.total_discount_amount}','${dataObj.gateway}','${dataObj.fulfillment_status}', '${createDate}', '${updateDate}')`, (err, res) => {
        if (err) throw err;

      })
    }
  })
}


exports.shopifyOrderItem = async (store_id, order) => {

  for (let lineItems of order.line_items) {
    con.query(`SELECT id FROM orders WHERE store_id='${store_id}' AND shopify_order_id = '${order.id}'`, async (err, result) => {
      for (let value of result) {
        let dataObj = {
          order_id: value.id,
          store_id: store_id,
          line_item_id: lineItems.id,
          quantity: lineItems.quantity,
          product_id: lineItems.product_id,
          variant_id: lineItems.variant_id,
          product_title: lineItems.title?.replace(/'/g, "\\'"),
          variant_title: lineItems.variant_title?.replace(/'/g, "\\'"),
          price: lineItems.price,
          sku: lineItems.sku?.replace(/'/g, "\\'")
        }
        const date = new Date().toISOString().slice(0, 19).replace('T', ' ');

        con.query(`SELECT * FROM order_items WHERE order_id = '${dataObj.order_id}' AND line_item_id='${dataObj.line_item_id}'`, async (err, orderItemResult) => {
          if (orderItemResult.length > 0) {
            con.query(`UPDATE order_items SET order_id='${value.id}',store_id='${store_id}',line_item_id='${dataObj.line_item_id}',quantity='${dataObj.quantity}',product_id='${dataObj.product_id}',variant_id='${dataObj.variant_id}',product_title='${dataObj.product_title}',variant_title = '${dataObj.variant_title}',price = '${dataObj.price}',sku = '${dataObj.sku}' WHERE Order_id ='${dataObj.order_id}' AND line_item_id = '${lineItems.id}'`, (err, res) => {
              if (err) throw err;
            })
          } else {
            con.query(`INSERT INTO order_items (order_id,store_id,line_item_id,quantity,product_id,variant_id,product_title,variant_title,price,sku,createdAt,updatedAt) VALUES ('${value.id}','${store_id}','${dataObj.line_item_id}','${dataObj.quantity}','${dataObj.product_id}','${dataObj.variant_id}','${dataObj.product_title}','${dataObj.variant_title}','${dataObj.price}','${dataObj.sku}','${date}', '${date}')`, (err, res) => {
              if (err) throw err;

            })
          }
        })
      }
    })
  }
}

exports.shopifyOrderCustomer = async (store_id, order) => {

  // con.query(`SELECT id FROM orders WHERE store_id='${store_id}' AND shopify_order_id = '${order.id}'`, async (err, result) => {
  //   for (let value of result) {
  let dataObj = {
    store_id: store_id,
    first_name: order.customer && order.customer.first_name ? order.customer.first_name.replace(/'/g, "\\'") : null,
    last_name: order.customer && order.customer.last_name ? order.customer.last_name.replace(/'/g, "\\'") : null,
    accepts_marketing: order.customer ? order.customer.accepts_marketing : null,
    customer_id: order.customer ? order.customer.id : null,
    order_id: order.id,
    customer_email: order.customer ? order.customer.email : null,
    phone_number: order?.customer?.phone || order?.billing_address?.phone || null
  }
  const date = new Date().toISOString().slice(0, 19).replace('T', ' ');
  con.query(`SELECT * FROM order_customers WHERE order_id = '${dataObj.order_id}'`, async (err, customerResult) => {
    if (customerResult.length > 0) {
      con.query(`UPDATE order_customers SET store_id='${store_id}',first_name='${dataObj.first_name}',last_name='${dataObj.last_name}',accepts_marketing='${dataObj.accepts_marketing}',customer_id='${dataObj.customer_id}',order_id='${dataObj.order_id}',customer_email = '${dataObj.customer_email}',phone_number = '${dataObj.phone_number}' WHERE Order_id ='${dataObj.order_id}' `, (err, res) => {
        if (err) throw err;
      })
    } else {
      con.query(`INSERT INTO order_customers (store_id,first_name,last_name,accepts_marketing,customer_id,order_id,customer_email,phone_number,createdAt,updatedAt) VALUES ('${store_id}','${dataObj.first_name}','${dataObj.last_name}','${dataObj.accepts_marketing}','${dataObj.customer_id}','${dataObj.order_id}','${dataObj.customer_email}','${dataObj.phone_number}','${date}','${date}')`, (err, res) => {
        if (err) throw err;

      })
    }
  })
  //   }
  // })
}




exports.shopifyOrderAddress = async (store_id, order, billingName) => {
  const escapeSQL = (value) => {
    if (typeof value === 'string') {
      return value.replace(/\\/g, '\\\\').replace(/'/g, "''");
    }
    return value;
  };

  let dataObj = {
    is_shipping_address: order?.shipping_address && Object.keys(order.shipping_address).length ? '1' : '0',
    store_id: store_id,
    address1: escapeSQL(order.shipping_address?.address1),
    address2: escapeSQL(order.shipping_address?.address2),
    city: escapeSQL(order.shipping_address?.city),
    company: escapeSQL(order.shipping_address?.company),
    country: escapeSQL(order.shipping_address?.country),
    first_name: escapeSQL(order.shipping_address?.first_name),
    last_name: escapeSQL(order.shipping_address?.last_name),
    name: escapeSQL(order.shipping_address?.name),
    latitude: order.shipping_address?.latitude,
    longitude: order.shipping_address?.longitude,
    order_id: order.id,
    phone: order.shipping_address?.phone,
    province: order.shipping_address?.province,
    province_code: order.shipping_address?.province_code,
    zip_code: order.shipping_address?.zip,
    country_code: order.shipping_address?.country_code,
    billing_address1: escapeSQL(order.billing_address?.address1),
    billing_address2: escapeSQL(order.billing_address?.address2),
    billing_city: escapeSQL(order.billing_address?.city),
    billing_company: escapeSQL(billingName) || escapeSQL(order.billing_address?.company),
    billing_country: escapeSQL(order.billing_address?.country),
    billing_first_name: escapeSQL(order.billing_address?.first_name),
    billing_last_name: escapeSQL(order.billing_address?.last_name),
    billing_name: escapeSQL(order.billing_address?.name),
    billing_phone: order.billing_address?.phone,
    billing_province: order.billing_address?.province,
    billing_province_code: order.billing_address?.province_code,
    billing_zip_code: order.billing_address?.zip,
    billing_country_code: order.billing_address?.country_code,
  };

  const date = new Date().toISOString().slice(0, 19).replace('T', ' ');

  con.query(`SELECT * FROM order_addresses WHERE order_id = '${dataObj.order_id}'`, async (err, Addressresult) => {
    if (Addressresult.length > 0) {
      con.query(`UPDATE order_addresses
        SET store_id='${store_id}', address1='${dataObj.address1}', address2='${dataObj.address2}',
        city='${dataObj.city}', company='${dataObj.company}', country='${dataObj.country}',
        first_name='${dataObj.first_name}', last_name='${dataObj.last_name}', name='${dataObj.name}', is_shipping_addresss='${dataObj.is_shipping_addresss}',
        latitude='${dataObj.latitude}', longitude='${dataObj.longitude}', order_id='${dataObj.order_id}', phone='${dataObj.phone}', province='${dataObj.province}',
        province_code='${dataObj.province_code}', zip_code='${dataObj.zip_code}', country_code='${dataObj.country_code}' 
        WHERE order_id='${dataObj.order_id}' `, (err, res) => {
        if (err) throw err;
      });
    } else {
      con.query(`INSERT INTO order_addresses (store_id, address1, billing_address1, address2, billing_address2, city, billing_city, company, billing_company, country, billing_country, first_name, billing_first_name, last_name, billing_last_name, name, billing_name, is_shipping_addresss, latitude, longitude, order_id, phone, billing_phone, province, billing_province, province_code, billing_province_code, zip_code, billing_zip_code, country_code, billing_country_code, createdAt, updatedAt) VALUES 
        ('${store_id}','${dataObj.address1}','${dataObj.billing_address1}','${dataObj.address2}','${dataObj.billing_address2}','${dataObj.city}','${dataObj.billing_city}','${dataObj.company}','${dataObj.billing_company}','${dataObj.country}','${dataObj.billing_country}','${dataObj.first_name}','${dataObj.billing_first_name}','${dataObj.last_name}','${dataObj.billing_last_name}','${dataObj.name}','${dataObj.billing_name}','${dataObj.is_shipping_addresss}',
        '${dataObj.latitude}','${dataObj.longitude}','${dataObj.order_id}', '${dataObj.phone}','${dataObj.billing_phone}','${dataObj.province}','${dataObj.billing_province}','${dataObj.province_code}','${dataObj.billing_province_code}','${dataObj.zip_code}','${dataObj.billing_zip_code}','${dataObj.country_code}','${dataObj.billing_country_code}','${date}', '${date}')`, (err, res) => {
        if (err) throw err;
      });
    }
  });
};

exports.shopifyOrderSplit = async (store_id, order, shop, token) => {
  try {
    let giftCardValue = 0;
    let note_attributes = order.note_attributes;
    let billCode = order?.billing_address?.province
    let shipCode = order?.shipping_address?.province
    let gstnNote = note_attributes?.find(notes => notes.name == "gstn" || notes.name == "GSTIN");
    let gstn = gstnNote ? gstnNote.value : null;
    let object = { gstin: gstn, billCode: billCode, shipCode: shipCode }
    let isValid = gstn ? await callForGstValidation(object, shop) : { isOrderHold: '0', isGstValid: '1' }
    let paymetGateway = "RAZORPAY"
    if (order.payment_gateway_names.includes('gift_card')) {
      giftCardValue = await manageGiftCards(order, shop, token);
    }
    if (order.payment_gateway_names.includes('snapmint')) {
      paymetGateway = "SNAPMINT"
    }

    const warrantySKUs = WARRANTY_PRODUCTS;

    const extendedWarrantyLineItems = order.line_items.filter(item => warrantySKUs.includes(item.sku));
    const othersLineItems = order.line_items.filter(item => !warrantySKUs.includes(item.sku));

    let { currentOrderIndex, updatedTemp } = await processLineItems(othersLineItems, { store_id, gstn, order, shop, token, giftCardValue, paymetGateway, isValid });
    await processextendedWarrantyLineItems(extendedWarrantyLineItems, { order, paymetGateway, startingIndex: currentOrderIndex });

    return { temp: updatedTemp, billingCompany: isValid?.billingCompanyName || null };
  } catch (error) {
    console.error('Error shopifyOrderSplit:', error);
    return { temp: false }
  }
}

async function processLineItems(lineItems, context) {
  try {
    const { store_id, gstn, order, shop, token, giftCardValue, paymetGateway, isValid } = context;
    if (isValid?.isGstValid === "1" && isValid?.isOrderHold === "1") {
      isValid.isOrderHold = "0"
    }
    let j = 1;
    let temp = false;
    if (lineItems.length === 0) {
      return { currentOrderIndex: j, updatedTemp: true };
    }
    for (let value of lineItems) {
      let fulfillmentLocation = await checkOrderLocation(shop, token, order.id, value.id);
      if (fulfillmentLocation !== "SUCCESS") {
        continue;
      } else {
        temp = true;
      }

      for (let i = 1; i <= value.quantity; i++) {
        const mainProductUniqID = value.properties.find((prop) => prop.name === '_bid');

        let dataObj = {
          store_id: store_id,
          shopify_order_id: order.id,
          line_item_id: value.id,
          shopify_customer_id: order.customer ? order.customer.id : null,
          shopify_variant_id: value.variant_id,
          shopify_product_id: value.product_id,
          order_number: order.number,
          gateway: paymetGateway, // order.gateway,
          shopify_order_name: order.name,
          order_name: order.name + '-' + j,
          order_created_at: order.created_at,
          order_amount: value.price,
          product_title: value.title.replace(/'/g, "\\'"),
          sku: value.sku?.replace(/'/g, "\\'"),
          quantity: 1,
          financial_status: order.financial_status,
          tax: null,
          discount: null,
          tax_percentage: null,
          gift_card_value: giftCardValue,
          extended_warranty_unique_id: mainProductUniqID && mainProductUniqID.value ? `'${mainProductUniqID.value}'` : 'NULL',
        };

        for (let tax of value.tax_lines) {
          let taxPercentage = tax.rate * 100;
          dataObj.tax_percentage = taxPercentage;
          if (value.quantity > 1) {
            let newTax = tax.price / value.quantity;
            dataObj.tax = parseFloat(newTax).toFixed(2);
          } else {
            dataObj.tax = tax.price;
          }
        }
        for (let discount of value.discount_allocations) {
          if (value.quantity > 1) {
            let newDiscount = discount.amount / value.quantity;
            dataObj.discount = parseFloat(newDiscount).toFixed(2);
          } else {
            dataObj.discount = discount.amount;
          }
        }

        const getOrderSplitResult = await executeQuery(`SELECT * FROM order_item_splits WHERE shopify_order_id = '${dataObj.shopify_order_id}'AND order_name =  '${dataObj.order_name}' AND line_item_id = '${dataObj.line_item_id}'`);
        const date = new Date(dataObj.order_created_at).toISOString().slice(0, 19).replace('T', ' ');
        if (getOrderSplitResult.length > 0) {
          await executeQuery(`UPDATE order_item_splits SET financial_status='${dataObj.financial_status}',store_id='${store_id}',shopify_order_id='${dataObj.shopify_order_id}',line_item_id='${dataObj.line_item_id}',shopify_customer_id='${dataObj.shopify_customer_id}',shopify_variant_id='${dataObj.shopify_variant_id}',shopify_product_id='${dataObj.shopify_product_id}',order_number='${dataObj.order_number}',shopify_order_name='${dataObj.shopify_order_name}',order_name='${dataObj.order_name}',order_created_at='${date}',order_amount='${dataObj.order_amount}',gateway='${dataObj.gateway}',product_title='${dataObj.product_title}',sku='${dataObj.sku}',quantity='${dataObj.quantity}',tax='${dataObj.tax}',tax_percentage='${dataObj.tax_percentage}',discount='${dataObj.discount}',gift_card_value='${dataObj.gift_card_value}' WHERE shopify_order_id = '${dataObj.shopify_order_id}' AND order_name = '${dataObj.order_name}'AND line_item_id = '${dataObj.line_item_id}' AND extended_warranty_unique_id = ${dataObj.extended_warranty_unique_id} `);
        } else {
          await executeQuery(`INSERT INTO order_item_splits (store_id,shopify_order_id,is_gst_Valid,is_order_hold,shopify_customer_id,shopify_variant_id,shopify_product_id,line_item_id,order_number,shopify_order_name,order_name,gateway,order_created_at,order_amount,product_title,sku,tax,discount,tax_percentage,quantity,financial_status,gstin,createdAt,updatedAt,gift_card_value,extended_warranty_unique_id) VALUES ('${store_id}',
            '${dataObj.shopify_order_id}',
            '${isValid.isGstValid}',
            '${isValid.isOrderHold}',
            '${dataObj.shopify_customer_id}',
            '${dataObj.shopify_variant_id}',
            '${dataObj.shopify_product_id}',
            '${dataObj.line_item_id}',
            '${dataObj.order_number}',
            '${dataObj.shopify_order_name}',
            '${dataObj.order_name}',
            '${dataObj.gateway}',
            '${date}',
            '${dataObj.order_amount}',
            '${dataObj.product_title}', 
            '${dataObj.sku}','${dataObj.tax}',
            '${dataObj.discount}',
            '${dataObj.tax_percentage}',
            '${dataObj.quantity}',
            '${dataObj.financial_status}',
            '${gstn}','${date}', '${date}', 
            '${dataObj.gift_card_value}', 
            ${dataObj.extended_warranty_unique_id})`);
        }
        j++;
      }
    }
    return { currentOrderIndex: j, updatedTemp: temp };
  } catch (error) {
    console.error('Error processLineItems:', error);
  }
}

async function processextendedWarrantyLineItems(lineItems, context) {
  try {
    const { order, paymetGateway, startingIndex } = context;
    let j = startingIndex;
    for (let value of lineItems) {
      for (let i = 1; i <= value.quantity; i++) {
        let dataObj = {
          shopify_order_id: order.id,
          line_item_id: value.id,
          shopify_variant_id: value.variant_id,
          shopify_product_id: value.product_id,
          gateway: paymetGateway, // order.gateway,
          shopify_order_name: order.name,
          order_name: order.name + '-' + j,
          order_created_at: order.created_at,
          order_amount: value.price,
          sku: value.sku?.replace(/'/g, "\\'"),
          quantity: 1,
          tax: null,
          discount: null,
        };
        for (let tax of value.tax_lines) {
          let taxPercentage = tax.rate * 100;
          dataObj.tax_percentage = taxPercentage;
          if (value.quantity > 1) {
            let newTax = tax.price / value.quantity;
            dataObj.tax = parseFloat(newTax).toFixed(2);
          } else {
            dataObj.tax = tax.price;
          }
        }
        for (let discount of value.discount_allocations) {
          if (value.quantity > 1) {
            let newDiscount = discount.amount / value.quantity;
            dataObj.discount = parseFloat(newDiscount).toFixed(2);
          } else {
            dataObj.discount = discount.amount;
          }
        }

        const getExtendedWarrantyOrder = await executeQuery(`SELECT * FROM extended_warranty_orders WHERE shopify_order_id = '${dataObj.shopify_order_id}'AND order_name =  '${dataObj.order_name}' AND line_item_id = '${dataObj.line_item_id}'`);
        const date = new Date(dataObj.order_created_at).toISOString().slice(0, 19).replace('T', ' ');
        if (getExtendedWarrantyOrder.length > 0) {
          await executeQuery(`UPDATE extended_warranty_orders SET shopify_order_id='${dataObj.shopify_order_id}',line_item_id='${dataObj.line_item_id}',shopify_variant_id='${dataObj.shopify_variant_id}',shopify_product_id='${dataObj.shopify_product_id}',shopify_order_name='${dataObj.shopify_order_name}',order_name='${dataObj.order_name}',order_created_at='${date}',order_amount='${dataObj.order_amount}',gateway='${dataObj.gateway}',sku='${dataObj.sku}',tax='${dataObj.tax}',discount='${dataObj.discount}'WHERE shopify_order_id = '${dataObj.shopify_order_id}' AND order_name = '${dataObj.order_name}'AND line_item_id = '${dataObj.line_item_id}'`);
        } else {
          await executeQuery(`INSERT INTO extended_warranty_orders (shopify_order_id,shopify_variant_id,shopify_product_id,line_item_id,shopify_order_name,order_name,gateway,order_created_at,order_amount,sku,tax,discount,createdAt,updatedAt) VALUES (
          '${dataObj.shopify_order_id}',
          '${dataObj.shopify_variant_id}',
          '${dataObj.shopify_product_id}',
          '${dataObj.line_item_id}',
          '${dataObj.shopify_order_name}',
          '${dataObj.order_name}',
          '${dataObj.gateway}',
          '${date}',
          '${dataObj.order_amount}',
          '${dataObj.sku}','${dataObj.tax}',
          '${dataObj.discount}',
          '${date}', '${date}')`);
        }
        await updateProductWarranty(value, dataObj);

        j++;
      }
    }
  } catch (error) {
    console.error('Error processextendedWarrantyLineItems:', error);
  }
}


async function updateProductWarranty(value, dataObj) {
  const mainProductUniqID = value.properties.find((prop) => prop.name === '_bid');
  const serialNumber = value.properties.find((prop) => prop.name === '_serial_number');

  try {
    if (mainProductUniqID) {
      const updateMainProduct = await executeQuery(`UPDATE order_item_splits SET is_extended_warranty='1' WHERE extended_warranty_unique_id = '${mainProductUniqID.value}' AND shopify_order_id = '${dataObj.shopify_order_id}' AND is_extended_warranty = '0' LIMIT 1`);
      if (updateMainProduct.affectedRows > 0) {
        const getUpdatedMainProduct = await executeQuery(`SELECT order_name, id FROM order_item_splits WHERE extended_warranty_unique_id = '${mainProductUniqID.value}' AND shopify_order_id = '${dataObj.shopify_order_id}' AND is_extended_warranty = '1' LIMIT 1`);
        if (getUpdatedMainProduct.length > 0) {
          const updateWarrantyProduct = await executeQuery(`UPDATE extended_warranty_orders SET main_order_name = '${getUpdatedMainProduct[0]?.order_name}',order_item_split_id = '${getUpdatedMainProduct[0]?.id}' WHERE shopify_order_id = '${dataObj.shopify_order_id}' AND order_name ='${dataObj.order_name}' AND line_item_id = '${dataObj.line_item_id}' AND order_item_split_id IS NULL`);
          if (updateWarrantyProduct.affectedRows > 0) {
            await executeQuery(`UPDATE order_item_splits SET extended_warranty_unique_id = null WHERE id = '${getUpdatedMainProduct[0]?.id}' AND shopify_order_id = '${dataObj.shopify_order_id}' LIMIT 1`);
          }
        }
      }
    } else if (serialNumber) {
      const updateMainProduct = await executeQuery(`UPDATE order_item_splits SET is_extended_warranty='1' WHERE product_serial_number = '${serialNumber.value}' AND is_extended_warranty = '0'`);
      if (updateMainProduct.affectedRows > 0) {
        const getUpdatedMainProduct = await executeQuery(`SELECT order_name, id FROM order_item_splits WHERE product_serial_number = '${serialNumber.value}' AND is_extended_warranty = '1'`);
        if (getUpdatedMainProduct.length > 0) {
          await executeQuery(`UPDATE extended_warranty_orders SET main_order_name = '${getUpdatedMainProduct[0]?.order_name}', order_item_split_id = '${getUpdatedMainProduct[0]?.id}' WHERE shopify_order_id = '${dataObj.shopify_order_id}' AND order_name ='${dataObj.order_name}' AND line_item_id ='${dataObj.line_item_id}' AND order_item_split_id IS NULL`);
        }
      }
    } else {
      console.log('No main product or serial number found for line item:', value.id);
    }
  } catch (error) {
    console.error('Error processing warranty update:', error);
  }
}


const callForGstValidation = async (gstObj, myshopifyDomain) => {
  try {
    let validResp = await axios.post(`${BASE_URL}/gst/validate?shop=${myshopifyDomain}`, gstObj);
    let billingCompanyName = validResp?.data?.status != '0' ? validResp?.data?.companyName : null
    return { isOrderHold: '1', isGstValid: validResp?.data?.status ? validResp?.data?.status : '0', billingCompanyName }
  } catch (error) {
    console.log("Error call For Gst Validation", error)
    return { isOrderHold: '1', isGstValid: '0', billingCompanyName: null }
  }
}

const checkOrderLocation = async (shop, token, order_id, line_item_id) => {
  try {
    const path = `https://${shop}/admin/api/2021-10/orders/${order_id}/fulfillment_orders.json`;
    let { data, error } = await axios.get(path, {
      headers: {
        'X-Shopify-Access-Token': token,
        'Content-Type': 'application/json'
      }
    })
    if (error || !data || !data.fulfillment_orders || data.fulfillment_orders.length < 1) return "FAILED"
    for (const fulfillment_order of data.fulfillment_orders) {
      if (fulfillment_order.assigned_location_id == "68148330553" || fulfillment_order.assigned_location_id == "68155342905") {
        let fulfillment_items = fulfillment_order.line_items
        for (const item of fulfillment_items) {
          if (item.line_item_id == line_item_id) {
            return "FAILED"
          }
        }
      }
    }
    return "SUCCESS"
  } catch (error) {
    console.log("check order location", error)
    return "FAILED"
  }
}

const manageGiftCards = async (order, shop, token) => {
  try {
    let response = await axios.get(`https://${shop}/admin/api/2021-10/orders/${order.id}/transactions.json`, {
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': `${token}`
      }
    })
    // if (!response)  handle error
    let totalTransactionAmount = 0;
    for (const transaction of response?.data?.transactions) {
      //if not success handle it
      if (transaction.status == 'success' && transaction.gateway == 'gift_card') {
        totalTransactionAmount = parseFloat(transaction.amount)
      }
    }
    let toalOrderAmount = order.current_subtotal_price
    let giftCardValue = parseFloat(totalTransactionAmount / toalOrderAmount)
    return truncateDecimals(giftCardValue, 4)
  } catch (error) {
    console.log("manage gift card error", error);
  }
}

function truncateDecimals(number, digits) {
  let strNumber = number.toString();
  let decimalIndex = strNumber.indexOf('.');
  if (decimalIndex !== -1) {
    // If the number has a decimal part
    return parseFloat(strNumber.slice(0, decimalIndex + digits + 1));
    // slice(0, decimalIndex + digits + 1) to include the dot and the desired number of decimal places
  } else {
    // If the number is an integer
    return number;
  }
}

const executeQuery = (query, queryValues = []) => {
  return new Promise((resolve, reject) => {
    con.query(query, queryValues, (error, results) => {
      if (error) {
        console.log('Error in executing query === ', query + ': ', error);
        return reject(error); // Reject with the error if one occurs
      }
      resolve(results); // Resolve with the query results
    });
  });
};