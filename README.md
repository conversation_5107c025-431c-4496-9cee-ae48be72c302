# Symphony Middleware

## Overview

Symphony Middleware is a Shopify app designed to automate order processing efficiently. The project consists of multiple components, including Node.js APIs, React frontend code, and Shopify order webhooks. This document outlines the project structure, setup instructions, and deployment steps.

---

## Project Directory Structure

```
- src
  - delhivery-webhook
  - node
  - react
  - react-track-order
  - webhook
.env
.gitignore
.gitlab-ci.yml
package.json
README.md
webpack.config.js
```

### Description of Key Directories
- **`src/react`**: Contains React code for the frontend.
- **`src/node`**: Contains Node.js API code.
- **`src/webhook`**: Contains Shopify order webhook logic.

---

## Environment Variables

The project supports the following environment variables:

- **`NODE_ENV`**: Specifies the environment (`local`, `development`, `production`).
  - `local`: Used for the local server.
  - `development`: Used for the development server.
  - `production`: Used for the production server.

---

## Local Setup

### Prerequisites
1. Setup the database.

### Steps

1. **Setup Node.js Backend:**
   - Navigate to the Node.js code directory:
     ```bash
     cd src/node
     ```
   - Install dependencies:
     ```bash
     npm install
     ```
   - Start the Node.js server:
     ```bash
     npm run server
     ```

2. **Setup React Frontend:**
   - Navigate to the root directory:
     ```bash
     cd ../..
     ```
   - Install React dependencies:
     ```bash
     npm install
     ```
   - Start the React frontend:
     ```bash
     npm run client
     ```

3. **Setup Webhooks (if needed):**
   - Update the webhook URL in the configuration file:
     ```
     src/node/config/*
     ```
   - Navigate to the webhook directory:
     ```bash
     cd src/webhook
     ```
   - Install dependencies:
     ```bash
     npm install
     ```
   - Run the webhook server:
     ```bash
     cd ../.. && npm run webhook
     ```

Once completed, your local server should be running successfully.

---

## Deployment

### Staging Server

#### Webhook Code
1. Update the database credentials for the staging server.
2. Create a zip file of the `src/webhook` code.
3. Deploy the zip file to the Elastic Beanstalk staging server.

#### React and Backend Code
1. Build the code for staging:
   ```bash
   npm run build:staging
   ```
   - This command generates the build in the `src/node` directory.
2. Update the required credentials.
3. Create a zip file of the built code and deploy it to the staging server.

### Production Server

#### Webhook Code
1. Update the database credentials for the production server.
2. Create a zip file of the `src/webhook` code.
3. Deploy the zip file to the Elastic Beanstalk production server.

#### React and Backend Code
1. Build the code for production:
   ```bash
   npm run build
   ```
2. Update the required credentials.
3. Create a zip file of the built code and deploy it to the production server.

---

## Notes
- Always ensure the correct database credentials are configured for the target environment.
- For webhook configuration, update the URL in the configuration file before deploying.
- Use proper commands for building and running the project in respective environments.

