import React from "react";
import { Navigation } from "@shopify/polaris";
import {
  OrdersMajor,
  SettingsMajor,
  TransactionMajor,
  RedoMajor,
  HomeMajor,
} from "@shopify/polaris-icons";
import { withRouter } from "react-router-dom";

class NavigationMarkup extends React.Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  changeNavigation = (url) => {
    this.props.history.push(url);
  };

  render() {
    return (
      <Navigation location="/">
        <Navigation.Section
          items={[
            {
              label: "Dashboard",
              icon: HomeMajor,
              onClick: () => this.changeNavigation("/app/dashboard"),
            },
            {
              label: "One Click Push",
              icon: RedoMajor,
              onClick: () => this.changeNavigation("/app/oneClick"),
            },
            {
              label: "Custom Funnels",
              icon: OrdersMajor,
              onClick: () => this.changeNavigation("/app/custom"),
            },
            {
              label: "Pricing Plan",
              icon: SettingsMajor,
              onClick: () => this.changeNavigation("/app/pricePlan"),
            }
          ]}
        
        />
      </Navigation>
    );
  }
}

export default withRouter(NavigationMarkup);
