const Sequelize = require("sequelize");
module.exports = (sequelize) => {
  return sequelize.define("cfa_types", {
    id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      unique: true
    },
    store_id: {
      allowNull:false,
      type: Sequelize.INTEGER,
      references: {
        model: 'stores',
        key: 'id'
      },
    },
    cfa_id: {
      type: Sequelize.INTEGER,
      references: {
        model: 'cfa_locations',
        key: 'id'
      },
    },
    types: {
      type: Sequelize.STRING
    }
  },
    {
      indexes: [
        {
          unique: true,
          fields: ['store_id', 'cfa_id','types']
        }
      ]
    });
};