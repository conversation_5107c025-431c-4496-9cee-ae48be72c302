import { cfaTypes } from './cfaTypes';

export const getCfaListRequest = (data) => ({
    type: cfaTypes.GET_CFA_LIST_REQUEST,
    payload: data
})

export const getCfaListSuccess = (data) => ({
    type: cfaTypes.GET_CFA_LIST_SUCCESS,
    payload: data,
});

export const getManageCfaListRequest = (data) => ({
    type: cfaTypes.GET_MANAGE_CFA_LIST_REQUEST,
    payload: data
})

export const getManageCfaListSuccess = (data) => ({
    type: cfaTypes.GET_MANAGE_CFA_LIST_SUCCESS,
    payload: data,
});

export const saveCfaFileRequest = (data) => ({
    type: cfaTypes.SEND_CFA_FILE_REQUEST,
    payload: data
})

export const saveCfaFileSuccess = (data) => ({
    type: cfaTypes.SEND_CFA_FILE_SUCCESS,
    payload: data,
});

export const saveCfaFileError = (data) => ({
    type: cfaTypes.SEND_CFA_FILE_ERROR,
    payload: data,
});
export const saveManageCfaFileRequest = (data) => ({
    type: cfaTypes.SEND_MANAGE_CFA_FILE_REQUEST,
    payload: data
})

export const saveManageCfaFileSuccess = (data) => ({
    type: cfaTypes.SEND_MANAGE_CFA_FILE_SUCCESS,
    payload: data,
});

export const saveManageCfaFileError = (data) => ({
    type: cfaTypes.SEND_MANAGE_CFA_FILE_ERROR,
    payload: data,
});

export const getMannualCFAListRequest = (data) => ({
    type: cfaTypes.GET_MANNUAL_CFA_LIST_REQUEST,
    payload: data
})
export const getMannualCFAListSuccess = (data) => ({
    type: cfaTypes.GET_MANNUAL_CFA_LIST_SUCCESS,
    payload: data
})

export const pincodeExportRequest = (data) => ({
    type: cfaTypes.PINCODE_EXPORT_REQUEST,
    payload: data
});

export const pincodeExportSuccess = (data) => ({
    type: cfaTypes.PINCODE_EXPORT_SUCCESS,
    payload: data
});

export const pincodeExportError = (data) => ({
    type: cfaTypes.PINCODE_EXPORT_ERROR,
    payload: data
});

export const exportCfaStockRequest = (data) => ({
    type: cfaTypes.CFASTOCK_EXPORT_REQUEST,
    payload: data
});

export const exportCfaStockSuccess = (data) => ({
    type: cfaTypes.CFASTOCK_EXPORT_SUCCESS,
    payload: data
});

export const exportCfaStockError = (data) => ({
    type: cfaTypes.CFASTOCK_EXPORT_ERROR,
    payload: data
});

export const exportAllCfaRequest = (data) => ({
    type: cfaTypes.EXPORT_CFA_REQUEST,
    payload: data
});

export const exportAllCfaSuccess = (data) => ({
    type: cfaTypes.EXPORT_CFA_SUCCESS,
    payload: data
});

export const exportAllCfaError = (data) => ({
    type: cfaTypes.EXPORT_CFA_ERROR,
    payload: data
});