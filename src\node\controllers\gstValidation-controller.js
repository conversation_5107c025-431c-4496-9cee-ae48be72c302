const CONFIG = require('../config/config_' + [process.env.NODE_ENV || 'local'] + '.json');
const axios = require('axios')
const randomString = require('randomstring')
const response = require('../helper/appResponse')
var os = require('os');
let base64 = require('base-64');
const dbConnection = require("../models");
const sapController = require("./sap_controller")
const orderHistoryHelper = require("../helper/orderHistory")
const { Op, QueryTypes, where } = require('sequelize');
const { defaultindiaLocations } = require('../helper/helper');

function getServerIp() {
  var ifaces = os.networkInterfaces();
  var values = Object.keys(ifaces).map(function (name) {
    return ifaces[name];
  });
  values = [].concat.apply([], values).filter(function (val) {
    return val.family == 'IPv4' && val.internal == false;
  });

  return values.length ? values[0].address : '0.0.0.0';
}

const options = {
  headers: {
    "clientid": CONFIG.gst.client_id, //need to use encode id
    "client-secret": CONFIG.gst.client_secret, //need to use encode secret
    "ip-usr": CONFIG.gst.ip_usr,
    "txn": randomString.generate({ length: 16, charset: 'alphanumeric' }),
    'Content-Type': 'application/json'
  }
}
exports.gstValidate = async (req, res) => {
  let successMessage = response.responseMessage(CONFIG.status.FAILED, CONFIG.msg.FAILED)
  let statusCode = 500
  try {
    const shop = req.query.shop
    if (!shop) throw Error('Missing parameters')

    const shopResponse = await dbConnection.shop.findOne({ where: { myshopify_domain: shop } })
    if (!shopResponse) throw Error('No Data Found')
    console.log("req.body", req.body)
    let isValid = await callToValidateGst(req.body)
    if (isValid.status) {
      statusCode = CONFIG.status.SUCCESS
      successMessage.status = isValid.isMatch
      successMessage.message = CONFIG.msg.SUCCESS
      successMessage.companyName = isValid.data.data.lgnm
    } else {
      statusCode = CONFIG.status.SUCCESS
      successMessage.status = 0
      successMessage.message = "Unauthorized User"
    }

  } catch (err) {
    console.log("Error gst validate===>", err)
    successMessage.status = 0
    successMessage.message = CONFIG.msg.ERROR
  }
  res.status(statusCode).send(successMessage)
}

const callToValidateGst = async (gstObj) => {
  try {
    const apiUrl = `${CONFIG.gst.url}?gstin=${gstObj.gstin}&action=TP`
    let gstRes = await axios.get(apiUrl, options);
    if (gstRes.data.status_cd == "1" && gstRes.data?.data?.sts == "Active") {
      let stateCode = gstRes.data?.data?.pradr?.addr?.stcd.toLowerCase();
      if (stateCode == gstObj.billCode?.toLowerCase()) {
        return { status: true, isMatch: '1', data: gstRes?.data }
      } else {
        return { status: true, isMatch: '2', data: gstRes?.data }
      }
    } else {
      return { status: false }
    }
  } catch (error) {
    return { status: false }
  }
}
// let authenticate = async () => {
//     return new Promise(async (resolve,reject) => {
//         try{
//             const appUrl = ""
//             const options = {
//                 headers:{
//                     "client-id":CONFIG.gst.client_id,
//                     "client-secret":CONFIG.gst.client_secret,
//                     "Gstin":CONFIG.gst.gstin
//                 }
//             }
//             const payload = {
//                 "action":CONFIG.gst.token,
//                 "username":CONFIG.gst.username,
//                 "password":CONFIG.gst.password,
//                 "app_key": randomString.generate({ length: 12, charset: 'alphanumeric' })
//             }
//             const authRes = await axios.post(appUrl,payload,options)
//             if(authRes.data.status == "1"){
//                 resolve({status:true,authtoken:authRes.data.authtoken})
//             }else{
//                 throw new Error({status:false})
//             }
//         }catch(err){
//             reject(err)
//         }
//     })
// }

// get customerMaster request
exports.getCustomerMasterRequest = (orderData) => {
  return new Promise(async (resolve, reject) => {
    let data = []
    let orderCustomer = orderData.orderCustomers.length > 0 ? orderData.orderCustomers[0] : null
    let orderAddress = orderData.orderAddresses.length > 0 ? orderData.orderAddresses[0] : null
    let shippingRegionCode = ""
    let streetAddress
    if (orderAddress) {
      streetAddress = sapController.getStreetAddress(orderData.orderAddresses)
      let province = orderAddress.billing_province != null && orderAddress.billing_province != "" && orderAddress.billing_province != "null" ? orderAddress.billing_province : ""
      if (province) {
        let state = await dbConnection.stateRegionCode.findOne({
          where: {

            [Op.or]: [{
              new_state: province
            },
            {
              state: province
            }]

          }
        })
        if (state) {
          shippingRegionCode = state.dataValues.region_code
        } else {
          console.log("Province Error==>")
        }
      }
    }

    const formatStreet = (street) => {
      if (!street || street.trim() === "") return "..."; // If empty, set to "..."
      return street.length < 3 ? street.padEnd(3, '.') : street; // Ensure minimum 3 characters
    };

    const body = {
      "NAME1": orderAddress ? orderAddress.billing_company != null && orderAddress.billing_company != "null" && orderAddress.billing_company != '' ? orderAddress.billing_company : "" : "",
      "NAME2": orderAddress ? orderAddress.billing_company != null && orderAddress.billing_company != "null" && orderAddress.billing_company != '' ? orderAddress.billing_company : "" : "",
      "NAME3": orderAddress ? orderAddress.billing_company != null && orderAddress.billing_company != "null" && orderAddress.billing_company != '' ? orderAddress.billing_company : "" : "",
      "NAME4": orderAddress ? orderAddress.billing_company != null && orderAddress.billing_company != "null" && orderAddress.billing_company != '' ? orderAddress.billing_company : "" : "",
      "SEARCH_TERM1": orderAddress ? orderAddress.billing_company != null && orderAddress.billing_company != "null" && orderAddress.billing_company != '' ? orderAddress.billing_company : "" : "",
      "SEARCH_TERM2": "",
      "STREET": streetAddress.shippingStreet,
      "STREET2": formatStreet(streetAddress.shippingStreet1),
      "STREET3": streetAddress.shippingStreet2,
      "STREET4": streetAddress.shippingStreet3,
      "STREET5": "",
      "CITY": orderAddress ? orderAddress.billing_city != null && orderAddress.billing_city != "null" ? orderAddress.billing_city : "" : "",
      "PINCODE": orderAddress ? orderAddress.billing_zip_code != null && orderAddress.billing_zip_code != "null" ? orderAddress.billing_zip_code : "" : "",
      "REGION": shippingRegionCode,
      "COUNTRY": orderAddress ? orderAddress.billing_country_code != null && orderAddress.billing_country_code != "null" ? orderAddress.country_code : "" : "",
      "TELEPHONE": "",
      "MOBILE": orderAddress ? orderAddress.phone != null && orderAddress.phone != "null" ? orderAddress.phone.replaceAll(' ', '') : "" : "",
      "EMAIL": orderCustomer ? orderCustomer.customer_email != null && orderCustomer.customer_email != "null" ? orderCustomer.customer_email : "" : "",
      "GSTIN": orderData.gstin != null && orderData.gstin != "null" ? orderData.gstin : "",
      "CONTACT_PERSON": "",
      "CONTACT_MOBILE": "",
      "CONTACT_EMAIL": "",
      "PAYMENT_TERM": "0001"
    }
    data.push(body)
    resolve({ status: true, data: data })
  })
}

// push customer data to customerMaster
exports.pushToCustomerMaster = (orderData) => {
  return new Promise(async (resolve, reject) => {
    const reqData = await this.getCustomerMasterRequest(orderData)
    const options = {
      headers: {
        "Accept": "application/json",
        "Authorization": 'Basic ' + base64.encode(CONFIG.sap.userId + ":" + CONFIG.sap.pass),
        // "Content-Type": "application/x-www-form-urlencoded"
      }
    }
    //   resolve({status:true, data:{
    //     "CustomerNum": "001",
    //     "Gstin": "24ASDFRER",
    //     "Message": "Customer has been created"
    // }})
    if (reqData.status) {
      const customerData = await axios.post(CONFIG.sap.CUS_URL, reqData.data, options)
      let helperObj = {
        shopify_order_id: orderData.shopify_order_id,
        shopify_order_name: orderData.shopify_order_name,
        order_name: orderData.order_name,
        platform_type: "SAP-PushToCustomerMaster",
        request_data: JSON.stringify(reqData.data),
        response_status_code: customerData.status,
        response_data: customerData.data ? JSON.stringify(customerData.data) : JSON.stringify(customerData)
      }
      await orderHistoryHelper.insertOrderHistory(helperObj)
      customerData.data.length > 0 ? resolve({ status: true, data: customerData.data[0] }) : resolve({ status: false })
    } else {
      resolve({ status: false })
    }
  })
}

exports.pushToCustomerMasterByManual = (orderData, gstn) => {
  return new Promise(async (resolve, reject) => {
    const reqData = await this.getCustomerMasterRequestManual(orderData, gstn)
    const options = {
      headers: {
        "Accept": "application/json",
        "Authorization": 'Basic ' + base64.encode(CONFIG.sap.userId + ":" + CONFIG.sap.pass),
        // "Content-Type": "application/x-www-form-urlencoded"
      }
    }
    //   resolve({status:true, data:{
    //     "CustomerNum": "001",
    //     "Gstin": "24ASDFRER",
    //     "Message": "Customer has been created"
    // }})
    if (reqData.status) {
      const customerData = await axios.post(CONFIG.sap.CUS_URL, reqData.data, options)
      let helperObj = {
        shopify_order_id: orderData.shopify_order_id,
        shopify_order_name: orderData.shopify_order_name,
        order_name: orderData.order_name,
        platform_type: "SAP-B2B",
        request_data: JSON.stringify(reqData),
        response_status_code: customerData.status,
        response_data: customerData.data ? JSON.stringify(customerData.data) : JSON.stringify(customerData)
      }
      await orderHistoryHelper.insertOrderHistory(helperObj)
      customerData.data.length > 0 ? resolve({ status: true, data: customerData.data[0] }) : resolve({ status: false })
    } else {
      let helperObj = {
        shopify_order_id: orderData.shopify_order_id,
        shopify_order_name: orderData.shopify_order_name,
        order_name: orderData.order_name,
        platform_type: "SAP-B2B",
        request_data: JSON.stringify(reqData),
        response_status_code: customerData.status,
        response_data: customerData.data ? JSON.stringify(customerData.data) : JSON.stringify(customerData)
      }
      await orderHistoryHelper.insertOrderHistory(helperObj)
      resolve({ status: false })
    }
  })
}

exports.getCustomerMasterRequestManual = (orderData, gstn) => {
  return new Promise(async (resolve, reject) => {
    let data = []
    let orderCustomer = orderData.orderCustomers.length > 0 ? orderData.orderCustomers[0] : null
    let orderAddress = orderData.orderAddresses.length > 0 ? orderData.orderAddresses[0] : null
    let shippingRegionCode = ""
    let streetAddress
    if (orderAddress) {
      streetAddress = sapController.getStreetAddress(orderData.orderAddresses)
      let province = orderAddress.province != null && orderAddress.province != "" && orderAddress.province != "null" ? orderAddress.province : ""
      if (province != "") {
        province = await dbConnection.stateRegionCode.findOne({
          where: {
            [Op.or]: [{
              new_state: province
            },
            {
              state: province
            }]
          }
        })
        if (province) {
          shippingRegionCode = province.dataValues.region_code
        }
      }
    }
    const body = {
      "NAME1": orderAddress ? orderAddress.first_name != null && orderAddress.first_name != "null" && orderAddress.first_name != '' ? orderAddress.first_name : "" : "",
      "NAME2": orderAddress.last_name != null && orderAddress.last_name != "null" && orderAddress ? orderAddress.last_name != "" ? orderAddress.last_name : orderAddress.first_name : "",
      "NAME3": orderAddress ? orderAddress.first_name != null && orderAddress.first_name != "null" && orderAddress.first_name != "" ? orderAddress.first_name : "" : "",
      "NAME4": orderAddress ? orderAddress.last_name != null && orderAddress.last_name != "null" && orderAddress.last_name != '' ? orderAddress.last_name : orderAddress.first_name : "",
      "SEARCH_TERM1": orderAddress ? orderAddress.first_name != null && orderAddress.first_name != "null" ? orderAddress.first_name : "" : "",
      "SEARCH_TERM2": "",
      "STREET": streetAddress.shippingStreet,
      "STREET2": streetAddress.shippingStreet1,
      "STREET3": streetAddress.shippingStreet2,
      "STREET4": streetAddress.shippingStreet3,
      "STREET5": "",
      "CITY": orderAddress ? orderAddress.billing_city != null && orderAddress.billing_city != "null" ? orderAddress.billing_city : "" : "",
      "PINCODE": orderAddress ? orderAddress.billing_zip_code != null && orderAddress.billing_zip_code != "null" ? orderAddress.billing_zip_code : "" : "",
      "REGION": shippingRegionCode,
      "COUNTRY": orderAddress ? orderAddress.country_code != null && orderAddress.country_code != "null" ? orderAddress.country_code : "" : "",
      "TELEPHONE": "",
      "MOBILE": orderAddress ? orderAddress.phone != null && orderAddress.phone != "null" ? orderAddress.phone.replaceAll(' ', '') : "" : "",
      "EMAIL": orderCustomer ? orderCustomer.customer_email != null && orderCustomer.customer_email != "null" ? orderCustomer.customer_email : "" : "",
      "GSTIN": gstn,
      "CONTACT_PERSON": "",
      "CONTACT_MOBILE": "",
      "CONTACT_EMAIL": "",
      "PAYMENT_TERM": "0001"
    }
    data.push(body)
    resolve({ status: true, data: data })
  })
}

exports.getAllGstOrders = async (req, res, next) => {
  let response = { statusCode: 500, message: CONFIG.msg.ERROR }
  const { shop, search, gstValid } = req.query;

  try {
    if (!shop) throw new Error('Shop parameter missing', 400)

    let shopResponse = await dbConnection.shop.findOne({ where: { myshopify_domain: shop } })
    if (!shopResponse) throw new Error('Shop parameter missing', 404);

    let where = `order_item_splits.is_order_hold='1' AND order_item_splits.store_id='${shopResponse.id}'`

    if (search) {
      where += ` AND order_item_splits.shopify_order_name LIKE '%${search}%'`
    }
    if (gstValid) {
      where += ` AND order_item_splits.is_gst_Valid='${gstValid}'`
    }

    let queryStr = `SELECT 
    JSON_OBJECT(
      'billing_address1', order_addresses.billing_address1,
      'billing_address2', order_addresses.billing_address2,  
      'billing_city',order_addresses.billing_city,
      'billing_company',order_addresses.billing_company,
      'billing_first_name',order_addresses.billing_first_name,
      'billing_last_name',order_addresses.billing_last_name,
      'billing_name',order_addresses.billing_name,
      'billing_phone',order_addresses.billing_phone,
      'billing_province_code',order_addresses.billing_province_code,
      'billing_zip_code',order_addresses.billing_zip_code
    ) AS billing_details,
    order_addresses.billing_company, shopify_order_id, shopify_order_name, gstin, order_created_at, is_gst_Valid 
    FROM order_item_splits INNER JOIN order_addresses 
    ON order_item_splits.shopify_order_id = order_addresses.order_id 
    WHERE ${where}
    GROUP BY order_item_splits.shopify_order_id;`
    let orderData = await dbConnection.sequelize.query(queryStr, { type: QueryTypes.SELECT })

    response.statusCode = 200
    response.message = CONFIG.msg.SUCCESS
    response.data = orderData
    response.shop = shop

  } catch (error) {
    console.log("Error get All Gst Orders", error)
    response.message = error?.data?.message || error?.message || 500
    response.message = error?.data?.message || CONFIG.msg.ERROR
  }
  res.status(response.statusCode).json(response)
}

exports.actionOnGstOrder = async (req, res) => {
  let response = { statusCode: 500, message: CONFIG.msg.ERROR }
  let { shop } = req.query;
  const { orderName, type, editedGstNo, billingData } = req.body;
  try {
    if (!shop) throw new Error('Shop parameter missing', 400)

    let shopResponse = await dbConnection.shop.findOne({ where: { myshopify_domain: shop } })
    if (!shopResponse) throw new Error('Shop parameter missing', 404);
    let orderAddressData = await dbConnection.orderAddress.findOne({ where: { order_id: orderName, store_id: shopResponse.id } })
    if (type == 'Edit' && editedGstNo) {
      let gstObject = { gstin: editedGstNo, billCode: orderAddressData.billing_province, shipCode: orderAddressData.province }
      let isValid = await callToValidateGst(gstObject)
      if (!isValid) throw new Error('GST is Invalid')
      console.log("isValid--", isValid.isMatch)
      await dbConnection.orderAddress.update({ billing_company: isValid?.data?.data?.lgnm }, { where: { order_id: orderName, store_id: shopResponse.id } })
      await dbConnection.orderItemSplit.update({ is_gst_Valid: isValid.isMatch, gstin: editedGstNo }, { where: { shopify_order_id: orderName, store_id: shopResponse.id } });
    } else if (type == 'EditBilling' && billingData) {
      let billingObj = {
        billing_address1: billingData.billing_address1,
        billing_address2: billingData.billing_address2,
        billing_city: billingData.billing_city,
        billing_company: billingData.billing_company,
        billing_first_name: billingData.billing_first_name,
        billing_last_name: billingData.billing_last_name,
        billing_name: billingData.billing_first_name + " " + billingData.billing_last_name,
        billing_phone: billingData.billing_phone,
        billing_province: defaultindiaLocations(billingData.billing_province_code),
        billing_zip_code: billingData.billing_zip_code,
        billing_province_code: billingData.billing_province_code,
      }
      await dbConnection.orderAddress.update(billingObj, { where: { order_id: orderName, store_id: shopResponse.id } })
    } else if (type == 'Approved') {
      await dbConnection.orderItemSplit.update({ is_order_hold: '0', middleware_status: "GST ORDER PENDING" }, { where: { shopify_order_id: orderName, store_id: shopResponse.id } });
    } else if (type == 'Rejected') {
      await dbConnection.orderItemSplit.update({ is_order_hold: '0', is_gst_Valid: '0', gstin: '' }, { where: { shopify_order_id: orderName, store_id: shopResponse.id } });
    } else {
      throw new Error('Bad request', 400)
    }

    response.statusCode = 200
    response.message = CONFIG.msg.SUCCESS

  } catch (error) {
    console.log("Error action On Gst Order", error)
    response.statusCode = error?.message == 'GST is Invalid' ? 401 : 500
    response.message = error?.message || CONFIG.msg.ERROR
  }
  res.status(response.statusCode).json(response)
}