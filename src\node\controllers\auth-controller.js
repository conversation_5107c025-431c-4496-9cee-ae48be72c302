const asyncWait = require("async");
const dotenv = require('dotenv');
dotenv.config();
const CONFIG = require('../config/config_' + [process.env.NODE_ENV || 'local'] + '.json');
//Axios Call
const axios = require('axios')
//database connection
const dbConnection = require("../models");
const shopConnection = dbConnection.shop;
//Shopify Oauth Redirection
const { default: ShopifyApi, DataType, ApiVersion } = require('@shopify/shopify-api');
const jwtToken = require('../middleware/userAuth')
const productsyncController = require("../controllers/productsync-controller");
const fulfillmentServiceController = require('../controllers/fulfillmentService-controller')
//Login Authentication
exports.login = (req, res) => {
  let shop
  //Check Shop Exist or Not
  if (req.method === 'GET') {
    shop = req.query.shop
  } else {
    shop = req.body.shop
  }
  if (!shop) {
    return res.status(400).send({ err: "Required parameters missing" });
  }

  checkShopInstall(CONFIG.shopify.scopes, shop).then((response) => {
    var authUrl = "https://" + shop + "/admin/oauth/authorize?client_id=" + CONFIG.shopify.apiKey + "&scope=" + CONFIG.shopify.scopes + "&redirect_uri=" + CONFIG.shopify.redirectUrl
    res.redirect(authUrl);
  })
};
let checkShopInstall = (scopes, shop) => {
  return new Promise((resolve, reject) => {
    shopConnection.findOne({ where: { myshopify_domain: shop } }).then((shopResponse) => {
      if (shopResponse) {
        if (shopResponse.is_shop_install == '1') {
          var isInstall = false;
          var previousScopes = shopResponse.access_scopes.split(",").map(function (value) { return value.trim() });
          var newScopes = scopes.split(",").map(function (value) { return value.trim() });
          if (newScopes.length > previousScopes.length) {
            resolve({ status: true })
          } else {
            asyncWait.forEachSeries(previousScopes, function (scope, callback) {
              var isNewScope = newScopes.filter(x => x === scope);
              if (isNewScope.length == 0) {
                isInstall = true;
              }
              callback()
            }, () => {
              resolve({ status: isInstall })
            })
          }
        } else {
          resolve({ status: true })
        }
      } else {
        resolve({ status: true })
      }
    })
  })
}
//Generate Token
exports.generateToken = async (req, res) => {
  const { shop, hmac, code } = req.query;
  if (shop && hmac && code) {
    //Access Token RequestUrl
    const accessTokenRequestUrl = "https://" + shop + "/admin/oauth/access_token";
    //Access Token Object
    const accessTokenObj = {
      client_id: CONFIG.shopify.apiKey,
      client_secret: CONFIG.shopify.secreteKey,
      code: code
    };
    //Request Headers
    const headers = {
      'Content-Type': 'application/json',
    };
    //API Call
    axios.post(accessTokenRequestUrl, accessTokenObj, { headers: headers })
      .then((response, err) => {
        //Check Response Data
        if (response.data) {
          const accessToken = response.data.access_token
          const shopify = new ShopifyApi.Clients.Rest(shop, accessToken);
          shopify.get({ path: 'shop' }).then(async (shopData) => {
            if (shopData) {
              //Manage Shop Data
              var shop_data = shopData.body.shop;
              await this.insertShopData(shop_data, accessToken)
              //this.registerWebhook(shopify)
              // productsyncController.syncProduct(shop)
              //fulfillmentServiceController.fulfillmentService(shop)
              res.redirect(CONFIG.shopify.appUrl + '/app/orders?shop=' + shop + '&token=' + jwtToken.createtoken(shop));
            } else {
              res.redirect('/error');
            }
          }).catch((err) => console.error("catch errrr==>>>", err));
        } else {
          res.redirect('/error');
        }
      })
  } else {
    res.status(400).send("Required parameters missing");
  }
};

//Insert Shop Data
exports.insertShopData = (data, token) => {
  return new Promise((resolve, reject) => {
    //Create Shop Data Obj
    const shopDataObj = {
      store_id: data.id,
      name: data.name,
      email: data.email,
      myshopify_domain: data.myshopify_domain,
      shop_owner: data.shop_owner,
      token: token,
      access_scopes: CONFIG.shopify.scopes,
      iana_timezone: data.iana_timezone,
      guid: data.id,
      shopify_plan_name: data.plan_name,
      is_shop_install: "1",
      currency: data.currency,
      shop_created_at: data.created_at
    };

    //Check Database Row exist or not
    shopConnection.findOne({ where: { myshopify_domain: data.myshopify_domain } }).then((shopResponse) => {
      if (shopResponse != null) {
        shopConnection.update(shopDataObj, { where: { myshopify_domain: data.myshopify_domain } }).then(update_res => {
          resolve({ status: true })
        }).catch(function (error) {
          resolve({ status: false })
        });
      } else {
        shopConnection.create(shopDataObj).then(createResponse => {
          resolve({ status: true })
        }).catch(function (error) {
          resolve({ status: false })
        });
      }
    })
  })
};


exports.registerWebhook = async (shopify) => {
  const webhooks = await shopify.get({ path: 'webhooks' });
  var webhookList = webhooks.body.webhooks
  var webhookData = webhookCreateObject();
  asyncWait.forEachSeries(webhookData, function (webhook, callback) {
    var isCreateWebhook = webhookList.filter(x => x.topic === webhook.topic && x.address === webhook.address);
    if (isCreateWebhook.length > 0) {

      callback()

    } else {

      let body = { webhook: webhook }
      shopify.post({ path: 'webhooks', data: body, type: DataType.JSON }).then((response, error) => {
        callback()
      }).catch(err => {

        console.log("eee---",err)
      })
    }
  })
}

let webhookCreateObject = () => {
  let allWebhook = [];
  const webhookURL = CONFIG.shopify.webhookUrl
  let orderCreate = {
    topic: "orders/create",
    address: webhookURL,
    format: "json"
  }
  allWebhook.push(orderCreate)

  let orderUpdate = {
    topic: "orders/updated",
    address: webhookURL,
    format: "json"
  }
  allWebhook.push(orderUpdate)

  let orderCancel = {
    topic: "orders/cancelled",
    address: webhookURL,
    format: "json"
  }
  allWebhook.push(orderCancel)

  let orderDelete = {
    topic: "orders/delete",
    address: webhookURL,
    format: "json"
  }
  allWebhook.push(orderDelete)

  let orderFullfilled = {
    topic: "orders/fulfilled",
    address: webhookURL,
    format: "json"
  }
  allWebhook.push(orderFullfilled)

  let orderPartiallyFullfilled = {
    topic: "orders/partially_fulfilled",
    address: webhookURL,
    format: "json"
  }
  allWebhook.push(orderPartiallyFullfilled)

  let orderPaid = {
    topic: "orders/paid",
    address: webhookURL,
    format: "json"
  }
  allWebhook.push(orderPaid)

  let productCreate = {
    topic: "products/create",
    address: webhookURL,
    format: "json"
  }
  allWebhook.push(productCreate)

  let productUpdate = {
    topic: "products/update",
    address: webhookURL,
    format: "json"
  }
  allWebhook.push(productUpdate)

  let productDelete = {
    topic: "products/delete",
    address: webhookURL,
    format: "json"
  }
  allWebhook.push(productDelete)

  let appUnistalled = {
    topic: "app/uninstalled",
    address: webhookURL,
    format: "json"
  }
  allWebhook.push(appUnistalled)

  return allWebhook;
}