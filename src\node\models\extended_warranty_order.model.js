const Sequelize = require("sequelize");

module.exports = (sequelize) => {
    return sequelize.define("extended_warranty_order", {
        id: {
            type: Sequelize.INTEGER,
            primaryKey: true, // This field is the primary key of the table.
            autoIncrement: true, // Automatically incremented with each new entry.
            unique: true // Ensures the id is unique.
        },
        warranty_certificate_url: {
            type: Sequelize.STRING,
            comment: 'URL for the warranty registration certificate obtained from the warranty portal.',
            allowNull: true // This field can be null if not applicable.
        },
        order_name: {
            type: Sequelize.STRING,
            comment: 'Generated order name for this specific extended order, different from the main order name.',
            allowNull: false // This field cannot be null.
        },
        main_order_name: {
            type: Sequelize.STRING,
            comment: 'The main order name. The main order is available in the order_items_split table as "order_name" and referenced here as "main_order_name".',
            allowNull: true // This field cannot be null.
        },
        sap_order_number: {
            type: Sequelize.STRING,
            comment: 'SAP order number assigned during the SAP order creation process.',
            allowNull: true // This field can be null.
        },
        sap_billing_number: {
            type: Sequelize.STRING,
            comment: 'SAP billing number assigned during the SAP order creation process.',
            allowNull: true // This field can be null.
        },
        order_item_split_id: {
            type: Sequelize.INTEGER,
            comment: 'Foreign key referencing the primary key "id" in the order_item_split table.',
            allowNull: true // This field cannot be null.
        },
        sap_status: {
            type: Sequelize.ENUM("pushed", "pending", "failed", "invoiced", "cancelled"),
            comment: 'The possible statuses are: "pushed" (order created in SAP), "pending" (pending order creation in SAP), "failed" (failed order creation in SAP), and "invoiced" (invoice created in SAP and billing number generated).',
            defaultValue: "pending", // Default status is "pending".
            allowNull: false // This field cannot be null.
        },
        middleware_status: {
            type: Sequelize.ENUM("PENDING", "PROCESSING", "INVOICED", "WARRANTY REGISTERED", "REVERSE ORDER PROCESSING", "REVERSE ORDER INVOICED", "FAILED", "CANCELLED"),
            defaultValue: "PENDING", // Default status is "pending".
            allowNull: false
        },
        fulfillment_status: {
            type: Sequelize.ENUM('fulfilled', 'failed', 'pending'),
            defaultValue: 'pending'
        },
        sku: {
            type: Sequelize.STRING,
            comment: 'Stock Keeping Unit (SKU) for the product.',
            allowNull: false // This field cannot be null.
        },
        tax: {
            type: Sequelize.STRING,
            defaultValue: null
        },
        shopify_order_id: {
            allowNull: false,// This field cannot be null.,
            comment: 'shopify order id.',
            type: Sequelize.STRING
        },
        shopify_order_name: {
            allowNull: false,
            comment: 'shopify order number.',
            type: Sequelize.STRING
        },
        shopify_product_id: {
            type: Sequelize.STRING,
            comment: 'Shopify product ID.',
            allowNull: false // This field cannot be null.
        },
        shopify_variant_id: {
            type: Sequelize.STRING,
            comment: 'Shopify variant ID.',
            allowNull: false // This field cannot be null.
        },
        order_created_at: {
            type: Sequelize.STRING,
            allowNull: false,
            comment: 'Order created at'
        },
        sap_invoice_date: {
            type: Sequelize.STRING,
            allowNull: true,
        },
        checkout_id: {
            type: Sequelize.STRING,
            allowNull: true,
        },
        order_amount: {
            type: Sequelize.DOUBLE(11, 2),
            defaultValue: '0.00',
            comment: 'Order amount'
        },
        discount: {
            type: Sequelize.STRING,
            defaultValue: null
        },
        failed_reason: {
            type: Sequelize.STRING,
            defaultValue: null
        },
        line_item_id: {
            allowNull: false,
            type: Sequelize.STRING,
        },
        gateway: {
            allowNull: true,
            type: Sequelize.STRING
        },
    }, {
        indexes: [
            {
                unique: true, // Enforces uniqueness for the "order_name" field.
                fields: ['order_name']
            }
        ]
    });
};
