const dbConnection = require("../../models");
const CONFIG = require(`../../config/config_${process.env.NODE_ENV || 'local'}.json`);
const { Op, Sequelize } = require("sequelize");
const refundController = require('./../refund-controller');
const delhiveryController = require('./../delhivery-controller');
const extendedWarrantyServiceController = require('./../extendedWarranty/extendedWarrantyService-controller');
const moment = require("moment");

// API to get all orders
exports.getAllOrders = async (req, res) => {
  await fetchOrders(req, res);
};

// API to get return/cancel orders
exports.getReturnOrders = async (req, res) => {
  const whereCondition = { is_return: "1" };
  await fetchOrders(req, res, whereCondition);
};

const fetchOrders = async (req, res, whereCondition = {}) => {
  try {
    const { phone, sortOrder = "DESC" } = req.query;

    // Validate phone number
    if (!phone || !/^\d{10}$/.test(phone)) {
      return res.status(400).json({
        success: false,
        message: "A valid 10-digit phone number is required."
      });
    }

    // Clean the input phone (remove +91 if present)
    const cleanedPhone = phone.replace(/^(\+91)?/, '');

    // Fetch orders based on the provided condition
    const orders = await dbConnection.orderItemSplit.findAll({
      attributes: [
        "order_amount", "product_title", "sku", "order_name", "quantity", "order_status", "middleware_status",
        [Sequelize.col("product.handle"), "handle"], [Sequelize.col("product.id"), "product_id"],
      ],
      include: [
        {
          model: dbConnection.orderCustomer,
          as: "orderCustomers",
          attributes: [],
          where: Sequelize.where(
            Sequelize.fn('REPLACE', Sequelize.col('phone_number'), '+91', ''),
            cleanedPhone
          ),
          required: true,
        },
        {
          model: dbConnection.product,
          as: "product",
          attributes: [],
          required: true
        },
      ],
      where: whereCondition, // Use the provided condition
      order: [["id", sortOrder.toUpperCase()]],
      raw: true,
    });

    // Fetch product images for each order
    const orderWithImages = await Promise.all(
      orders.map(async (order) => {
        if (order.product_id) {
          const image = await dbConnection.productImage.findOne({ where: { product_id: order.product_id }, raw: true });
          order.product_image_url = image ? image.image_url : null;
        }
        return order;
      })
    );

    return res.status(200).json({
      success: orderWithImages.length > 0,
      message: orderWithImages.length ? "Orders fetched successfully." : "No records found.",
      data: orderWithImages,
    });
  } catch (error) {
    console.error("Error in fetchOrders:", error);
    return res.status(500).json({ success: false, message: "Internal server error." });
  }
};

exports.getOrderDetails = async (req, res) => {
  try {
    const { orderName } = req.body;
    if (!orderName) {
      return res.status(400).json({ success: false, message: "'orderName' is required." });
    }

    const order = await dbConnection.orderItemSplit.findOne({
      attributes: [
        "id", "shopify_product_id", "shopify_order_id", "shopify_variant_id", "order_amount", "product_title", "sku",
        "order_created_at", "order_name", "discount", "product_serial_number", "delivered_at", "shopify_order_name",
        "financial_status", "shipment_status", "quantity", "order_status", "middleware_status",
      ],
      where: { order_name: orderName },
    });

    if (!order) {
      return res.status(404).json({ success: false, message: "Order not found." });
    }

    return res.status(200).json({ success: true, message: "Order fetched successfully.", data: order });
  } catch (error) {
    console.error("Error in getOrderDetails:", error);
    return res.status(500).json({ message: "Internal server error." });
  }
};

exports.getInvoiceDetails = async (req, res) => {
  try {
    const { orderName } = req.body;
    if (!orderName) {
      return res.status(400).json({ success: false, message: "'orderName' is required." });
    }

    const invoice = await dbConnection.orderItemSplit.findOne({
      attributes: ["invoice_url", "order_name"],
      where: { order_name: orderName },
    });

    if (!invoice?.invoice_url) {
      return res.status(200).json({ success: false, message: "Invoice not found." });
    }

    return res.status(200).json({ success: true, message: "Invoice fetched successfully.", data: invoice });
  } catch (error) {
    console.error("Error in getInvoiceDetails:", error);
    return res.status(500).json({ message: "Internal server error." });
  }
};

// Common function to check order eligibility
const checkOrderEligibility = async (orderName) => {
  if (!orderName) {
    return { success: false, message: "'orderName' is required." };
  }

  const orderData = await dbConnection.orderItemSplit.findOne({
    // attributes: ["is_pickup", "order_name"],
    where: { order_name: orderName },
  });

  if (!orderData) {
    return { success: false, message: "Order not found." };
  }

  const isEligible = orderData.is_pickup === "0"; // Eligible if is_pickup is "0"
  return { success: true, isEligible, orderData };
};

// Check order cancel eligibility API
exports.checkCancelEligibility = async (req, res) => {
  try {
    const { orderName } = req.body;
    const eligibilityResult = await checkOrderEligibility(orderName);

    if (!eligibilityResult.success) {
      return res.status(400).json({ success: false, message: eligibilityResult.message });
    }

    return res.status(200).json({
      success: true,
      message: "Eligibility check successful.",
      data: { eligibility: eligibilityResult.isEligible, order_name: eligibilityResult.orderData.order_name },
    });
  } catch (error) {
    console.error("Error in checkCancelEligibility:", error);
    return res.status(500).json({ success: false, message: "Internal server error." });
  }
};

// Order cancellation API
exports.orderCancel = async (req, res) => {
  try {
    const { orderName } = req.body;

    // Check eligibility before proceeding
    const eligibilityResult = await checkOrderEligibility(orderName);

    if (!eligibilityResult.success) {
      return res.status(400).json({ success: false, message: eligibilityResult.message });
    }

    if (!eligibilityResult.isEligible) {
      return res.status(400).json({ success: false, message: "Order is not eligible for cancellation." });
    }

    // Insert order cancellation record
    const dbRes = await dbConnection.orderCancellation.create({
      order_name: orderName,
      status_type: "limechat_order_cancel",
      return_order_name: orderName,
      middleware_status: "LimeChat Customer Cancellation",
    });

    if (!dbRes || !dbRes.dataValues.id) {
      return res.status(500).json({ success: false, message: "Failed to create order cancellation record." });
    }

    let orderData = eligibilityResult.orderData;

    // If SAP status is 'Pushed', call SAP delivery function
    if (orderData.sap_status === "Pushed") {
      await delhiveryController.sapDelivery(orderName);
      orderData = await dbConnection.orderItemSplit.findOne({ where: { order_name: orderName } });
    }

    // If the order is paid and not COD, process refund
    if (orderData?.financial_status === "paid" && orderData?.gateway !== "Cash on Delivery (COD)") {
      await refundController.checkingForRefund(orderData);
    }

    // Cancel order in Delhivery
    // await delhiveryController.cancelOrder(orderName);

    const orderObj = { sap_status: "Cancel", delhivery_status: "Cancel", middleware_status: "CANCELLED", shipment_status: "cancel", is_replaceable: "0", is_cancel: "1", order_status: "Cancel", is_pickup: "1", is_order_hold: "0", order_cancel_date: new Date().toISOString() };
    await dbConnection.orderItemSplit.update(orderObj, { where: { order_name: orderName } })

    if (orderData.is_extended_warranty == "1") {
      await extendedWarrantyServiceController.cancelWarrantyOrder(orderName)
    }

    return res.status(200).json({ success: true, message: "Order canceled successfully." });

  } catch (err) {
    console.error("Error in orderCancel:", err);
    return res.status(500).json({ success: false, message: "Internal Server Error." });
  }
};
