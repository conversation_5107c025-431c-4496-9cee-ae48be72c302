module.exports = app => {
    const sapController = require("../controllers/sap_controller");
    var router = require("express").Router();
  
    // Login API Routes
    router.post("/return/sales", sapController.createReturnSaleOrder);
    router.get("/sync/stock",sapController.syncSapInventory)
    //router.post("/stock/check",sapController.inventoryStockCheck)
    app.use('/api/sap', router);
  };
  