module.exports = app => {
  const extendedWarrantyApiController = require("../controllers/extendedWarranty/extendedWarrantyApi-controller");
  const router = require("express").Router();


  router.get("/all-warranty-order", extendedWarrantyApiController.getAllOrder);
  router.post("/warranty-export", extendedWarrantyApiController.exportWarrantyOrders)
  router.post("/warranty-purchase", extendedWarrantyApiController.warrantyPurchase)

  //API Routes
  app.use('/api/order', router);
};
