import { put, takeLatest } from "redux-saga/effects";
import Api from "../../apis/Api";
import { openToast } from "../toast/toastActions";
import { getAllGstOrdersSuccess, actionGstOrderSuccess, actionGstOrderError } from "./gstOrdersActions";
import { gstOrdersTypes } from "./gstOrdersTypes";
import { actionMsg } from "../../helpers/appHelpers";

let toast = {
    message: "",
    isError: false,
    isActive: false
};

function* getAllGstOrdersList(data) {
    try {
        const response = yield Api.postAsync(
            Api.getAllGstOrdersData,
            data.payload
        )
        yield put(getAllGstOrdersSuccess(response));
        data.payload.callback();
    } catch (e) {
        data.payload.callback();
    }
}

function* actionOnGstOrderList(data) {
    let message = "Oppss...There is something wrong.";
    let isError = false;
    try {
        message = actionMsg[data?.payload?.request?.type] || "Success";
        const response = yield Api.postAsync(
            Api.actionOnGstOrder,
            data.payload
            )
            yield put(actionGstOrderSuccess(response));
            data.payload.callback();
    } catch (e) {
        isError = true;
        if (typeof e.response.data.message !== 'undefined' && e.response.data.message !== null) {
            message = e.response.data.message;
        }
        yield put(actionGstOrderError(e.response));
    } finally {
        toast = { ...toast, message: message, isActive: true, isError: isError }
        yield put(openToast(toast));
        data.payload.callback();
    }
}

export default function* gstOrdersSaga() {
    yield takeLatest(gstOrdersTypes.GET_GST_ORDERS_REQUEST, getAllGstOrdersList);
    yield takeLatest(gstOrdersTypes.ACTION_GST_ORDER_REQUEST, actionOnGstOrderList);
}