module.exports = app => {
  const authController = require("../controllers/auth-controller.js");
  const orderController = require('../controllers/ordersync-controller')
  const deliveryController = require('../controllers/delhivery-controller')
  const webhookController = require("../controllers/webhook-controller")
  const zippeeWebhookController = require("../controllers/zippee-webhook-controller")
  const extendedWarrantyApiController = require("../controllers/extendedWarranty/extendedWarrantyApi-controller");
  const helper = require('../middleware/userAuth')
  var router = require("express").Router();
  // Login API Routes
  router.get("/login", authController.login);
  router.post("/login", authController.login);
  router.get("/auth/callback", authController.generateToken);
  router.get("/order/customer_order", helper.authenticateWebhook, orderController.customerOrder);
  router.post("/order/cancel", helper.authenticateWebhook, orderController.cancelOrderSapDelhivery)
  //router.post("/order/replacement", orderController.replacementOnDelhiveryAndSap)
  //router.post("/order/replacement/delivery", orderController.replacementDelivery)
  router.post("/handle/webhook", helper.authenticateWebhook, webhookController.webhook)
  router.post("/handle/zippee-webhook",zippeeWebhookController.zippeeWebhook)
  router.post("/create/customerMaster", deliveryController.pushCustomerMaster)
  router.get("/export-data-plantcode", orderController.exportDataByPlantCode)
  router.get("/order-not-picked", orderController.orderNotPickedData)
  router.get("/order-not-delivered", orderController.orderNotDeliveredData)
  router.get("/get-customer-order", helper.authenticateWebhook, extendedWarrantyApiController.getCustomerOrderForWarranty);
  router.post("/order/verify-serial-number", helper.authenticateWebhook, extendedWarrantyApiController.verifySerialNumber);


  app.use('/', router);
};