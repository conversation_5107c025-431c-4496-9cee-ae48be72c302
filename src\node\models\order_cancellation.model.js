const Sequelize = require("sequelize");
module.exports = (sequelize) => {
  return sequelize.define("order_cancellation", {
    id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      unique: true
    },
    order_name: {
        allowNull: true,
        type: Sequelize.STRING(100)  // Limit to 100 characters
    },
    middleware_status: {
      allowNull: true,
      type: Sequelize.STRING(100),  // Limit to 100 characters
      defaultValue: null
    },
    return_order_name: {
      allowNull: true,
      type: Sequelize.STRING(100),  // Limit to 100 characters
      defaultValue: null
    },
    status_type: {
      allowNull: true,
      type: Sequelize.STRING(50),   // Limit to 50 characters
      defaultValue: null
    },
    // Add a computed hash field for unique constraint
    cancel_hash: {
      type: Sequelize.STRING(64),
      allowNull: true,
      unique: true
    }
  },
    {
      indexes: [
        {
          name:'cancel_hash_index',
          unique: true,
          fields: ['cancel_hash']
        }
      ],
      hooks: {
        beforeCreate: (instance, options) => {
          // Generate hash from the combination of fields
          const crypto = require('crypto');
          const hashString = `${instance.order_name || ''}-${instance.status_type || ''}-${instance.middleware_status || ''}-${instance.return_order_name || ''}`;
          instance.cancel_hash = crypto.createHash('sha256').update(hashString).digest('hex');
        },
        beforeUpdate: (instance, options) => {
          // Generate hash from the combination of fields
          const crypto = require('crypto');
          const hashString = `${instance.order_name || ''}-${instance.status_type || ''}-${instance.middleware_status || ''}-${instance.return_order_name || ''}`;
          instance.cancel_hash = crypto.createHash('sha256').update(hashString).digest('hex');
        }
      }
    });
};