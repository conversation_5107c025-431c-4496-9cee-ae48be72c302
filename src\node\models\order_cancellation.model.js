const Sequelize = require("sequelize");
module.exports = (sequelize) => {
  return sequelize.define("order_cancellation", {
    id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      unique: true
    },
    order_name: {
        allowNull: true,
        type: Sequelize.STRING
    },
    middleware_status: {
      allowNull: true,
      type: Sequelize.STRING,
      defaultValue: null
    },
    return_order_name: {
      allowNull: true,
      type: Sequelize.STRING,
      defaultValue: null
    },
    status_type: {
      allowNull: true,
      type: Sequelize.STRING,
      defaultValue: null
  },
  },
    {
      indexes: [
        {
          name:'cancel_index',
          unique: true,
          fields: ['order_name','status_type','middleware_status','return_order_name']
        }
      ]
    });
};