{"shopify": {"apiKey": "c5c60f96e8012fb6dd32b6b187586e4c", "secreteKey": "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", "redirectUrl": "https://symphonyapp.lucentinnovation.com/auth/callback", "scopes": "read_products,write_products,read_orders,write_orders,read_fulfillments,write_fulfillments,write_assigned_fulfillment_orders,write_merchant_managed_fulfillment_orders,write_third_party_fulfillment_orders", "appUrl": "https://symphonyapp.lucentinnovation.com", "appName": "", "webhookUrl": "arn:aws:events:ap-south-1::event-source/aws.partner/shopify.com/6342397/symphony-prod-webhook", "encryptionkey": "2Z3obqOFXl2qT94S8mVgaJxT7jgE2L7r"}, "database": {"host": "symphony-prod-db.cnsig8a8aa1z.ap-south-1.rds.amazonaws.com", "user": "sapsymphony", "password": "Oaj0xtGdOS05ZZ7sPRZg", "db": "symphony", "dialect": "mysql", "pool": {"max": 5, "min": 0, "acquire": 30000, "idle": 10000}}, "delhivery": {"post_url": "https://track.delhivery.com/api/cmu/create.json", "get_url": "https://track.delhivery.com/api/v1/packages/json/", "cancel_url": "https://track.delhivery.com/api/p/edit", "waybill_url": "https://track.delhivery.com/waybill/api/bulk/json/?token=b9e5279205d776088d6daae8a16f9c13f597bfeb&count=2", "token": "b9e5279205d776088d6daae8a16f9c13f597bfeb", "CFA_NAME": "SYMPHONY SURFACE", "CFA_CLIENT": "SYMPHONY SURFACE", "billing_url": "https://track.delhivery.com/api/p/packing_slip", "track_order_url": "https://track.delhivery.com/api/v1/packages/json/?token=b9e5279205d776088d6daae8a16f9c13f597bfeb"}, "sap": {"SO_URL": "https://symerpprd.symphonylimited.com:8443/sap/bc/zspysoapi", "INV_URL": "https://symerpprd.symphonylimited.com:8443/sap/bc/zspystkapi", "DEL_URL": "https://**************:8443/sap/bc/zspydlvyapi", "CUS_URL": "https://symerpprd.symphonylimited.com:8443/sap/bc/zspycustapi", "CLR_URL": "https://symerpprd.symphonylimited.com:8443/sap/bc/zb2c_clr_doc", "userId": "RFCSAPD2C", "pass": "Qwerty@123"}, "crm": {"log_url": "https://symphonycustapp.servitiumcrm.com/ServitiumCRM_IS_SYMPHONY_CUST/IS/submitData", "warranty_url": "https://symphonydlrapp.servitiumcrm.com/ServitiumCRM_IS_DL_SYMPHONY/IS/submitData", "track_url": "https://app.servitiumcrm.com/ServitiumCRM_IS_DL_SYMPHONY/IS/getCallList", "getCallList": "https://symphonydlrapp.servitiumcrm.com/ServitiumCRM_IS_DL_SYMPHONY/IS/getCallList", "distributorCode": 0, "dealerCode": 0, "customerType": 1, "brandCode": 1, "productCategoryCode": 1, "productCode": 1, "serviceTypeCode": 1, "cityDesc": "0", "loginType": "0", "companyCode": "86007", "userId": 9999, "userLogId": 9999, "warrantyUserId": 9997, "version": "9", "token": "OIYSZZ6Z2ZWYEO4EGMGSXBYNLLQKZT5U", "warrantyToken": "8ZEGPJFC3MYC1XSHOPPNM7ONI2GUMCI", "new_warrantyToken": "IVRSZZ6Z2ZWYEOSYMPHONYYNLLQKZIVR", "new_warrantyUserId": "9998"}, "gst": {"url": "https://passthroughapi.cygnetgsp.in/addon-commonapi/v1.1/search", "client_id": "W3lA8VKZxT9UKQetpPN+LzZ7zhba74Mfyd4QizL+ing=", "client_secret": "Z42u/dTLZ1odH5QgosoexHVSH8gqVhRVZnX66Hj/i3zWyvGyG7EqIS1SNzldhrYIHpA+o7Mhbvmzlq1TsPWelQ==", "client_secret_encode": "787D1LfdLpsZTnSyOpG9lBIOxXGnqJturqq/zFGWFlwe58UpE0stkE2+m4E8/RpWzqn0HuQbB0HYXMyIOSgEavKyTTfIHsreRiiheRM4Ru3sS2YtSDM5Pxr9QCkTnTjyi8qQpxIBvz38Xe1adyeGx30jnHNiQE4YRq+pjGTfLUXC9LjZTgMbKIQMk6ZnE6tKyUMJbuFORLJDN6nz/64py7ppnBRqq8u/iYdB3HQELeyIqvjsVOgO5zptvm4ohQYo", "client_id_encode": "rn+ZypqRDVD5WwLOVAx7at96edI+7g5kSjoRLrgKZyIslOluDWDkpeqY4qjf4Ucj88D/vlKmmwiY1FI3f6YweEROQsH1C7nablSCb5FSskhSsw3bRMnyVm8YYofmAlSI", "ip_usr": "***********", "txn": "", "username": "ashwin.prajapa<PERSON>@symphonylimited.com", "password": "Admin@123", "token": ""}, "msg": {"SUCCESS": "Success", "UPDATE": "Updated Successfully", "CREATE": "Created Successfully", "FAILED": "Failed", "ERROR": "Something went wrong", "PARAMETER": "Required parameter not found", "NO_DATA": "Data not found", "DELETE": "Deleted Successfully", "REFUND": "Refund Successfull", "UPLOAD": "File Uploaded Successfully", "CANCEL": "Canceled Successfully"}, "status": {"SUCCESS": 200, "FAILED": 500, "ERROR": 500}, "otp": {"url": "http://ip.shreesms.net/smsserver/SMS10N.aspx", "user_id": "symphony", "password": "12345", "entity_id": "1301159108961563511", "template_id": "1307161578314629650", "gsm": "SYMPNY", "otp_secret": "FaRjfZuO0k4"}, "aws": {"accessKey": "****************************************************************", "secretKey": "1c1b49b37ffe3f5c819c655593d52eac60679a7d53fb8dd7ea66a3b1545369020c0dcb3d27adcf66d83ca257c3dcc937", "bucketName": "symphony-object"}, "fulfillment_service": {"name": "SYMPHONY", "callback_url": "https://symphonyapp.lucentinnovation.com/", "inventory_management": true, "tracking_support": true, "requires_shipping_method": false, "format": "json"}, "jwt": {"token": "lucent_innovation"}, "razorpay": {"keyId": "***********************", "keySecret": "X7na4FAV8wgazOH7e7lyQGDa", "url": "https://api.razorpay.com/v1/", "account_id": "acc_LptnmtE72xqC7F"}, "webhookAuth": {"userName": "symphony", "password": "gmyqmgjz8s415zqs"}, "ftp": {"host": "***************", "user": "TPL", "password": "g2xJeM*m$g"}, "googleDistanceMatrixApi": {"url": "https://maps.googleapis.com/maps/api/distancematrix/json", "apiKey": "AIzaSyBjSgMo4WWXAcPB5UteXUOyeoIqCCCYXms"}, "email": {"username": "<EMAIL>", "password": "k6VSrRo3DmCp", "host": "************", "port": 587, "from": "<EMAIL>", "to": "<EMAIL>,<EMAIL>,<EMAIL>", "exportto": "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>", "pendingPickupMail": "<EMAIL>,<EMAIL>,<EMAIL>,<PERSON><PERSON><PERSON>.<EMAIL>,<EMAIL>", "pendingDeliveryMail": "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>", "escalation_to": "<EMAIL>", "escalation_from": "<EMAIL>", "cqsendmail": "<EMAIL>,<EMAIL>"}, "clickpost": {"courier_partner": "289", "account_code": "Symphony Surface", "url": "https://www.clickpost.in/api/v3/tracking/awb-register/", "key": "cd0a6a3f-0250-4399-87f6-ca1e160e01cf", "reverse_courier_partner": "25", "reverse_account_code": "Symphony RVP"}, "extendedwarranty": {"url": "https://www.sl-ddm.com/api/b2c-warranty-purchase", "authkey": "FzkSK3W2FW71vKUhhfG2Naf8RYw7", "ew_product": {"EXT-WARR": "********", "EXT-WARR-MV": "********"}}, "limechat": {"username": "symphony@limechat", "password": "LuC3C7X9Hbj33V3od"}, "zippee": {"createOrder": "https://api.loginextsolutions.com/ShipmentApp/mile/v2/create", "cancelOrder": "https://api.loginextsolutions.com/ShipmentApp/mile/v2/cancel", "authHeader": "a1e13d07-4f01-4421-ba9d-cf8533ba07e2", "branch": "DEL_malviya-nagar"}}