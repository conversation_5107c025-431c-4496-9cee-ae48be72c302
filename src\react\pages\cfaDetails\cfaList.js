import {
  <PERSON>,
  Layout,
  Page,
  Filters,
  ChoiceList,
  Stack,
  Pagination,
  Button,
  Icon,
  Modal,
  FormLayout,
  TextField,
  DropZone,
  Caption,
  DataTable,
  SkeletonBodyText,
  TextContainer,
  Select,
  Spinner,
  Banner
} from "@shopify/polaris";
import React, { Component } from "react";
import { SearchMajor } from '@shopify/polaris-icons';
import { connect } from "react-redux";
import { exportCfaStockRequest, getCfaListRequest, pincodeExportRequest, saveCfaFileRequest } from "../../redux/cfa/cfaActions";
import '../../../react/App.css';
import { setPagination, isEmpty, disambiguateLabel, dateConversion, debounceEvent, StatusArray } from "../../helpers/appHelpers";
//   import {SEARCH_IMAGE} from "../../config/settings";
const SEARCH_IMAGE = "";
import { EditMinor, CirclePlusOutlineMinor, CircleMinusOutlineMinor, ViewMinor, HideMinor } from "@shopify/polaris-icons";
//   import '../../../react/App.css';
// const CONFIG = require('../../../node/config/config_' + [process.env.NODE_ENV || 'local'] + '.json');
// const Cryptr = require('cryptr');
// const cryptr = new Cryptr(CONFIG.mySecrete);



let pagination = setPagination();

class Customers extends Component {
  constructor(props) {
    super(props);
    this.state = {
      queryValue: "",
      cfaType: [],
      pageLoading: false,
      importModalActive: false,
      customer: {},
      files: [],
      rejectedFiles: [],
      isSaveDisable: true,
      errorMessage: null,
      importButtonLoading: false,
      exportCfaActive:false
    };
  }
  componentDidMount() {
    document.title = "CFA Stock";
    this.getAllCfaData();
  }

  getAllCfaData = () => {
    this.setState({ pageLoading: true });
    const { perpage, page } = pagination;
    // debugger;
    const {
      queryValue, cfaType
    } = this.state;
    const request = {
      page: page,
      perpage: perpage,
      search: queryValue,
      cfaType: cfaType
    };
    let { getCfaList } = this.props;
    getCfaList({
      request,
      callback: () => {
        if (!this.props.error) {
          const { cfaListData } = this.props;
          const { page, limit, total, totalPage } = cfaListData.data;
          pagination = setPagination(page, limit, totalPage, total);
        }
        this.setState({ pageLoading: false });
      },

    });

  };

  handleChangePage = (action) => {
    action === "Next" ? pagination.page++ : pagination.page--;
    this.getAllCfaData();
  };

  handleChangeCfaType = (value) => {
    this.setState({ cfaType: value },
      () => {
        pagination.page = 1;
        this.getAllCfaData();
      });
  }

  handleFiltersClearAll = () => {
    this.handleQueryValueRemove();
  };

  handleFiltersQueryChange = (value) => {
    this.setState({ queryValue: value });
    debounceEvent(() => {
      pagination.page = 1;
      this.getAllCfaData();
    }, 300);
  };
  handleQueryValueRemove = () => {
    this.setState({
      queryValue: ''
    },
      () => {
        this.getAllCfaData();
      });
  };

  handleCfaTypeRemove = () => {
    this.setState({ cfaType: '' },
      () => {
        this.getAllCfaData();
      });
  }

  saveCfaModal = () => {
    this.setState({ importButtonLoading: true });
    var files = this.state.files;
    if (files[0].type == 'xls' || files[0].type == 'xlsx' || files[0].type == 'application/vnd.ms-excel' || files[0].type == 'application/vnd.msexcel' || files[0].type == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
      //   if(files[0].type == 'text/csv' || files[0].type == 'CSV' || files[0].type == 'csv' || files[0].type=='application/vnd.ms-excel' || files[0].type=='application/vnd.msexcel'){
      let formData = new FormData();
      if (!isEmpty(files)) {
        formData.append('file', files[0]);
      }
      let { saveCfaFile } = this.props;
      saveCfaFile({
        formData,
        callback: () => {
          if (!this.props.error) {
            this.setState({ importButtonLoading: false, file: [], importModalActive: false },
              () => {
                this.getAllCfaData();
              });
          } else {
            this.setState({ importButtonLoading: false });
          }
        },
      });
    } else {
      this.setState({ errorMessage: "Only xlsx file is allowed", importButtonLoading: false })
    }
  }

  // handle dropzone
  handleDrop = (_droppedFiles, files, rejectedFiles) => {
    this.setState({
      files: files,
      rejectedFiles: rejectedFiles
    });
    if (rejectedFiles.length > 0) {
      this.setState({ isSaveDisable: true })
    } else {
      this.setState({ isSaveDisable: false })
    }
  };
  handleExportCFAModal = () => {
    this.setState({ exportCfaActive: !this.state.exportCfaActive });
  };
  handleImportModal = () => {
    this.setState({ importModalActive: !this.state.importModalActive, errorMessage: '', files: [], rejectedFiles: [], isSaveDisable: true });
  }
  handleExportStock = () => {
    let { exportCfaStock } = this.props;
    this.setState({ loadingButton: true});
    exportCfaStock({
      data:{},
      callback: () => {
        if (!this.props.error) {
          this.setState({ loadingButton: false, exportCfaActive: false });
        } else {
          this.setState({ loadingButton: false })
        }
      },
    });
  };
  serviceablePincodeExport = () => {
    let { pincodeExport } = this.props;
    pincodeExport({
        callback: () => {
            if (!this.props.error) {
                var response = this.props.pincodeExportResponse;
                const downloadUrl = window.URL.createObjectURL(
                    new Blob([response.data])
                );
                const link = document.createElement("a");
                link.href = downloadUrl;
                var name = "Serviceable Pincode";
                link.setAttribute("download", name + ".xlsx");
                document.body.appendChild(link);
                link.click();
                link.remove();
                this.getAllCfaData();
            } else {
                console.log("error==>>>>", this.props.error)
            }
        },
    });
}

  customerListMarkup = () => {
    const { loading, cfaListData } = this.props;
    let rows = [];
    if (!loading) {
      const dataObj = cfaListData.data.data;
      if (!isEmpty(dataObj)) {
        dataObj.forEach(function (cfa) {
          const row = [
            // cfa.pincode,
            // cfa.cfa_type,
            cfa.pincode_group,
            cfa.stock,
            cfa.sku,
            cfa.is_active?'Yes':'No'
          ];
          rows.push(row);
        }, this);

      } else {
        return (<div className="search_div">
          {/* <img src={SEARCH_IMAGE} alt="search_img" /> */}
          <Icon source={SearchMajor} color="base" />
          <div>
            <span className="content_span">No data found</span>
          </div>
        </div>);
      }
    } else {
      rows = [];
      for (var i = 0; i <= 5; i++) {
        rows.push([
          <SkeletonBodyText lines={1} />,
          <SkeletonBodyText lines={1} />,
          <SkeletonBodyText lines={1} />,
          <SkeletonBodyText lines={1} />,
          <SkeletonBodyText lines={1} />,
        ]);
      }
    }

    return (
      <DataTable
        verticalAlign="middle"
        columnContentTypes={["text", "text", "text"]}
        headings={[
          //  <b>Pincode</b> ,
          // <b>CFA Type</b>,
          <b>CFA Name</b>,
          <b>Stock</b>,
          <b>SKU</b>,
          <b>Active</b>

        ]}
        rows={rows}
      />
    );
  };
  render() {
    const hasError = this.state.rejectedFiles.length > 0;
    const errorMessage = hasError && <Banner title="Only xlsx file is allowed" status="critical"></Banner>
    const filters = [
      // {
      //   key: "cfaType",
      //   label: "CFA Type",
      //   filter: (
      //     <ChoiceList
      //       title="Cfa type"
      //       titleHidden
      //       choices={[
      //         {label: 'Primary', value: 'primary'},
      //         { label: "Secondary", value: "secondary"},
      //         { label: "Mother", value: "mother"},
      //       ]}
      //       selected={this.state.cfaType || []}
      //       onChange={this.handleChangeCfaType}
      //     />
      //   ),
      //   shortcut: true,
      // },

    ];

    const appliedFilters = [];

    if (!isEmpty(this.state.cfaType)) {
      const key = "cfaType";
      appliedFilters.push({
        key,
        label: disambiguateLabel(key, this.state.cfaType),
        onRemove: this.handleCfaTypeRemove,
      });
    }

    return (
      <div>
        <Page
          fullWidth
          title="SKU Details"
          primaryAction={
            <Stack>
              <Button primary onClick={this.handleImportModal}> Import Serviceable Pincode</Button>
              <Button onClick={this.handleExportCFAModal}> Export CFA Stock</Button>
            </Stack>
          }
          
        >
          <Layout>
            <Layout.Section>
              <Card>
                <Card.Section>
                  <Filters
                    queryPlaceholder="Search"
                    label="Title"
                    filters={filters}
                    queryValue={this.state.queryValue}
                    appliedFilters={appliedFilters}
                    onQueryChange={this.handleFiltersQueryChange}
                    onQueryClear={this.handleQueryValueRemove}
                    // value={this.state.searchField}
                    onClearAll={this.handleFiltersClearAll}
                  />
                </Card.Section>
                {this.customerListMarkup()}
                <Card.Section>
                  <Stack alignment="center" vertical="middle" distribution="center">
                    <Pagination
                      plain
                      hasPrevious={pagination.hasPrevious}
                      onPrevious={() => {
                        this.handleChangePage("Previous");
                      }}
                      hasNext={pagination.hasNext}
                      onNext={() => {
                        this.handleChangePage("Next");
                      }}
                    />
                  </Stack>
                </Card.Section>
              </Card>
            </Layout.Section>
          </Layout>
          <Modal
            title="Import Serviceable Pincode"
            open={this.state.importModalActive}
            onClose={this.handleImportModal}
            primaryAction={{
              content: 'Save',
              onAction: this.saveCfaModal,
              loading: this.state.importButtonLoading,
              disabled: this.state.isSaveDisable
            }}
          >
            <Modal.Section>
              <FormLayout>
              <Button plain onClick={this.serviceablePincodeExport}>Click here to download template</Button>
                {errorMessage}
                <DropZone
                  label="Select File"
                  allowMultiple={false}
                  error={true}
                  accept=".xlsx"
                  type="file"
                  onDrop={this.handleDrop}
                >
                  <Stack vertical>
                    {this.state.files !== '' ? (
                      this.state.files.map((file, index) => (
                        <Stack alignment="center" key={index}>
                          <div>
                            {file.name} <Caption>{file.size} bytes</Caption>
                          </div>
                        </Stack>
                      ))
                    ) : ""}
                    <DropZone.FileUpload />
                  </Stack>
                </DropZone>
                {/* <div className = "error-text">{this.state.errorMessage}</div>            */}
              </FormLayout>
            </Modal.Section>
          </Modal>
          <Modal
          title="Export CFA STOCK"
          open={this.state.exportCfaActive}
          onClose={this.handleExportCFAModal}
          primaryAction={{
            content: "Yes",
            onAction: this.handleExportStock,
            loading: this.state.loadingButton,
          }}
          secondaryActions={[
            {
              content: "No",
              onAction: this.handleExportCFAModal,
            },
          ]}
        >
          <Modal.Section>
            <TextContainer>
              <p>Are you sure you want to export?</p>
            </TextContainer>
          </Modal.Section>
        </Modal>
        </Page>
      </div>
    );
  }
}


const mapStateToProps = (state) => (
  {
    cfaListData: state.cfa.data,
    pincodeExportResponse:state.cfa.pincodeExport,
    loading: state.cfa.loading,
    // customerloading:state.customer.customerloading,
    // error: state.customer.error,
  });


const mapDispatchToProps = (dispatch) => ({
  getCfaList: (cfaData) => dispatch(getCfaListRequest(cfaData)),
  saveCfaFile: (object) => dispatch(saveCfaFileRequest(object)),
  pincodeExport: (pincodeExport) => dispatch(pincodeExportRequest(pincodeExport)),
  exportCfaStock: (data) => dispatch(exportCfaStockRequest(data)),

  // syncCustomer: (customerSyncData) => dispatch(syncCustomerRequest(customerSyncData)),
  // getCustomersDetail: ( customerDetail) => dispatch(getCustomersDetailRequest(customerDetail)),
  // updateCustomer: (object) => dispatch(updateCustomerRequest(object)),
  // saveTag:(object) =>dispatch(saveTagRequest(object)),
  // removeCustomer:object=>dispatch(removeCustomerRequest(object)),
});



export default connect(mapStateToProps, mapDispatchToProps)(Customers);
