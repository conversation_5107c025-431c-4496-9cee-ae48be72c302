const Sequelize = require("sequelize");
module.exports = (sequelize) => {
    return sequelize.define("cfa_stock_mapping", {
        id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            unique: true
        },
        store_id: {
            allowNull: false,
            type: Sequelize.INTEGER,
            references: {
                model: 'stores',
                key: 'id'
            },
        },
        stock: {
            type: Sequelize.STRING,
            defaultValue: '0'
        },
        local_stock:{
            type:Sequelize.STRING,
            defaultValue: '0'
        },
        sku: {
            type: Sequelize.STRING
        },
        pincode_group: {
            type: Sequelize.STRING
        },
        is_active: {
            type: Sequelize.BOOLEAN,
            defaultValue: true,
          },
    },
        {
            indexes: [
                {
                    unique: true,
                    fields: ['pincode_group', 'sku']
                }
            ]
        
    });
};