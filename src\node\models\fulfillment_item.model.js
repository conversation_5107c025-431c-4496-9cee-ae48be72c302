const Sequelize = require("sequelize");
module.exports = (sequelize) => {
  return sequelize.define("fulfillment_item", {
    id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      unique: true
    },
    store_id: {
      allowNull:false,
      type: Sequelize.INTEGER,
      references: {
        model: 'stores',
        key: 'id'
      },
    },
    order_id: {
      allowNull:false,
      type: Sequelize.INTEGER,
      references: {
        model: 'order_item_splits',
        key: 'id'
      },
    },
    fulfillment_id: {
      allowNull:false,
      type: Sequelize.STRING
    },
    fulfillment_status: {
      type: Sequelize.STRING,
    },
    tracking_company: {
      type: Sequelize.STRING,
    },
    tracking_url: {
      type: Sequelize.STRING,
    },
    location_id: {
      type: Sequelize.STRING,
    },
    tracking_number: {
      type: Sequelize.STRING,
      // references: {
      //   model: 'order_item_splits',
      //   key: 'waybill_number'
      // },
    },
    line_item_id: {
      allowNull:false,
      type: Sequelize.STRING,
    },
    price: {
      allowNull:false,
      type: Sequelize.DOUBLE(11,2),
      defaultValue: '0.00'
    },
    fulfillment_created_at: {
      type: Sequelize.STRING,
    },
    fulfillment_updated_at: {
      type: Sequelize.STRING
    },
  },
    {
      indexes: [
        {
          unique: true,
          fields: ['store_id','order_id','fulfillment_id']
        }
      ]
    });
};