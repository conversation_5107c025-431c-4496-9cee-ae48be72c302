{"name": "symphony-app", "version": "1.0.0", "description": "Middleware App between Shopify & SAP", "main": "index.js", "scripts": {"build": "set NODE_ENV=production&& webpack --mode production", "start": "node src/node/index.js", "client": "set NODE_ENV=local&& webpack --mode development --watch", "server": "nodemon src/node/index.js", "webhook": "nodemon src/webhook/app.js", "build:staging": "set NODE_ENV=development&& webpack --mode development"}, "author": "lucent", "license": "ISC", "dependencies": {"@material-ui/core": "^4.12.3", "@shopify/app-bridge": "^1.29.0", "@shopify/app-bridge-react": "^1.29.0", "@shopify/app-bridge-utils": "^1.29.0", "@shopify/polaris": "^6.1.0", "@shopify/polaris-icons": "^4.5.0", "@shopify/shopify-api": "^2.0.0", "@testing-library/jest-dom": "^5.16.3", "@testing-library/react": "^12.1.4", "@testing-library/user-event": "^13.5.0", "async": "^3.2.2", "aws-sdk": "^2.1046.0", "axios": "^0.24.0", "babel-polyfill": "^6.26.0", "base-64": "^1.0.0", "cors": "^2.8.5", "dotenv": "^10.0.0", "express": "^4.17.1", "fs": "^0.0.1-security", "ftp": "^0.3.10", "ftp-client": "^0.2.2", "json-as-xlsx": "^2.3.10", "jsonwebtoken": "^8.5.1", "moment": "^2.29.1", "multer": "^1.4.4", "mysql2": "^2.3.3", "node-cron": "^3.0.0", "nodemailer": "^6.7.3", "nodemon": "^2.0.15", "path": "^0.12.7", "pdf-creator-node": "^2.2.4", "pdf-merger-js": "^3.2.1", "pdfkit": "^0.13.0", "randomstring": "^1.2.1", "react": "^16.5.2", "react-chrono": "^1.14.0", "react-dom": "^16.5.2", "react-hot-loader": "^4.5.3", "react-redux": "^7.2.2", "react-router-dom": "^5.2.0", "react-scripts": "4.0.1", "redux": "^4.0.5", "redux-logger": "^3.0.6", "redux-persist": "^6.0.0", "redux-saga": "^1.1.3", "sequelize": "^6.9.0", "shopify-jwt-auth-verify": "^1.0.10", "styled-components": "^5.3.0", "winston": "^3.3.3", "xlsx": "^0.17.5"}, "devDependencies": {"@babel/core": "^7.0.0", "@babel/plugin-proposal-class-properties": "^7.0.0", "@babel/preset-env": "^7.0.0", "@babel/preset-react": "^7.0.0", "babel-core": "^6.26.3", "babel-loader": "^8.2.2", "body-parser": "^1.19.0", "clean-webpack-plugin": "^4.0.0-alpha.0", "css": "^3.0.0", "css-loader": "^2.1.1", "file-loader": "^3.0.0", "html-webpack-plugin": "^5.3.1", "sass": "^1.34.0", "sass-loader": "^11.1.1", "scss-loader": "0.0.1", "style-loader": "^0.23.0", "url-loader": "^1.0.1", "webpack": "^5.38.1", "webpack-cli": "^4.7.0", "webpack-dev-server": "^3.11.2"}, "repository": {"type": "git", "url": "git+https://gitlab.com/lucent-backend-maintainer/app-symphony.git"}, "keywords": [], "bugs": {"url": "https://gitlab.com/lucent-backend-maintainer/app-symphony/issues"}, "homepage": "https://gitlab.com/lucent-backend-maintainer/app-symphony#readme"}