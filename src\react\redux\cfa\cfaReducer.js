import { cfaTypes } from "./cfaTypes";

const initialState = {
    loading: true,
    data: null,
    error: false,
    cfaData: null,
    pincodeExport: null
}
export default function cfaReducer(state = initialState, action) {
    switch (action.type) {
        case cfaTypes.GET_CFA_LIST_REQUEST:
            return { ...state, loading: true };

        case cfaTypes.GET_CFA_LIST_SUCCESS:
            return { ...state, data: action.payload, loading: false };

        case cfaTypes.GET_MANAGE_CFA_LIST_REQUEST:
            return { ...state, loading: true };

        case cfaTypes.GET_MANAGE_CFA_LIST_SUCCESS:
            return { ...state, data: action.payload, loading: false };

        case cfaTypes.SEND_CFA_FILE_REQUEST:
            return { ...state, error: false };

        case cfaTypes.SEND_CFA_FILE_SUCCESS:
            return { ...state, error: false };

        case cfaTypes.SEND_CFA_FILE_ERROR:
            return { ...state, error: true };

        case cfaTypes.SEND_MANAGE_CFA_FILE_REQUEST:
            return { ...state, error: false };

        case cfaTypes.SEND_MANAGE_CFA_FILE_SUCCESS:
            return { ...state, error: false };

        case cfaTypes.SEND_MANAGE_CFA_FILE_ERROR:
            return { ...state, error: true };

        case cfaTypes.GET_MANNUAL_CFA_LIST_REQUEST:
            return { ...state, cfaListloading: false };

        case cfaTypes.GET_MANNUAL_CFA_LIST_SUCCESS:
            return { ...state, cfaData: action.payload, cfaListloading: true };

        case cfaTypes.PINCODE_EXPORT_REQUEST:
            return { ...state, error: false };

        case cfaTypes.PINCODE_EXPORT_SUCCESS:
            return { ...state, error: false, pincodeExport: action.payload };

        case cfaTypes.PINCODE_EXPORT_ERROR:
            return { ...state, error: true };

        case cfaTypes.CFASTOCK_EXPORT_REQUEST:
            return { ...state, error: false };

        case cfaTypes.CFASTOCK_EXPORT_SUCCESS:
            return { ...state, error: false, pincodeExport: action.payload, loading: false };

        case cfaTypes.CFASTOCK_EXPORT_ERROR:
            return { ...state, error: true, loading: false };

        case cfaTypes.EXPORT_CFA_REQUEST:
            return { ...state, error: false };

        case cfaTypes.EXPORT_CFA_SUCCESS:
            return { ...state, error: false, cfaData: action.payload };

        case cfaTypes.EXPORT_CFA_ERROR:
            return { ...state, error: true };

        default:
            return state;
    }
}