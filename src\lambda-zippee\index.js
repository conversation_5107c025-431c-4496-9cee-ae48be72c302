const db = require('./db');
const { pincodeValidator } = require('./pincode-validator');

exports.handler = async (event) => {
  if (event.requestContext && event.requestContext.http.method === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Content-Type",
        "Access-Control-Allow-Methods": "POST, OPTIONS"
      },
      body: ''
    };
  }

  try {
    const { sku, pincode } = JSON.parse(event.body || '{}');
    const result = await pincodeValidator(sku, pincode);
    if (result.success) {
      await db.execute(
        'INSERT INTO pincode_validates (sku, pincode, response_message, createdAt, updatedAt) VALUES (?, ?, ?, NOW(), NOW())',
        [sku, pincode, result.message]
      );
    }
    return {
      statusCode: 200,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Content-Type",
        "Access-Control-Allow-Methods": "POST, OPTIONS"
      },
      body: JSON.stringify(result),
    };
  } catch (error) {
    console.error('Error:', error);
    return {
      statusCode: 500,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Content-Type",
        "Access-Control-Allow-Methods": "POST, OPTIONS"
      },
      body: JSON.stringify({
        success: false,
        message: 'Internal Server Error',
        error: error.message,
      }),
    };
  }
};
