const Sequelize = require("sequelize");
module.exports = (sequelize) => {
    return sequelize.define("escalation_mail", {
        id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            unique: true
        },
        email_id: {
            type: Sequelize.STRING
        },
        phone: {
            type: Sequelize.STRING,
        },
        complaint_number: {
            type: Sequelize.STRING,
        },
        name: {
            type: Sequelize.STRING,
        },
        escalation_type: {
            type: Sequelize.STRING,
        },
        feedback_type: {
            type: Sequelize.STRING,
        },
        feedback: {
            type: Sequelize.TEXT,
        },
        is_email_send: {
            allowNull: false,
            type: Sequelize.ENUM("0", "1"),
            defaultValue: "0"
        },
    },
        {
            indexes: [
                {
                    unique: true,
                    fields: ['phone', 'complaint_number']
                }
            ]
        });
};