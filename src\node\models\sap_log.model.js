const Sequelize = require("sequelize");
module.exports = (sequelize) => {
  return sequelize.define("sap_orders_logs", {
    id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      unique: true
    },
    store_id: {
      allowNull:false,
      type: Sequelize.INTEGER,
      references: {
        model: 'stores',
        key: 'id'
      },
    },
    order_id: {
      allowNull:false,
        type: Sequelize.STRING,
        // references: {
        //   model: 'orders',
        //   key: 'id'
        // },
      },
    sap_billing_number: {
      type: Sequelize.STRING,
    },
    sap_delivery_number:{
      type:Sequelize.STRING
    },
    sap_order_number:{
      type:Sequelize.STRING
    },
    shopify_order_name:{
      type:Sequelize.STRING
    },
    response_message: {
      type: Sequelize.STRING(500),
    },
    line_item_id:{
      type:Sequelize.STRING
    },
    order_name:{
      type:Sequelize.STRING
    },
    return_order_name:{
      type:Sequelize.STRING
    },
    plant_code:{
      type:Sequelize.STRING,
      defaultValue:null
    },
    is_cancel:{
      type:Sequelize.ENUM("0","1"),
      defaultValue:"0"
    },
    is_return:{
      type:Sequelize.ENUM("0","1"),
      defaultValue:"0"
    },
    return_created_date: {
      type: Sequelize.STRING,
      defaultValue: null
    },
    cn_created_date: {
      type: Sequelize.STRING,
      defaultValue: null
    },
    sap_confirmation_id: {
      type: Sequelize.STRING,
      defaultValue: null
    },
    is_clear:{
      type:Sequelize.ENUM("0","1"),
      defaultValue:"0"
    },
  },
    {
      indexes: [
        {
          name: 'unique_index',
          unique: true,
          fields: ['store_id','sap_billing_number','is_cancel','is_return']
        }
      ]
    });
};