const CONFIG = require('../config/config_' + [process.env.NODE_ENV || 'local'] + '.json');
const s3 = require("../helper/awsClient");
const client = require("ftp");
const fs = require("fs");
const path = require("path");
const dbConnection = require("../models");
const emailController = require('./email-controller');

exports.imageUpload = async (base64) => {
  const base64Data = new Buffer.from(base64.replace(/^data:image\/\w+;base64,/, ""), 'base64');
  const type = base64.split(';')[0].split('/')[1];
  let fileName = new Date().getTime()
  const params = {
    Bucket: CONFIG.aws.bucketName,
    Key: fileName + "." + type,
    Body: base64Data,
    ACL: "public-read",
    ContentEncoding: "base64",
    ContentType: `image/${type}`,
  };
  try {
    const imageUploadResponse = await s3.upload(params).promise();
    // const imageUrl = s3.getSignedUrl('getObject', {
    //   Bucket: CONFIG.aws.bucketName,
    //   Key: imageUploadResponse.key
    // })
    return imageUploadResponse.Location;
  } catch (error) {
    console.log("error==>", error);
  }
};

exports.ftpFileRead=(req,res)=>{
  this.readFile(req.body.names)
  res.send();
}

exports.readFile = async (orderNames) => {
  try {
    const config = {
      host: CONFIG.ftp.host,
      port: 21,
      user: CONFIG.ftp.user,
      password: CONFIG.ftp.password,
      connTimeout: 300000
    };
    let ftpClient = new client();
    ftpClient.connect(config);
    ftpClient.on('error', console.log)
    ftpClient.on("ready", async () => {
      for (i = 0; i < orderNames.length; i++) {
        let order = orderNames[i]
        let fileName = order.sap_billing_number
        let orderName = order.order_name
        let data = await this.getFTPFile(ftpClient, fileName, orderName)
      }
      ftpClient.end();
      return;
    });

  } catch (error) {
    // console.log("FIle Error==>", error);
    return;
  }
};
exports.getFTPFile = (ftpClient, fileName, orderName) => {
  return new Promise((resolve, reject) => {
    ftpClient.get(fileName + ".pdf", async (err, stream) => {
      console.log("getting files", fileName)
      if (err) {
        resolve()
        throw err
      };
      stream.once('close', function () {
        console.log("stream closed")
      });
      const writeStream = fs.createWriteStream(path.join(__dirname, `../uploads/${fileName + ".pdf"}`))
      stream.pipe(writeStream);
      writeStream.on('finish', function () {
        writeStream.destroy();
      }).on("close", async () => {
        console.log('File Closed ')
        const fileContent = fs.readFileSync(path.join(__dirname, `../uploads/${fileName + ".pdf"}`));
        var data = {
          Bucket: CONFIG.aws.bucketName,
          Key: "invoiceFiles/" + fileName + ".pdf",
          Body: fileContent,
          ContentEncoding: "base64",
          ACL: "public-read",
          ContentType: "application/pdf",
        };
        const awsData = await s3.upload(data).promise();
        if (awsData) {
          let invoiceUrl = awsData.Location;
          console.log("invoiceUrl===>", invoiceUrl)
          
          await dbConnection.orderItemSplit.update({
            invoice_url: invoiceUrl
          }, {
            where: {
              order_name: orderName,
            }
          });
         let emailData= await emailController.sendEmailToCustomer(orderName,invoiceUrl);
         resolve()
        } else {
          resolve()
          console.log("reerr")
        }
        fs.readdir(path.resolve(__dirname, "../uploads"), (err, files) => {
          if (err) throw err;
          for (const file of files) {
            fs.unlink(
              path.join(path.resolve(__dirname, "../uploads"), file),
              (err) => {
                if (err) throw err;
              }
            );
          }
        });
      })
    });
  })
}