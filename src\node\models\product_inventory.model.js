const Sequelize = require("sequelize");
module.exports = (sequelize) => {
  return sequelize.define("product_inventory", {
    id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      unique: true
    },
    store_id: {
      allowNull:false,
      type: Sequelize.INTEGER,
      references: {
        model: 'stores',
        key: 'id'
      },
    },
    available: {
      type: Sequelize.INTEGER,
    },
    cfa: {
      type: Sequelize.STRING,
    },
    sku: {
      type: Sequelize.STRING,
    },
},
    {
      indexes: [
        {
          unique: true,
          fields: ['store_id', 'id']
        }
      ]
    });
};