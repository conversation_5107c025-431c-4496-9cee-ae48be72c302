const CONFIG = require('../config/config_' + [process.env.NODE_ENV || 'local'] + '.json');
//Axios Call
const axios = require('axios')
//database connection
const dbConnection = require("../models");
const delhiveryController = require("./delhivery-controller")
const orderSyncController = require("./ordersync-controller")
const razorpayController = require("./razorpay-controller")
const orderHistoryHelper = require('../helper/orderHistory')
let buff = `${CONFIG.sap.userId}:${CONFIG.sap.pass}`
let accessToken = new Buffer(buff)
accessToken = accessToken.toString('base64')
const { Op } = require('sequelize');
const options = {
    headers: {
        "Accept": "application/json",
        "Authorization": 'Basic ' + accessToken
    }
}
exports.pushOrderOnSAP = async (data) => {
    return new Promise(async (resolve, reject) => {
        try {
            // resolve({status:true,data:{"SHOPIFY_NUM":"#39486","SAP_SO_NUM":"0050017057","SAP_DLY_NUM":"2100450074","SAP_SO_DATE":"2023-04-21","MESSAGE":[{"MESSAGE":"Sales Order created sucessfully"}]}})
            const sapRes = await axios.post(CONFIG.sap.SO_URL, data, options)
            if (sapRes.data) {
                if (sapRes.data.OUTPUT.SAP_SO_NUM != "" && sapRes.data.OUTPUT.SAP_DLY_NUM != "") {
                    resolve({ status: true, status_code: sapRes.status, data: sapRes.data.OUTPUT })
                } else {
                    resolve({ status: false, status_code: sapRes.status, data: sapRes.data.OUTPUT })
                }
            } else {
                resolve({ status: false, status_code: sapRes.status, data: sapRes })
            }
        } catch (err) {
            console.log('Error pushOrderOnSAP=====>', err)
            resolve({ status: false, status_code: err?.response?.status, data: { MESSAGE: [err?.response?.statusText] } })
        }
    })
}
exports.getSapReqBody = (sapParam) => {
    return new Promise(async (resolve, reject) => {
        try {
            let { orderData, docType, reason, plantCode, billNo, CustomerNum, Gstin, rate } = sapParam
            const orderCustomer = orderData.orderCustomers.length > 0 ? orderData.orderCustomers : []
            const orderAddress = orderData.orderAddresses.length > 0 ? orderData.orderAddresses : []
            let orderBillingAddress = orderAddress.length > 0 ? orderAddress[0] : null
            if (orderBillingAddress) {
                orderBillingAddress = orderBillingAddress
            }
            let shippingFirstName = ""
            if (orderAddress.length > 0) {
                shippingFirstName = orderAddress[0].first_name != null && orderAddress[0].first_name != "null" ? orderAddress[0].first_name : null
                if (shippingFirstName != null) {
                    if (shippingFirstName.length > 35) {
                        shippingFirstName = shippingFirstName.split(" ")[0]
                    }
                }
            }
            let billingFirstName = ""
            if (orderBillingAddress) {
                billingFirstName = orderBillingAddress.billing_first_name != null && orderBillingAddress.billing_first_name != "null" ? orderBillingAddress.billing_first_name : null
                if (billingFirstName != null) {
                    if (billingFirstName.length > 35) {
                        billingFirstName = billingFirstName.split(" ")[0]
                    }
                }
            }
            let orderCreatedAt = orderData.order_created_at.split('-')
            let orderDate = `${orderCreatedAt[0]}${orderCreatedAt[1]}${orderCreatedAt[2].split(" ")[0]}`
            let totalPrice = 0, basicPrice = 0, taxPrice = 0, discount = 0
            if (orderData.order_amount) {
                totalPrice = parseFloat(orderData.order_amount)
                discount = orderData.discount != null && orderData.discount != "null" ? parseFloat(orderData.discount) : 0
                discount += rate
                discount = parseFloat(discount / 1.18).toFixed(2)
                basicPrice = parseFloat(totalPrice / 1.18).toFixed(2)
                taxPrice = totalPrice - basicPrice
                console.log("toal amount==>", rate, discount, totalPrice, taxPrice)

            }
            let paymentMethod = orderData.gateway
            // if (orderData.gateway != null && orderData.gateway != "null") {
            //     console.log("orderDatagateway===>", orderData.gateway)
            //     if (orderData.gateway == "Cash on Delivery (COD)" || orderData.gateway == "Cash on Delivery (COD)")
            //         paymentMethod = "COD"
            //     if (orderData.gateway == "razorpay_cards_upi_netbanking_wallets_paypal_" || orderData.gateway == 'Razorpay Secure' || orderData.gateway == 'Razorpay')
            //         paymentMethod = "RAZORPAY"
            //     if (orderData.gateway == "cashfree")
            //         paymentMethod = "CFCHECKOUT"
            // }
            let shippingRegionCode = ""
            if (orderAddress.length > 0) {
                let province = orderAddress[0].province != null && orderAddress[0].province != "" && orderAddress[0].province != "null" ? orderAddress[0].province : ""
                if (province != '') {
                    let state = await dbConnection.stateRegionCode.findOne({
                        where: {
                            [Op.or]: [{
                                new_state: province
                            },
                            {
                                state: province
                            }]
                        }
                    })
                    if (state) {
                        shippingRegionCode = state.dataValues.region_code;
                    } else {
                        console.log("Province Error==>")
                    }
                }
            }
            let billingRegionCode = ""
            if (orderBillingAddress) {
                let province = orderBillingAddress.billing_province != null && orderBillingAddress.billing_province != "null" ? orderBillingAddress.billing_province : ""
                if (province != "") {
                    let state = await dbConnection.stateRegionCode.findOne({
                        where: {
                            [Op.or]: [{
                                new_state: province
                            },
                            {
                                state: province
                            }]
                        }
                    })
                    if (state) {
                        billingRegionCode = state.dataValues.region_code
                    } else {
                        console.log("billing Address Province Error==>")
                    }
                }
            }
            let billingName1 = orderBillingAddress ? orderBillingAddress.billing_last_name != null && orderBillingAddress.billing_last_name != "null" ? orderBillingAddress.billing_last_name : "" : ""
            let gstfirstName = ''
            if (Gstin) {
                let fullGstName = orderBillingAddress.billing_company.split(/\s+(.+)/);
                gstfirstName = fullGstName[0]
                billingName1 = fullGstName[1] || ''
            }
            const streetAddress = this.getStreetAddress(orderAddress, orderBillingAddress)

            const body = {
                "ORDER_HEADER_IN": {
                    "SHOPIFY_NUM": orderData.shopify_order_name,
                    "DOC_TYPE": docType,
                    "DOC_DATE": orderDate,
                    "REF_1": orderData.order_name,
                    "ORD_REASON": reason ? reason : "", // ""->forward , 1-return, 2-replacement
                    "BILL_NUM": billNo ? billNo : ""
                },
                "ORDER_ITEMS_IN": {
                    "ITM_NUMBER": "10",
                    "MATERIAL": orderData.sku,
                    "BILL_DATE": orderDate,
                    "PLANT": plantCode,
                    "STORE_LOC": "1007",
                    "PURCH_NO_C": paymentMethod == "COD" ? orderData.order_name : orderData.checkout_id, // payment transaction id,
                    "PURCH_NO_S": orderData.order_name ? orderData.order_name : "", // secondary order number
                    "POITM_NO_S": "",
                    "SALQTYNUM": "1",
                    "ITEM_CATEG": "ZTAN", // Fixed as of now
                    "PRICE": `${orderData.order_amount}`,
                    "BASIC_AMOUNT": `${basicPrice}`, //  query
                    "DISCOUNT_AMOUNT": `${discount}`,
                    "TAX_PERCENT": '18',//orderData.tax_percentage != null && orderData.tax_percentage != "null" ? orderData.tax_percentage : "", // add column in table
                    "TAX_AMOUNT": taxPrice != "" ? `${parseFloat(taxPrice).toFixed(2)}` : "",
                    "TOTAL_AMOUNT": `${totalPrice - discount}`,
                    "PAYMENT_METHODS": paymentMethod // orderData.financial_status == "paid" ? "Prepaid" : "COD",
                },
                "ORDER_PARTNERS": {
                    "NAME": shippingFirstName,
                    "NAME_2": orderAddress.length > 0 ? orderAddress[0].last_name != null && orderAddress[0].last_name != "null" ? orderAddress[0].last_name : "" : "",
                    "STREET": "", // query
                    "COUNTRY": orderAddress.length > 0 ? orderAddress[0].country_code != null && orderAddress[0].country_code != "null" ? orderAddress[0].country_code : "" : "",
                    "POSTL_CODE": orderAddress.length > 0 ? orderAddress[0].zip_code != null && orderAddress[0].zip_code != "null" ? orderAddress[0].zip_code : "" : "",
                    "CITY": orderAddress.length > 0 ? orderAddress[0].city != null && orderAddress[0].city != "null" ? orderAddress[0].city : "" : "",
                    "DISTRICT": "", // query
                    "REGION": shippingRegionCode,
                    "TELEPHONE": orderAddress.length > 0 ? orderAddress[0].phone != null && orderAddress[0].phone != "null" ? orderAddress[0].phone.replaceAll(' ', "") : "" : "",
                    "LANGU": "EN",
                    "ADDRESS": orderAddress.length > 0 ? orderAddress[0].address1 != null && orderAddress[0].address1 != "null" ? orderAddress[0].address1 : "" : ""
                },
                "BP_PARTNERADDRESSES": { // query solved
                    "CUST_NUM": CustomerNum ? CustomerNum : "",
                    "GSTIN": Gstin ? Gstin.toUpperCase() : "",
                    "NAME": Gstin ? gstfirstName : billingFirstName,
                    "NAME_2": billingName1,
                    "CITY": orderBillingAddress ? orderBillingAddress.billing_city != null && orderBillingAddress.billing_city != "null" ? orderBillingAddress.billing_city : "" : "",
                    "POSTL_COD1": orderBillingAddress ? orderBillingAddress.billing_zip_code != null && orderBillingAddress.billing_zip_code != "null" ? orderBillingAddress.billing_zip_code : "" : "",
                    "STREET": streetAddress.billingStreet,
                    "STREET_NO": "",
                    "HOUSE_NO": "",
                    "STR_SUPPL1": streetAddress.billingStreet1,
                    "STR_SUPPL2": streetAddress.billingStreet2,
                    "STR_SUPPL3": streetAddress.billingStreet3,
                    "COUNTRY": orderBillingAddress ? orderBillingAddress.billing_country_code != null && orderBillingAddress.billing_country_code != "null" ? orderBillingAddress.billing_country_code : "" : "",
                    "LANGU": "EN",
                    "REGION": billingRegionCode,
                    "TEL1_NUMBR": orderBillingAddress ? orderBillingAddress.billing_phone != null && orderBillingAddress.billing_phone != "null" ? orderBillingAddress.billing_phone.replaceAll(' ', "") : "" : "",
                    "E_MAIL": orderCustomer.length > 0 ? orderCustomer[0].customer_email != null && orderCustomer[0].customer_email != "null" ? orderCustomer[0].customer_email : "" : ""
                },
                "SH_PARTNERADDRESSES": { // query solved
                    "NAME": shippingFirstName,
                    "NAME_2": orderAddress.length > 0 ? orderAddress[0].last_name != null && orderAddress[0].last_name != "null" ? orderAddress[0].last_name : "" : "",
                    "CITY": orderAddress.length > 0 ? orderAddress[0].city != null && orderAddress[0].city != "null" ? orderAddress[0].city : "" : "",
                    "POSTL_COD1": orderAddress.length > 0 ? orderAddress[0].zip_code != null && orderAddress[0].zip_code != "null" ? orderAddress[0].zip_code : "" : "",
                    "STREET": streetAddress.shippingStreet,
                    "STREET_NO": "",
                    "HOUSE_NO": "",
                    "STR_SUPPL1": streetAddress.shippingStreet1,
                    "STR_SUPPL2": streetAddress.shippingStreet2,
                    "STR_SUPPL3": streetAddress.shippingStreet3,
                    "COUNTRY": orderAddress.length > 0 ? orderAddress[0].country_code != null && orderAddress[0].country_code != "null" ? orderAddress[0].country_code : "" : "",
                    "LANGU": "EN",
                    "REGION": shippingRegionCode,
                    "TEL1_NUMBR": orderAddress.length > 0 ? orderAddress[0].phone != null && orderAddress[0].phone != "null" ? orderAddress[0].phone.replaceAll(' ', "") : "" : "",
                    "E_MAIL": orderCustomer.length > 0 ? orderCustomer[0].customer_email != null && orderCustomer[0].customer_email != "null" ? orderCustomer[0].customer_email : "" : ""
                },
                "ORDER_SCHEDULES_IN": {
                    "ITM_NUMBER": "10",
                    "SCHED_LINE": "0001",
                    "REQ_DATE": orderDate,
                    "REQ_QTY": "1"
                },
                "ORDER_CONDITIONS_IN": {
                    "ITM_NUMBER": "10",
                    "COND_TYPE": docType,
                    "COND_VALUE": `${orderData.order_amount}`,
                    "CURRENCY": "INR"
                }
            }
            console.log("BodyData==>", JSON.stringify(body))
            resolve({ status: true, data: body })
        } catch (err) {
            console.log('Error getSapReqBody=====>', err)
        }
    })
}
// get street addresss
exports.getStreetAddress = (orderAddress, orderBillingAddress) => {
    try {
        let shippingStreet = "", shippingStreet1 = "", shippingStreet2 = "", shippingStreet3 = ""
        let billingStreet = "", billingStreet1 = "", billingStreet2 = "", billingStreet3 = ""
        if (orderAddress.length > 0) {
            let address = orderAddress.length > 0 ? orderAddress[0].address1 != null && orderAddress[0].address1 != "null" ? orderAddress[0].address1 : "" : ""
            if (address != "") {
                if (address.length <= 40) {
                    shippingStreet = address
                } else {
                    shippingStreet = address.slice(0, 40)
                    shippingStreet1 = address.slice(40, address.length)
                    if (shippingStreet1.length > 41) {
                        shippingStreet1 = shippingStreet1.slice(0, 40)
                        shippingStreet2 = shippingStreet1.slice(40, address.length)
                        if (shippingStreet2.length > 41) {
                            shippingStreet2 = shippingStreet2.slice(0, 40)
                            shippingStreet3 = shippingStreet2.slice(40, address.length)
                        }
                    }
                }
            }
        }
        if (orderBillingAddress) {
            let address = orderBillingAddress.billing_address1 != null && orderBillingAddress.billing_address1 != "null" ? orderBillingAddress.billing_address1 : ""
            if (address != "") {
                if (address.length <= 40) {
                    billingStreet = address
                } else {
                    billingStreet = address.slice(0, 40)
                    billingStreet1 = address.slice(40, address.length)
                    if (billingStreet1.length > 40) {
                        billingStreet1 = billingStreet1.slice(0, 40)
                        billingStreet2 = billingStreet1.slice(40, address.length)
                        if (billingStreet2.length > 40) {
                            billingStreet2 = billingStreet2.slice(0, 40)
                            billingStreet3 = billingStreet2.slice(40, address.length)
                        }
                    }
                }
            }
        }
        let addressObj = {
            shippingStreet: shippingStreet,
            shippingStreet1: shippingStreet1,
            shippingStreet2: shippingStreet2,
            shippingStreet3: shippingStreet3,
            billingStreet: billingStreet,
            billingStreet1: billingStreet1,
            billingStreet2: billingStreet2,
            billingStreet3: billingStreet3
        }
        return addressObj
    } catch (err) {
        console.log('Error getStreetAddress=====>', err)
    }
}

// get inventory from SAP
exports.inventoryUpdate = () => {
    return new Promise(async (resolve, reject) => {
        try {
            const obj = {}
            await axios.post(CONFIG.sap.INV_URL, obj, options).then(async inventoryRes => {
                let helperObj = {
                    platform_type: "SAP-Inventory",
                    response_status_code: inventoryRes.status,
                    response_data: inventoryRes.data ? JSON.stringify(inventoryRes.data) : JSON.stringify(inventoryRes)
                }
                await orderHistoryHelper.insertOrderHistory(helperObj)
                if (inventoryRes.data.length > 0) {
                    for (let invRes of inventoryRes.data) {
                        // Check if the SKU exists in the productVariant table
                        const skuExists = await dbConnection.productVariant.findOne({
                            where: { sku: invRes.Material }
                        });

                        const quantity = invRes?.Quantity || "0";
                        if (skuExists) {
                            // Check if a stock mapping already exists for the SKU and pincode group (Plant)
                            const existingMapping = await dbConnection.cfaStockMapping.findOne({
                                where: {
                                    sku: invRes.Material,
                                    pincode_group: invRes.Plant
                                }
                            });

                            if (existingMapping) {
                                // If mapping exists, update the stock and local_stock values
                                await existingMapping.update({
                                    stock: quantity,
                                    local_stock: quantity
                                });
                            } else {
                                // If no mapping exists, create a new one with the relevant data
                                await dbConnection.cfaStockMapping.create({
                                    sku: invRes.Material,
                                    pincode_group: invRes.Plant,
                                    stock: quantity,
                                    local_stock: quantity,
                                    store_id: skuExists.store_id,
                                });
                            }
                        }
                    }
                }
            }).catch(err => { console.log("Inventory sync error", err) })
            resolve()

        } catch (err) {
            resolve()
            console.log("Catch Error in inventory stock sync ===>", err)
        }
    })
}

//Function to SYNC SAP Inventory Manually
exports.syncSapInventory = async (req, res) => {
    try {
        await this.inventoryUpdate()
        res.status(200).send({ status: 200, message: "Sucess" })
    } catch (err) {
        res.status(200).send({ status: 200, message: JSON.stringify(err) })
        console.log("Eror", err)
    }
}

// SAP delivery API call
exports.getSapBillingNumber = (data, orderStatus = null, orderName) => {
    return new Promise(async (resolve, reject) => {
        try {
            const deliveryRes = await axios.post(CONFIG.sap.DEL_URL, data, options)
            let delRes = deliveryRes.data.length > 0 ? deliveryRes : null
            if (delRes) {
                if (deliveryRes.data[0].SapBillNum != "" && deliveryRes.data[0].SapBillNum != null && deliveryRes.data[0].SapBillNum != undefined) {
                    resolve({ status: true, isInvoice: true, status_code: deliveryRes.status, data: deliveryRes.data[0] })
                } else {
                    let isCancel = deliveryRes.data[0].Message.find(msg => msg.message == "Delivery is Blocked")
                    isCancel ? resolve({ isCancel: true, status_code: deliveryRes.status, data: deliveryRes.data[0] }) : resolve({ status: false, status_code: deliveryRes.status, data: deliveryRes.data[0] })
                }
            } else {
                resolve({ status: false, status_code: deliveryRes.status, data: deliveryRes.data })
            }
        } catch (err) {
            console.log('Error getSapBillingNumber=====>', err)
            resolve({ status: false, status_code: err?.response?.status || null, data: err?.response?.data || {} })
        }
    })
}

//Function for create return sale order in SAP
exports.returnSaleOrderSap = async (orderData) => {
    try {

        let giftCardAmout = parseFloat(orderData.order_amount) * parseFloat(orderData.gift_card_value)
        orderData.order_amount = parseFloat(orderData.order_amount) - giftCardAmout

        let sapParam = {
            orderData: orderData,
            docType: "ZWRP",
            reason: "Y02",
            plantCode: orderData.plant_code,
            rate: 0,
            billNo: orderData.sap_billing_number
        }
        //let shopResponse = await dbConnection.shop.findOne({ where: { id: orderData.store_id } })
        // const payId = await razorpayController.getPaymentId(shopResponse.dataValues.myshopify_domain, shopResponse.dataValues.token, orderData.shopify_order_id)
        // if (payId.status) {
        //     await dbConnection.orderItemSplit.update({ checkout_id: payId.payId }, { where: { order_name: orderData.order_name } })
        // }

        // let orderResData = await orderSyncController.getorderSplitDataByName(orderData.order_name)
        // let item = JSON.parse(JSON.stringify(orderResData.data))

        const sapReqData = await this.getSapReqBody(sapParam)
        if (sapReqData.status) {
            const sapData = await this.pushOrderOnSAP(sapReqData.data)
            let helperObj = {
                shopify_order_id: orderData.shopify_order_id,
                shopify_order_name: orderData.shopify_order_name,
                order_name: orderData.order_name,
                platform_type: "SAP-Return",
                request_data: JSON.stringify(sapReqData.data),
                response_status_code: sapData.status_code,
                response_data: sapData.data ? JSON.stringify(sapData.data) : JSON.stringify(sapData)
            }
            await orderHistoryHelper.insertOrderHistory(helperObj)
            const sapObj = {
                store_id: orderData.store_id,
                order_id: orderData.shopify_order_id,
                sap_billing_number: null,
                line_item_id: orderData.line_item_id,
                order_name: orderData.order_name,
                plant_code: orderData.plant_code,
                return_order_name: orderData.return_order_name,
                is_return: "1"
            }
            const sapLogResponse = await dbConnection.sapLog.findOne({ where: { order_name: orderData.order_name, is_return: "1" } })
            let orderDataObj = {}
            if (sapData.status) {
                sapObj.sap_delivery_number = sapData.data.SAP_DLY_NUM
                sapObj.sap_order_number = sapData.data.SAP_SO_NUM
                sapObj.shopify_order_name = sapData.data.SHOPIFY_NUM
                sapObj.return_created_date = new Date().toISOString(),
                    sapObj.response_message = sapData?.data?.MESSAGE?.length > 0 ? sapData.data.MESSAGE[0].MESSAGE : "Didn't get any response from SAP"
                orderDataObj = {
                    sap_order_number: sapData.data.SAP_SO_NUM,
                    sap_delivery_number: sapData.data.SAP_DLY_NUM,
                    sap_status: "Pushed",
                    order_status: "Returned",
                    plant_code: orderData.plant_code,
                    sap_billing_number: null,
                    is_return: "1"
                }
            } else {
                orderDataObj = {
                    sap_status: "Failed",
                    is_return: "1",
                    middleware_status: "FAILED"
                }
                sapObj.response_message = sapData?.data?.MESSAGE[0].MESSAGE
            }
            await dbConnection.orderItemSplit.update(orderDataObj, { where: { order_name: orderData.order_name } })
            sapLogResponse ? await dbConnection.sapLog.update(sapObj, { where: { order_name: orderData.order_name, is_return: "1" } }) : await dbConnection.sapLog.create(sapObj)
        }
    } catch (err) {
        console.log("Error returnSaleOrderSap====>", err)
    }
}

//API Function to call return sale order method
exports.createReturnSaleOrder = async (req, res) => {
    try {
        let orderNames = req.body.orders
        for (let orderName of orderNames) {
            let orderData = await delhiveryController.getorderSplitDataById(orderName)
            await this.returnSaleOrderSap(orderData.data)
        }
        res.status(200).send({ status: true, message: "success" })
    } catch (err) {
        res.status(500).send({ status: false, message: "Error" })
    }
}

exports.sapConfirmationId = async (sapOrderLogs) => {
    try {
        let data = [];
        for (const sapLog of sapOrderLogs) {
            const obj = {
                BILLING_DOC: sapLog.dataValues.sap_billing_number,
            };

            data.push(obj);
            if (data.length === 100) {
                await exports.getSapConIds(data);
                data = [];
            }
        }

        if (data.length > 0) {
            await exports.getSapConIds(data);
        }
    } catch (err) {
        console.error("Error getClrDoc=====>", err);
        throw err;
    }
};

exports.getSapConIds = async (data) => {
    try {
        const sapRes = await axios.post(CONFIG.sap.CLR_URL, data, options);

        //store in OrderHistory
        let helperObj = {
            platform_type: "SAP-Confirmation-Id",
            request_data: JSON.stringify(data),
            response_status_code: sapRes.status,
            response_data: sapRes ? JSON.stringify(sapRes.data) : JSON.stringify(sapRes)
        }
        await orderHistoryHelper.insertOrderHistory(helperObj)

        const sapResponse = sapRes.data.length > 0 ? sapRes.data : null;
        if (sapResponse) {
            for (const response of sapResponse) {
                if (response.STATUS === "YES") {
                    const saporderDataObj = {
                        is_clear: "1",
                        sap_confirmation_id: response.CLEAR_DOC_NO,
                    };
                    await dbConnection.sapLog.update(saporderDataObj, { where: { sap_billing_number: response.BILLING_DOC } });
                }
            }
        }
    } catch (err) {
        console.error("Error getSapConIds=====>", err);
        throw err;
    }
};