module.exports = app => {
    const crmController = require("../controllers/crm-controller.js");
    var router = require("express").Router();
  
    // Login API Routes
    router.post("/warranty/registration", crmController.warrantyRegistration); // warrany API ->4
    router.post("/log/complaint", crmController.warrantyRegistration); // warranty API ->3
    router.post("/log/check/user", crmController.logComplaint); // log a complaint ->1
    router.post("/log/registration", crmController.logComplaint); // log a complaint ->2
    router.post("/track/complaint", crmController.trackComplaint); // track a log
    router.get("/pincode/getdetails",crmController.getCityAndStateCode);
    router.post("/autowarranty/registration",crmController.autoWarrantyRegistration);
    router.post("/log/escalation",crmController.escalationEmail);
    app.use('/crm', router);
  };
  