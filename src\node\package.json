{"name": "symphony-app", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "webpack --mode production", "start": "node src/node/index.js", "client": "webpack --mode development --watch", "server": "nodemon src/node/index.js"}, "author": "lucent", "license": "ISC", "dependencies": {"@shopify/app-bridge": "^1.29.0", "@shopify/app-bridge-react": "^1.29.0", "@shopify/app-bridge-utils": "^1.29.0", "@shopify/polaris": "^6.1.0", "@shopify/polaris-icons": "^4.5.0", "@shopify/shopify-api": "^2.0.0", "async": "^3.2.2", "aws-sdk": "^2.1046.0", "axios": "^0.24.0", "babel-polyfill": "^6.26.0", "base-64": "^1.0.0", "body-parser": "^1.19.0", "canvas": "^2.11.2", "cors": "^2.8.5", "dotenv": "^10.0.0", "express": "^4.17.1", "fs": "^0.0.1-security", "ftp": "^0.3.10", "ftp-client": "^0.2.2", "jsbarcode": "^3.11.6", "json-as-xlsx": "^2.4.0", "jsonwebtoken": "^8.5.1", "moment": "^2.29.1", "multer": "^1.4.4", "mysql2": "^2.3.3", "node-cron": "^3.0.0", "nodemailer": "^6.7.5", "nodemon": "^2.0.15", "path": "^0.12.7", "pdf-creator-node": "^2.2.4", "pdf-merger-js": "^3.2.1", "pdfkit": "^0.13.0", "randomstring": "^1.2.1", "react": "^16.5.2", "react-dom": "^16.5.2", "react-hot-loader": "^4.5.3", "react-redux": "^7.2.2", "react-router-dom": "^5.2.0", "react-scripts": "4.0.1", "redux": "^4.0.5", "redux-logger": "^3.0.6", "redux-persist": "^6.0.0", "redux-saga": "^1.1.3", "sequelize": "^6.9.0", "shopify-jwt-auth-verify": "^1.0.10", "styled-components": "^5.3.0", "winston": "^3.3.3", "xlsx": "^0.17.4"}}