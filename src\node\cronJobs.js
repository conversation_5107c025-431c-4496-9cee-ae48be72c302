const cron = require("node-cron");
const { Op, QueryTypes, where } = require("sequelize");
const sapController = require("./controllers/sap_controller");
const delhiveryController = require("./controllers/delhivery-controller");
const { processWarrantyPurchase, createOrderOnSAPforWarranty, getSapInvoiceNumber, createWarrantyFulfillment } = require("./controllers/extendedWarranty/extendedWarrantyService-controller");
const orderSynController = require("./controllers/ordersync-controller");
const ftpController = require("./controllers/ftp-controller");
const moment = require('moment');
const currentDate = moment().format('DD-MM-YY HH:mm:ss');
const db = require("./models");
const { crmWarrantyRegistration, getCallList } = require("./controllers/crm-controller");
const Orders = require("./shopifyObjects/Order");


module.exports = () => {
  if (process.env.NODE_ENV == "production") {

    //Every 30 minutes, Monday to Friday
    cron.schedule("*/15 * * * *", async () => {
      console.log("getOrderNamesForPush order push", currentDate)
      getOrderNamesForPush();
    });


    //Every 15 minutes, Monday to Friday
    cron.schedule("*/7 * * * *", async () => {
      console.log("sapBillingOrder invoice", currentDate)
      //sapBillingOrder();
      fulfillOrders();
      createWarrantyFulfillment();
    });

    cron.schedule("45 3,4,5,6,7,8,9,10,11,12,13,14,15 * * *", async () => {
      console.log("ftpReadFile order", currentDate)
      ftpReadFile();
      sapCqBillingOrder();
    });

    //Every lahf hour,
    cron.schedule("0/30 * * * *", async () => {
      cancelButtonDisabled()
    });

    //Every hour,
    cron.schedule("0 * * * *", async () => {
      getFailedOrderPush()
      warrantyRegistration();
    });

    cron.schedule("*/30 * * * *", async () => {
      sapBillingOrdereveryhour();
      //createExtendedWarranty();
      getWarrantySOInvoiceNumber();
      failedSAPExtendedWarranty();
      createExtendedWarranty();
    });

    //Every hour,
    cron.schedule("30 22 * * *", async () => {
      // stock update from SAP to Database
      console.log("inventoryUpdate", currentDate)
      sapController.inventoryUpdate();
      getCallListStatus();
      getCallListStatusforServiceLog();
      updateTotalOrder();
    });

    //Every 24 hours
    cron.schedule("0 0 * * *", async () => {
      clearOrderLogs();
      getSapConfirmation();
      orderSynController.sendMailPendingCQ();
    });
  }

  const getOrderNamesForPush = async () => {
    const twoHoursAgo = new Date(Date.now() - 1 * 60 * 60 * 1000);
    const formattedDate = twoHoursAgo.toISOString().slice(0, 19).replace("T", " ");
    const orderNames = await db.sequelize.query(
      `SELECT * FROM order_item_splits WHERE middleware_status NOT IN ('FAILED', 'OLD ORDERS', 'OUT OF STOCK')
      AND (delhivery_status='Pending' OR delhivery_status='Failed')
      AND (sap_status='Pending' OR sap_status='Failed' OR sap_status='Pushed')
      AND is_cancel!='1' 
      AND is_return!='1'
      AND is_order_hold='0' 
      AND order_created_at<='${formattedDate}'
      LIMIT 25`,
      { type: QueryTypes.SELECT }
    );

    const order = [];
    if (orderNames.length > 0) {
      for (const orderName of orderNames) {
        order.push(orderName.order_name);
      }
      db.orderItemSplit.update({ is_pickup: "1" }, { where: { order_name: order } })
      await orderSynController.pushOrderOnDelhiveryAndSap(order);
    }
  };

  //fulfill orders
  const fulfillOrders = async () => {
    const fulfillorderData = await db.orderItemSplit.findAll({
      where: {
        fulfillment_status: {
          [Op.in]: ['failed', 'pending']
        },
        order_status: "completed"
      },
      limit: 15
    });
    for (const orderItem of fulfillorderData) {
      let shopResponse = await db.shop.findOne({
        where: { id: orderItem.store_id },
      });
      const paramObj = {
        line_item_id: orderItem.line_item_id,
        price: orderItem.order_amount,
        orderId: orderItem.shopify_order_id,
        orderRef: orderItem.id,
        storeId: orderItem.store_id,
        status: "confirmed",
        waybill: orderItem.waybill_number,
        shopResponse,
      };
      setTimeout(async () => {
        await delhiveryController.createFulfillment(paramObj);
      }, 15000);
    }
  };
  const cancelButtonDisabled = async () => {
    const twoHoursAgo = new Date(Date.now() - 1 * 60 * 60 * 1000);
    const formattedDate = twoHoursAgo.toISOString().slice(0, 19).replace("T", " ");

    await db.orderItemSplit.update(
      { is_pickup: "1" },
      {
        where: {
          middleware_status: { [Op.notIn]: ['FAILED', 'OLD ORDERS'] },
          delhivery_status: { [Op.in]: ['Pending', 'Failed'] },
          sap_status: { [Op.in]: ['Pending', 'Failed', 'Pushed'] },
          is_cancel: { [Op.ne]: '1' },
          is_return: { [Op.ne]: '1' },
          is_order_hold: '0',
          order_created_at: { [Op.lte]: formattedDate }
        }
      }
    );
  };

  // ftp read file
  const ftpReadFile = async () => {
    let ftporderData = await db.sequelize.query(
      "SELECT order_name,sap_billing_number FROM order_item_splits WHERE middleware_status NOT IN ('FAILED', 'OLD ORDERS') And sap_billing_number IS NOT NULL AND sap_status='Invoiced' AND invoice_url IS NULL order by order_created_at desc LIMIT 100",
      { type: QueryTypes.SELECT }
    );
    if (ftporderData.length > 0) {
      await ftpController.readFile(ftporderData);
    }
  };

  const clearOrderLogs = async () => {
    let today = new Date();
    let thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(today.getDate() - 30);

    let deleteLogs = db.orderLogHistory.destroy({
      where: {
        createdAt: { [Op.lt]: thirtyDaysAgo },
      },
    });
  };

  const updateTotalOrder = async () => {
    const sapOrderLogs = await db.cfaPlantLocation.update({ total_order: 0 },
      {
        where: {
          order_limit: { [Op.gt]: 0 },
        },
      })
  };

  const getSapConfirmation = async () => {
    const sapOrderLogs = await db.sapLog.findAll({
      where: {
        sap_billing_number: { [Op.ne]: null },
        sap_Confirmation_id: null,
        is_clear: "0",
      },
    });
    await sapController.sapConfirmationId(sapOrderLogs);
  };

  const warrantyRegistration = async () => {
    const twoDaysAgo = new Date(Date.now() - 2 * 24 * 60 * 60 * 1000);
    const formattedDate = twoDaysAgo.toISOString().slice(0, 19).replace("T", " ");
    const orderNames = await db.sequelize.query(
      `SELECT order_name,warranty_code FROM symphony.order_item_splits
        WHERE is_replaceable = '1' AND middleware_status = 'DELIVERED'AND delivered_at < '${formattedDate}'
        ORDER BY delivered_at DESC
        LIMIT 25`,
      { type: QueryTypes.SELECT }
    );

    if (orderNames.length > 0) {
      for (const orderName of orderNames) {
        console.log('warrantyRegistration->>>', orderName.order_name)
        await db.orderItemSplit.update({ is_replaceable: "0" }, { where: { order_name: orderName.order_name } })
        orderName.warranty_code == null ? await crmWarrantyRegistration(orderName.order_name) : ""
      }
    }
  };

  // CQ Billing Order
  const sapCqBillingOrder = async () => {
    const oneHoursAgo = new Date(Date.now() - 15 * 60 * 1000);
    const formattedDate = oneHoursAgo.toISOString().slice(0, 19).replace("T", " ");
    let orderNames = await db.sequelize.query(
      `SELECT order_name FROM order_item_splits WHERE (is_return ='1' OR is_cancel ='1')
      AND (middleware_status ='CANCELLED' OR middleware_status='RETURNED TO WAREHOUSE' OR middleware_status='RTO RETURNED TO WAREHOUSE') 
      AND updatedAt<='${formattedDate}'
      order by updatedAt DESC`,
      { type: QueryTypes.SELECT }
    );
    if (orderNames.length > 0) {
      for (let orderName of orderNames) {
        await delhiveryController.sapDelivery(orderName.order_name);
      }
    }
  };

  const getFailedOrderPush = async () => {
    const orderNames = await db.sequelize.query(
      `SELECT * FROM order_item_splits WHERE middleware_status ='FAILED'
      AND (delhivery_status='Pending' OR delhivery_status='Failed')
      AND (sap_status='Pending' OR sap_status='Failed' OR sap_status='Pushed')
      AND is_cancel!='1' 
      AND is_return!='1'
      AND is_order_hold='0'
      LIMIT 15`,
      { type: QueryTypes.SELECT }
    );

    const order = [];
    if (orderNames.length > 0) {
      for (const orderName of orderNames) {
        order.push(orderName.order_name);
      }
      console.log('getFailedOrderPush order--->', order)
      db.orderItemSplit.update({ is_pickup: "1" }, { where: { order_name: order } })
      await orderSynController.pushOrderOnDelhiveryAndSap(order);
    }
  };

  const sapBillingOrdereveryhour = async () => {
    let orderNames = await db.sequelize.query(
      `SELECT order_name, order_created_at  FROM order_item_splits WHERE middleware_status NOT IN ('FAILED', 'OLD ORDERS') 
      AND sap_status ='Pushed' 
      AND is_return!='1' 
      AND is_cancel!='1' 
      AND delhivery_status= 'Pushed'
      order by order_created_at ASC`,
      { type: QueryTypes.SELECT }
    );
    if (orderNames.length > 0) {
      for (let orderName of orderNames) {
        await delhiveryController.sapDelivery(orderName.order_name);
        await sleep(3000); // 3 second break
      }
    }
  };

  function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  //cron for create Extended Warranty
  const createExtendedWarranty = async () => {
    try {
      // Calculate the date 7 days ago (register warranty after delivery of 7 days)
      const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      const formattedDate = sevenDaysAgo.toISOString().slice(0, 19).replace("T", " ");

      // Fetch all required data in a single query using a join
      const ordersData = await db.extendedWarrantyOrder.findAll({
        include: [
          {
            model: db.orderItemSplit,
            attributes: ["product_serial_number", "sku", "product_title"],
            as: "orderItemSplit",
            required: true,
            where: { middleware_status: "DELIVERED", delivered_at: { [Op.lte]: formattedDate } },
          },
          { model: db.orderCustomer, attributes: { exclude: ["createdAt", "updatedAt"] }, as: "orderCustomers", required: true },
          { model: db.orderAddress, attributes: { exclude: ["createdAt", "updatedAt"] }, as: "orderAddresses", required: true },
        ],
        where: { middleware_status: { [Op.in]: ["INVOICED", "FAILED"] }, sap_status: "invoiced" },
        raw: true,
        nest: true,
      });

      // Process the warranty purchase if data is found
      if (ordersData.length > 0) {
        console.log(`Processing ${ordersData.length} extended warranty orders.`);
        await processWarrantyPurchase(ordersData);
      } else {
        console.log("No eligible orders found for extended warranty registration.");
      }
    } catch (error) {
      console.error("Error in createExtendedWarranty cron:", error);
    }
  };

  const failedSAPExtendedWarranty = async () => {
    try {
      const ordersData = await db.extendedWarrantyOrder.findAll({ where: { middleware_status: "FAILED", sap_status: "failed" }, raw: true });
      if (ordersData.length) {
        for (let orderData of ordersData) {
          await createOrderOnSAPforWarranty(orderData.main_order_name)
        }
      }
    } catch (error) {
      console.error("Error in failedSAPExtendedWarranty cron:", error);
    }
  };

  const createSapOrderExtendedWarranty = async () => {
    try {
      const ordersData = await db.extendedWarrantyOrder.findAll({
        include: [
          {
            model: db.orderItemSplit, attributes: ["product_serial_number", "store_id", "shopify_order_id"],
            as: "orderItemSplit",
            required: true,
            where: { middleware_status: "DELIVERED" }
          },
        ],
        where: { middleware_status: "PENDING", sap_status: "pending" },
        raw: true,
        nest: true,
      });

      if (ordersData.length) {
        for (let orderData of ordersData) {
          await createOrderOnSAPforWarranty(orderData.main_order_name)
          let shopResponse = await db.shop.findOne({ where: { id: orderData?.orderItemSplit?.store_id } });
          let orderUpdate = new Orders(shopResponse?.myshopify_domain, shopResponse?.token, orderData?.orderItemSplit?.store_id)
          await orderUpdate.update("extened_warranty")
        }
      }
    } catch (error) {
      console.error("Error in createSapOrderExtendedWarranty cron:", error);
    }
  };

  const getWarrantySOInvoiceNumber = async () => {
    try {
      const ordersData = await db.extendedWarrantyOrder.findAll({
        where: { sap_status: "pushed", middleware_status: ["PROCESSING", "FAILED"] },
        raw: true,
      });

      if (ordersData.length) {
        for (let orderData of ordersData) {
          await getSapInvoiceNumber(orderData)
        }
      }
    } catch (error) {
      console.error("Error in getWarrantySOInvoiceNumber cron:", error);
    }
  };

  const getCallListStatus = async () => {
    try {
      const allOrderData = await db.orderItemSplit.findAll({
        where: {
          crm_ticket_status: ['open', 'pending'],
          middleware_status: "DELIVERED",
          crm_ticket_number: { [Op.ne]: null },
        },
      });

      if (!allOrderData.length) return;
      const statusMap = { 1: 'open', 2: 'closed', 3: 'pending', 5: 'open', 6: 'cancelled' };

      for (const orderData of allOrderData) {
        try {
          const response = await getCallList(orderData.crm_ticket_number);

          if (response.success && response?.data?.callStatusCode) {
            const crm_ticket_status = statusMap[response.data.callStatusCode] || orderData.crm_ticket_status;
            const call_registration_date = response?.data?.callRegistrationDateTime ? moment(response.data.callRegistrationDateTime, "DD-MMM-YYYY HH:mm:ss").format("YYYY-MM-DD HH:mm:ss") : null;
            const call_closed_date = response?.data?.callClosedDateTime ? moment(response.data.callClosedDateTime, "DD-MMM-YYYY HH:mm:ss").format("YYYY-MM-DD HH:mm:ss") : null;
            await orderData.update({ crm_ticket_status, call_registration_date, call_closed_date });
          }
          await sleep(3000); // 3 second break
        } catch (err) {
          console.error(`Error updating order ID ${orderData.id}:`, err);
        }
      }
    } catch (error) {
      console.error("Error in getCallListStatus function:", error);
    }
  };


  const getCallListStatusforServiceLog = async () => {
    try {
      const allServiceLogs = await db.serviceRequestLog.findAll({
        where: {
          call_status: { [Op.notIn]: ['closed', 'cancelled'] },
          crm_ticket_number: { [Op.ne]: null },
        },
      });

      if (!allServiceLogs.length) return;
      const statusMap = { 1: 'open', 2: 'closed', 3: 'pending', 5: 'open', 6: 'cancelled' };

      for (const serviceLog of allServiceLogs) {
        try {
          const response = await allServiceLogs(serviceLog.crm_ticket_number);

          if (response.success && response?.data?.callStatusCode) {
            const call_status = statusMap[response.data.callStatusCode] || orderData.crm_ticket_status;
            const call_registration_date = response?.data?.callRegistrationDateTime ? moment(response.data.callRegistrationDateTime, "DD-MMM-YYYY HH:mm:ss").format("YYYY-MM-DD HH:mm:ss") : null;
            const call_closed_date = response?.data?.callClosedDateTime ? moment(response.data.callClosedDateTime, "DD-MMM-YYYY HH:mm:ss").format("YYYY-MM-DD HH:mm:ss") : null;
            await serviceLog.update({ call_status, call_registration_date, call_closed_date });
          }
          await sleep(3000); // 3 second break
        } catch (err) {
          console.error(`Error updating order ID ${orderData.id}:`, err);
        }
      }
    } catch (error) {
      console.error("Error in getCallListStatus function:", error);
    }
  };
};