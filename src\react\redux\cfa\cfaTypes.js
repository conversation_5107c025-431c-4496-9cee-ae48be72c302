export const cfaTypes = {
    GET_CFA_LIST_REQUEST: "GET_CFA_LIST_REQUEST",
    GET_CFA_LIST_SUCCESS: "GET_CFA_LIST_SUCCESS",

    GET_MANAGE_CFA_LIST_REQUEST: "GET_MANAGE_CFA_LIST_REQUEST",
    GET_MANAGE_CFA_LIST_SUCCESS: "GET_MANAGE_CFA_LIST_SUCCESS",

    SEND_CFA_FILE_REQUEST: "SEND_CFA_FILE_REQUEST",
    SEND_CFA_FILE_SUCCESS: "SEND_CFA_FILE_SUCCESS",
    SEND_CFA_FILE_ERROR: "SEND_CFA_FILE_ERROR",

    SEND_MANAGE_CFA_FILE_REQUEST: "SEND_MANAGE_CFA_FILE_REQUEST",
    SEND_MANAGE_CFA_FILE_SUCCESS: "SEND_MANAGE_CFA_FILE_SUCCESS",
    SEND_MANAGE_CFA_FILE_ERROR: "SEND_MANAGE_CFA_FILE_ERROR",

    GET_MANNUAL_CFA_LIST_SUCCESS: "GET_MANNUAL_CFA_LIST_SUCCESS",
    GET_MANNUAL_CFA_LIST_REQUEST: "GET_MANNUAL_CFA_LIST_REQUEST",

    PINCODE_EXPORT_REQUEST: "PINCODE_EXPORT_REQUEST",
    PINCODE_EXPORT_SUCCESS: "PINCODE_EXPORT_SUCCESS",
    PINCODE_EXPORT_ERROR: "PINCODE_EXPORT_ERROR",

    CFASTOCK_EXPORT_REQUEST: "CFASTOCK_EXPORT_REQUEST",
    CFASTOCK_EXPORT_SUCCESS: "CFASTOCK_EXPORT_SUCCESS",
    CFASTOCK_EXPORT_ERROR: "CFASTOCK_EXPORT_ERROR",

    EXPORT_CFA_REQUEST: "EXPORT_CFA_REQUEST",
    EXPORT_CFA_SUCCESS: "EXPORT_CFA_SUCCESS",
    EXPORT_CFA_ERROR: "EXPORT_CFA_ERROR"
}

export const toastObject = {
    message: "",
    isError: false,
    isActive: false
}