const Sequelize = require("sequelize");
module.exports = (sequelize) => {
  return sequelize.define("order", {
    id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      unique: true
    },
    store_id: {
      type: Sequelize.INTEGER,
      allowNull:false,
      references: {
        model: 'stores',
        key: 'id'
      }
    },
    shopify_order_id: {
      type: Sequelize.STRING
    },
    shopify_customer_id: {
      allowNull:true,
      type: Sequelize.STRING
    },
    order_number: {
      allowNull:false,
      type: Sequelize.STRING
    },
    order_name: {
      allowNull:false,
      type: Sequelize.STRING
    },
    order_created_at: {
      allowNull:true,
      type: Sequelize.STRING
    },
    order_updated_at: {
      allowNull:true,
      type: Sequelize.STRING
    },
    order_cancelled_at: {
      allowNull:true,
      type: Sequelize.STRING
    },
    total_price: {
      type: Sequelize.DOUBLE(11, 2),
      defaultValue: '0.00'
    },
    subtotal_price: {
      allowNull:false,
      type: Sequelize.DOUBLE(11, 2),
      defaultValue: '0.00'
    },
    total_weight: {
      allowNull:false,
      type: Sequelize.DOUBLE(11, 2),
      defaultValue: '0.00'
    },
    total_discount_amount: {
      allowNull:false,
      type: Sequelize.DOUBLE(11, 2),
      defaultValue: '0.00'
    },
    gateway: {
      allowNull:false,
      type: Sequelize.STRING
    },
    fulfillment_status: {
      allowNull:false,
      type: Sequelize.DataTypes.ENUM('unfulfilled', 'fulfilled', 'partial','pending'),
      defaultValue: 'unfulfilled'
    },
    master_id:{
      type:Sequelize.STRING,
      defaultValue: null
    }
  },
    {
      indexes: [
        {
          unique: true,
          fields: ['store_id', 'shopify_order_id']
        }
      ]
    });
};