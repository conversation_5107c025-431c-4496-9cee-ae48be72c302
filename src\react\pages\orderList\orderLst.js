import {
  Card,
  Layout,
  Page,
  Filters,
  ChoiceList,
  Badge,
  IndexTable,
  Stack,
  Pagination,
  Button,
  Modal,
  TextContainer,
  Popover,
  Tooltip,
  Heading,
  Select,
  Link,
  TextField,
  FormLayout,
  DropZone,
  Banner,
  Caption,
} from "@shopify/polaris";
import React, { Component } from "react";
import { connect } from "react-redux";
import {
  getOrdersListRequest,
  syncOrderRequest,
  cancelOrdersRequest,
  pushOrdersRequest,
  submitOrderCFARequest,
  createExportOrdersRequest,
  createRefundOrdersRequest,
  saveOrderDetailsRequest,
  handleSubmitServiceRequest,
  orderReturnRequest,
  saveRefundFileRequest,
} from "../../redux/orders/ordersActions";

import {
  getMannualCFAListRequest
} from "../../redux/cfa/cfaActions";
import {
  setPagination,
  isEmpty,
  disambiguateLabel,
  StatusArray,
  priceConversion,
  dateConversion,
} from "../../helpers/appHelpers";
import { SortMinor } from "@shopify/polaris-icons";
import DateRangePicker from "../../childComponent/DateRangePicker";
import moment from "moment";
import { debounce } from "lodash";
import ServiceRequestModal from "./serviceRequestModal";

let pagination = setPagination();
var sDate = moment().subtract(30, "days").toDate();
var eDate = moment().toDate();

class Orders extends Component {
  constructor(props) {
    super(props);
    this.state = {
      queryValue: "",
      middlewareStatus:[],
      fulfillmentStatus: [],
      refundStatus: [],
      paymentStatus: [],
      sapStatus: [],
      delhiveryStatus: [],
      orderStatus: [],
      selectedResources: [],
      allResourcesSelected: false,
      orderData: {},
      popactive: false,
      sortValue: [],
      importModalActive: false,
      files: [],
      rejectedFiles: [],
      isSaveDisable: true,
      errorMessage: null,
      importButtonLoading: false,
      cfaOption: [],
      pageLoading: false,
      syncModalActive: false,
      orderDetailModalActive: false,
      syncButtonLoading: false,
      ConfirmationLoadingButton: false,
      startDate: moment(sDate).format("YYYY-MM-DD"),
      endDate: moment(eDate).format("YYYY-MM-DD"),
      selectedDate: {
        start: moment().subtract(30, "days").toDate(),
        end: moment().toDate(),
      },
      selectedCFa: "",
      selectedDateText: {
        start: moment().subtract(30, "days").format("YYYY-MM-DD"),
        end: moment().format("YYYY-MM-DD"),
      },
      appOrderStatus: "",
      appOrderName: "",
      createdOrderData: {},
      cancelOrderData: {},
      cfaToCustomerData: {},
      customerToCfaData: {},
      shipmentStatus: [],
      actionsConfirmationActive: false,
      actionsConfirmationHeader: null,
      editModalActive: false,
      editModalIsLoading: false,
      editedOrderStatus: '',
      orderDetailsTexts: {
        waybillNumber: '',
        sapOrderNumber: '',
        sapInvoiceNumber: '',
        sapDeliveryNumber: '',
        cfaNumber: ''
      },
      orderDetailsTextErrors: {
        waybillNumber: false,
        sapOrderNumber: false,
        sapInvoiceNumber: false,
        sapDeliveryNumber: false,
        cfaNumber: false
      },
      serviceModalActive: false,
      orderItemData:{}
    };

    this.props.history.replace("/app/orders");
  }
  componentDidMount() {
    document.title = "Orders";
    this.getAllOrders();
  }

  handleChanagePage = (action) => {
    this.setState({ allResourcesSelected: false, selectedResources: [] });

    action === "Next" ? pagination.page++ : pagination.page--;
    this.getAllOrders();
  };

  debounceEvent(...args) {
    this.debounceEvent = debounce(...args);
    return (e) => {
      e.perstist();
      return this.debounceEvent(e);
    };
  }

  getAllOrders = () => {
    this.setState({ pageLoading: true });
    let { perpage, page } = pagination;
    const {
      fulfillmentStatus,
      delhiveryStatus,
      orderStatus,
      paymentStatus,
      sapStatus,
      queryValue,
      sortValue,
      startDate,
      endDate,
      shipmentStatus,
      refundStatus,
      middlewareStatus
    } = this.state;
    const request = {
      page: page,
      perpage: perpage,
      search: queryValue,
      paymentStatus: paymentStatus,
      orderStatus: orderStatus,
      fulfillmentStatus: fulfillmentStatus,
      delhiveryStatus: delhiveryStatus,
      sapStatus: sapStatus,
      sort: sortValue,
      startDate: startDate,
      endDate: endDate,
      shipmentStatus: shipmentStatus,
      refundStatus: refundStatus,
      middlewareStatus:middlewareStatus
    };
    let { getOrdersList } = this.props;
    getOrdersList({
      request,
      callback: () => {
        if (!this.props.error) {
          const { orderListAllData } = this.props;
          const { page, limit, total, totalPage } = orderListAllData.data;
          pagination = setPagination(page, limit, totalPage, total);
        }
        this.setState({ pageLoading: false });
      },
    });
  };

  handleStatusChange = (value) => {
    this.setState({ orderStatus: value, selectedResources: [] }, () => {
      pagination.page = 1;
      this.getAllOrders();
    });
  };
  handleSapStatusChange = (value) => {
    this.setState({ sapStatus: value, selectedResources: [] }, () => {
      pagination.page = 1;
      this.getAllOrders();
    });
  };
  handleDelhiveryStatusChange = (value) => {
    this.setState({ delhiveryStatus: value, selectedResources: [] }, () => {
      pagination.page = 1;
      this.getAllOrders();
    });
  };

  handleFulfillmentStatusChange = (value) => {
    this.setState(
      {
        fulfillmentStatus: value,
        selectedResources: [],
      },
      () => {
        pagination.page = 1;
        this.getAllOrders();
      }
    );
  };

  handlePaymentStatusChange = (value) => {
    this.setState(
      {
        paymentStatus: value,
        selectedResources: [],
      },
      () => {
        pagination.page = 1;
        this.getAllOrders();
      }
    );
  };

  handleFiltersQueryChange = (value) => {
    this.setState({ queryValue: value, selectedResources: [] });
    this.debounceEvent(() => {
      pagination.page = 1;
      this.getAllOrders();
    }, 300);
    // this.setState({queryValue:value},
    //   ()=>{
    //       pagination.page = 1;
    //       this.getAllOrders();
    //     });
  };

  handleQueryValueRemove = () => {
    this.setState(
      {
        queryValue: "",
      },
      () => {
        this.getAllOrders();
      }
    );
  };

  handleStatusRemove = () => {
    this.setState(
      {
        orderStatus: "",
      },
      () => {
        this.getAllOrders();
      }
    );
  };

  handleFulfillmentStatusRemove = () => {
    this.setState(
      {
        fulfillmentStatus: "",
      },
      () => {
        this.getAllOrders();
      }
    );
  };

  handlePaymentStatusRemove = () => {
    this.setState(
      {
        paymentStatus: "",
      },
      () => {
        this.getAllOrders();
      }
    );
  };
  handleShipmentStatusRemove = () => {
    this.setState(
      {
        shipmentStatus: "",
      },
      () => {
        this.getAllOrders();
      }
    );
  }
  handleMiddlewareStatusRemove = () => {
    this.setState(
      {
        middlewareStatus: "",
      },
      () => {
        this.getAllOrders();
      }
    );
  }
  handleSapStatusRemove = () => {
    this.setState(
      {
        sapStatus: "",
      },
      () => {
        this.getAllOrders();
      }
    );
  };
  handlesDelhiveryStatusRemove = () => {
    this.setState(
      {
        delhiveryStatus: "",
      },
      () => {
        this.getAllOrders();
      }
    );
  };

  handleFiltersClearAll = () => {
    this.handleStatusRemove();
    this.handleSapStatusRemove();
    this.handlesDelhiveryStatusRemove();
    this.handlePaymentStatusRemove();
    this.handleShipmentStatusRemove();
    this.handleQueryValueRemove();
  };

  handlesyncOrderModal = () => {
    this.setState({ syncModalActive: !this.state.syncModalActive });
  };
  handleServiceModal = (value,item) => {
    console.log("aman",value)
    this.setState({ serviceModalActive: value,orderItemData:item,selectedResources: [] })
}
handleSubmitService = async (obj,orderName) => {
  console.log("obj---",obj)
  let { handleSubmitRequest} = this.props;
  await new Promise((resolve, reject) => {
    handleSubmitRequest({
          serviceRequest: {orderName:orderName,serviceData:obj},
          callback: () => {
              if (!this.props.error) {
                  // this.setState({ serviceModalActive: false })
                  this.getAllOrders()
              }
              this.setState({ actionLoading: false })
              resolve()
          },
      })

  })
  return;
}
  handlesyncOrder = () => {
    this.setState({ syncButtonLoading: true });
    let { syncOrder } = this.props;
    syncOrder({
      callback: () => {
        this.setState({
          pageLoading: false,
          syncModalActive: false,
          syncButtonLoading: false,
        });
      },
    });
  };

  pushOrder = () => {
    let {
      selectedResources,
      queryValue,
      paymentStatus,
      sortValue,
      fulfillmentStatus,
      orderStatus,
      allResourcesSelected,
      startDate,
      endDate,
      sapStatus,
      delhiveryStatus,
      shipmentStatus
    } = this.state;
    let { pushOrders } = this.props;
    if (allResourcesSelected === true) {
      selectedResources = [];
    }
    const objdata = {
      order_ids: selectedResources,
      isAllResource: allResourcesSelected,
      filters: {
        search: queryValue,
        payment_status: paymentStatus,
        order_status: orderStatus,
        fulfillment_status: fulfillmentStatus,
        delhivery_status: delhiveryStatus,
        sap_status: sapStatus,
        sort: sortValue,
        startDate: startDate,
        endDate: endDate,
        shipment_status: shipmentStatus
      },
    };
    pushOrders({
      objdata,
      callback: () => {
        if (!this.props.error) {
          this.setState({ selectedResources: [], allResourcesSelected: false, actionsConfirmationActive: false, ConfirmationLoadingButton: false });
          // this.props.history.push("/app/orders");
          this.getAllOrders();
        } else {
          this.setState({ ConfirmationLoadingButton: false })
        }
      },
    });
  };

  handleCancelOrder = () => {
    let {
      selectedResources,
      queryValue,
      paymentStatus,
      sortValue,
      fulfillmentStatus,
      orderStatus,
      allResourcesSelected,
      startDate,
      endDate,
      sapStatus,
      delhiveryStatus,
      shipmentStatus
    } = this.state;
    let { cancelOrders } = this.props;
    if (allResourcesSelected === true) {
      selectedResources = [];
    }
    const objdata = {
      order_ids: selectedResources,
      isAllResource: allResourcesSelected,
      filters: {
        search: queryValue,
        payment_status: paymentStatus,
        order_status: orderStatus,
        fulfillment_status: fulfillmentStatus,
        delhivery_status: delhiveryStatus,
        sap_status: sapStatus,
        sort: sortValue,
        startDate: startDate,
        endDate: endDate,
        shipment_status: shipmentStatus
      },
    };
    cancelOrders({
      objdata,
      callback: () => {
        if (!this.props.error) {
          this.setState({ ConfirmationLoadingButton: false, selectedResources: [], allResourcesSelected: false, actionsConfirmationActive: false });
          // this.props.history.push("/app/orders");
          this.getAllOrders();
        } else {
          this.setState({ ConfirmationLoadingButton: false })
        }
      },
    });
  };

  handleReturnOrderRequest = () => {
    let {
      selectedResources,
      queryValue,
      paymentStatus,
      sortValue,
      fulfillmentStatus,
      orderStatus,
      allResourcesSelected,
      startDate,
      endDate,
      sapStatus,
      delhiveryStatus,
      shipmentStatus
    } = this.state;
    let { returnOrders } = this.props;
    if (allResourcesSelected === true) {
      selectedResources = [];
    }
    const objdata = {
      order_ids: selectedResources,
      isAllResource: allResourcesSelected,
      filters: {
        search: queryValue,
        payment_status: paymentStatus,
        order_status: orderStatus,
        fulfillment_status: fulfillmentStatus,
        delhivery_status: delhiveryStatus,
        sap_status: sapStatus,
        sort: sortValue,
        startDate: startDate,
        endDate: endDate,
        shipment_status: shipmentStatus
      },
    };
    returnOrders({
      objdata,
      callback: () => {
        if (!this.props.error) {
          this.setState({ selectedResources: [], allResourcesSelected: false, actionsConfirmationActive: false, ConfirmationLoadingButton: false });
          // this.props.history.push("/app/orders");
          this.getAllOrders();
        } else {
          this.setState({ ConfirmationLoadingButton: false })
        }
      },
    });
  };

  handleExportOrder = (type=null) => {
    let {
      selectedResources,
      queryValue,
      paymentStatus,
      fulfillmentStatus,
      sapStatus,
      orderStatus,
      allResourcesSelected,
      startDate,
      endDate,
      delhiveryStatus,
      shipmentStatus,
      refundStatus,
      middlewareStatus
    } = this.state;
    let { exportOrders } = this.props;
    if (allResourcesSelected === true) {
      selectedResources = [];
    }
    const objdata = {
      exportType:type,
      order_ids: selectedResources,
      isAllResource: allResourcesSelected,
      filters: {
        search: queryValue,
        financial_status: paymentStatus,
        fulfilment_status: fulfillmentStatus,
        sap_status: sapStatus,
        delhivery_status: delhiveryStatus,
        order_status: orderStatus,
        start_date: startDate,
        end_date: endDate,
        shipment_status: shipmentStatus,
        refund_status: refundStatus,
        middleware_status:middlewareStatus
      },
    };
    exportOrders({
      objdata,
      callback: () => {
        if (!this.props.error) {
          var response = this.props.orderResponseData;
          // const downloadUrl = window.URL.createObjectURL(
          //   new Blob([response.data])
          // );
          // const link = document.createElement("a");
          // link.href = downloadUrl;
          // var name = "orders";
          // link.setAttribute("download", name + ".xlsx");
          // document.body.appendChild(link);
          // link.click();
          // link.remove();
          this.setState({ ConfirmationLoadingButton: false, selectedResources: [], allResourcesSelected: false, actionsConfirmationActive: false });
          this.getAllOrders();
        } else {
          this.setState({ ConfirmationLoadingButton: false })
        }
      },
    });
  };
  handleRefundOrder = () => {
    let {
      selectedResources,
      queryValue,
      paymentStatus,
      fulfillmentStatus,
      sapStatus,
      orderStatus,
      allResourcesSelected,
      startDate,
      endDate,
      delhiveryStatus,
      shipmentStatus,
      refundStatus,
      middlewareStatus
    } = this.state;
    let { refundOrders } = this.props;
    if (allResourcesSelected === true) {
      selectedResources = [];
    }
    const objdata = {
      order_ids: selectedResources,
      isAllResource: allResourcesSelected,
      filters: {
        search: queryValue,
        financial_status: paymentStatus,
        fulfilment_status: fulfillmentStatus,
        sap_status: sapStatus,
        delhivery_status: delhiveryStatus,
        order_status: orderStatus,
        start_date: startDate,
        end_date: endDate,
        shipment_status: shipmentStatus,
        refund_status: refundStatus,
        middleware_status:middlewareStatus
      },
    };
    refundOrders({
      objdata,
      callback: () => {
        if (!this.props.error) {
          this.setState({ ConfirmationLoadingButton: false, selectedResources: [], allResourcesSelected: false, actionsConfirmationActive: false });
          this.getAllOrders();
        } else {
          this.setState({ ConfirmationLoadingButton: false })
        }
      },
    });
  }

  handleSaveOrderDetails = () => {
    this.setState({ editModalIsLoading: true });
    let objdata = {
      orderStatus: this.state.editedOrderStatus,
    }
    if (this.state.editedOrderStatus == 'cancel') {
      objdata.data = { ...this.state.cancelOrderData }
    } else if (this.state.editedOrderStatus == 'replaced') {
      objdata.data = { ...this.state.customerToCfaData }
    } else {
      objdata.data = { ...this.state.createdOrderData }
    }
    objdata.data.orderName = this.state.appOrderName
    let { saveOrderDetails } = this.props;
    saveOrderDetails({
      objdata,
      callback: () => {
        if (!this.props.error) {
          this.setState({
            editModalIsLoading: false,
            editModalActive: !this.state.editModalActive,
            orderDetailModalActive: false,
            selectedResources: [],
            createdOrderData: {},
            cancelOrderData: {},
            cfaToCustomerData: {},
            customerToCfaData: {},
          });
          this.getAllOrders();
        } else {
          this.setState({ editModalIsLoading: false, orderDetailModalActive: false, selectedResources: [], createdOrderData: {},
            cancelOrderData: {},
            cfaToCustomerData: {},
            customerToCfaData: {}, })
        }
      },
    });
  }
  handleShipmentStatusChange = (value) => {
    this.setState(
      {
        shipmentStatus: value,
        selectedResources: [],
      },
      () => {
        pagination.page = 1;
        this.getAllOrders();
      }
    );
  }
  handleOrderDetailsText = (value, key) => {
    if (this.state.editedOrderStatus == 'cancel') {
      let tempObj = { ...this.state.cancelOrderData }
      tempObj[key] = value
      this.setState({
        cancelOrderData: { ...tempObj }
      });
    } else if (this.state.editedOrderStatus == 'replaced') {
      let tempObj = { ...this.state.customerToCfaData }
      tempObj[key] = value
      this.setState({
        customerToCfaData: { ...tempObj }
      });
    } else {
      let tempObj = { ...this.state.createdOrderData }
      tempObj[key] = value
      this.setState({
        createdOrderData: { ...tempObj }
      });
    }
  }

  handleOrderDetailsEdit = (order_status) => {
    this.setState({
      editModalActive: !this.state.editModalActive,
      modalHeader: `${this.state.modalHeader.includes('Edit') ? this.state.modalHeader : `Edit ${this.state.modalHeader}`}`,
      editedOrderStatus: order_status
    });
  }

  handleRefundStatusChange = (value) => {
    this.setState(
      {
        refundStatus: value,
        selectedResources: [],
      },
      () => {
        pagination.page = 1;
        this.getAllOrders();
      }
    );
  }
  handleMiddlewareStatusChange = (value) => {
    this.setState(
      {
        middlewareStatus: value,
        selectedResources: [],
      },
      () => {
        pagination.page = 1;
        this.getAllOrders();
      }
    );
  }
  togglePopover = () => {
    this.setState({
      popactive: !this.state.popactive,
    });
  };

  handleChange = (value) => {
    this.setState(
      {
        sortValue: value,
      },
      () => {
        pagination.page = 1;
        this.getAllOrders();
      }
    );
  };
  handleOrderDetailModal = (appOrderName, appOrderStatus, item) => {
    if (appOrderStatus == "Out Of Stock") {
      let { getCFAList } = this.props;
      getCFAList({
        callback: () => {
          if (!this.props.error) {
            const { cfaListData } = this.props;
            let cfaData = cfaListData.data.data
            let cfaSelectList = []
            for (const data of cfaData) {
              cfaSelectList.push({ label: data.plantCode + " (" + data.plantName + ")", value: data.plantCode },)
            }
            if (cfaSelectList.length > 0) {
              this.setState({ selectedCFa: cfaSelectList[0].value })
            }
            this.setState({ cfaOption: cfaSelectList })

          }
          this.setState({ cfaButttonLoading: false });
        },
      });
    }

    let createdOrderObj = {}
    let cancelOrderObj = {}
    let cfaToCustomer = {}
    let customerToCfa = {}

    if (item.is_cancel == "1") {
      let sapArr = item.sapLog.filter(el => {
        return el.is_cancel == "1" && el.return_order_name == item.return_order_name
      })
      let delhiveryArr = item.delhiveryLog.filter(el => {
        return el.is_cancel == "1" && el.return_order_name == item.return_order_name
      })

      for (let sapValue of sapArr) {
        cancelOrderObj.sapOrderNumber = sapValue.sap_order_number
        cancelOrderObj.sapInvoiceNumber = sapValue.sap_billing_number
        cancelOrderObj.sapDeliveryNumber = sapValue.sap_delivery_number
        cancelOrderObj.cfaNumber = sapValue.plant_code
      }
      for (let delhiveryValue of delhiveryArr) {
        cancelOrderObj.orderName = delhiveryValue.order_name
        cancelOrderObj.waybillNumber = delhiveryValue.waybill_number
      }
      if(delhiveryArr.length == 0){
        cancelOrderObj.orderName = item.order_name
        cancelOrderObj.waybillNumber = item.waybill_number
      }
      this.setState({
        cancelOrderData: cancelOrderObj
      })
    } else if (item.order_status == "Replacement" || item.order_status == "Returned") {

      let sapArr = item.sapLog.filter(el => {
        return el.is_return == "1" && el.return_order_name == item.return_order_name
      })
      let delhiveryArr = item.delhiveryLog.filter(el => {
        return el.is_return == "1" && el.return_order_name == item.return_order_name
      })
      if (delhiveryArr.length > 0 && sapArr.length > 1) {
        for (let value of delhiveryArr) {
          // if (value.return_order_name) {
          //   cfaToCustomer.waybillNumber = value.waybill_number
          // } else {
          customerToCfa.waybillNumber = value.waybill_number
          // }
        }

        for (let value of sapArr) {
          // if (value.return_order_name) {
          //   cfaToCustomer.orderName = value.order_name
          //   cfaToCustomer.sapOrderNumber = value.sap_order_number
          //   cfaToCustomer.sapInvoiceNumber = value.sap_billing_number
          //   cfaToCustomer.sapDeliveryNumber = value.sap_delivery_number
          // } else {
          customerToCfa.orderName = value.return_order_name
          customerToCfa.sapOrderNumber = value.sap_order_number
          customerToCfa.sapInvoiceNumber = value.sap_billing_number
          customerToCfa.sapDeliveryNumber = value.sap_delivery_number
          customerToCfa.cfaNumber = value.plant_code
          // }
        }

      } else {
        for (let delhiveryValue of delhiveryArr) {
          customerToCfa.waybillNumber = delhiveryValue.waybill_number
          customerToCfa.orderName = delhiveryValue.return_order_name
        }

        for (let sapValue of sapArr) {
          customerToCfa.sapOrderNumber = sapValue.sap_order_number
          customerToCfa.sapInvoiceNumber = sapValue.sap_billing_number
          customerToCfa.sapDeliveryNumber = sapValue.sap_delivery_number
          customerToCfa.cfaNumber = sapValue.plant_code
        }
      }
      this.setState({
        customerToCfaData: customerToCfa,
        // cfaToCustomerData: cfaToCustomer
      })
    }
    let sapArr = item.sapLog.filter(el => {
      return el.is_cancel == "0" && el.is_return == "0"
    })
    let delhiveryArr = item.delhiveryLog.filter(el => {
      return el.is_cancel == "0" && el.is_return == "0"
    })

    createdOrderObj.orderName = sapArr.length > 0 ? sapArr[0].order_name : ''
    createdOrderObj.sapOrderNumber = sapArr.length > 0 ? sapArr[0].sap_order_number : ''
    createdOrderObj.sapInvoiceNumber = sapArr.length > 0 ? sapArr[0].sap_billing_number : ''
    createdOrderObj.sapDeliveryNumber = sapArr.length > 0 ? sapArr[0].sap_delivery_number : ''
    createdOrderObj.cfaNumber = sapArr.length > 0 ? sapArr[0].plant_code : ''
    createdOrderObj.waybillNumber = delhiveryArr.length > 0 ? delhiveryArr[0].waybill_number : ''
    this.setState({
      orderDetailModalActive: true,
      appOrderStatus: appOrderStatus,
      appOrderName: appOrderName,
      createdOrderData: createdOrderObj,
      selectedResources: [],
      allResourcesSelected: false,
      modalHeader: 'Order Detail /' + appOrderName
    });
  };
  handleOrderDetailModalClose = () => {
    this.setState({
      orderDetailModalActive: false,
      appOrderStatus: "",
      appOrderName: "",
      selectedResources: [],
      allResourcesSelected: false,
      createdOrderData: {},
      cancelOrderData: {},
      cfaToCustomerData: {},
      customerToCfaData: {},
    });
  };
  handleSelectCFA = (value) => {
    this.setState({ selectedCFa: value })
  };

  handleSubmitCFA = () => {
    if (this.state.selectedCFa != '') {
      this.setState({ cfaButttonLoading: true })
      const { submitOrderCFA } = this.props;
      let orderObj = {}
      orderObj.order_ids = [this.state.appOrderName]
      orderObj.plantCode = this.state.selectedCFa
      submitOrderCFA({
        orderObj,
        callback: () => {
          if (!this.props.error) {
            this.setState({
              cfaButttonLoading: false,
              orderDetailModalActive: false,
              appOrderStatus: '',
              appOrderName: '',
              selectedResources: [],
              allResourcesSelected: false,
            });
            this.getAllOrders();
          } else {
            this.setState({ cfaButttonLoading: false })
          }
        }
      });
    } else {
      this.setState({ cfaButttonLoading: false })
    }
  }
  saveCfaModal = () => {
    this.setState({ importButtonLoading: true });
    var files = this.state.files;
    if (files[0].type == 'xls' || files[0].type == 'xlsx' || files[0].type == 'application/vnd.ms-excel' || files[0].type == 'application/vnd.msexcel' || files[0].type == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
      //   if(files[0].type == 'text/csv' || files[0].type == 'CSV' || files[0].type == 'csv' || files[0].type=='application/vnd.ms-excel' || files[0].type=='application/vnd.msexcel'){
      let formData = new FormData();
      if (!isEmpty(files)) {
        formData.append('file', files[0]);
      }
      let { saveRefundFile } = this.props;
      saveRefundFile({
        formData,
        callback: () => {
          if (!this.props.error) {
            this.setState({ importButtonLoading: false, file: [], importModalActive: false ,},
              () => {
                this.getAllOrders();
              });
          } else {
            this.setState({ importButtonLoading: false });
          }
        },
      });
    } else {
      this.setState({ errorMessage: "Only xlsx file is allowed", importButtonLoading: false })
    }
  }
  onSelection = (type, isChecked, id) => {
    let { selectedResources, allResourcesSelected } = this.state;
    const { loading, orderListAllData } = this.props;
    if (!loading) {
      const dataObj = orderListAllData.data.data;
      switch (type) {
        case "page":
          allResourcesSelected = false;
          if (isChecked) {
            dataObj.forEach(function (order_data) {
              let id = order_data.order_name;
              if (selectedResources.indexOf(id) === -1) {
                selectedResources.push(id);
              }
            });
          } else {
            selectedResources = [];
          }
          break;
        case "single":
          allResourcesSelected = false;
          if (isChecked) {
            selectedResources.push(id);
          } else {
            selectedResources = selectedResources.filter(function (value) {
              return value !== id;
            });
          }
          break;
        case "all":
          if (isChecked) {
            allResourcesSelected = true;
            dataObj.forEach(function (order_data) {
              let id = order_data.order_name;
              if (selectedResources.indexOf(id) === -1) {
                selectedResources.push(id);
              }
            });
          }
          break;
        default:
          break;
      }
    }
    this.setState({
      selectedResources: selectedResources,
      allResourcesSelected: allResourcesSelected,
    });
  };
  actionsConfirmationClose = () => {
    this.setState({ actionsConfirmationActive: false, ConfirmationLoadingButton: false })
  }
  actionsConfirmationModal = () => {
    this.setState({ ConfirmationLoadingButton: true })
    if (this.state.actionsConfirmationHeader == "Push Orders") {
      this.pushOrder();
    }
    if (this.state.actionsConfirmationHeader == "Cancel Orders") {
      this.handleCancelOrder();
    }
    if (this.state.actionsConfirmationHeader == "Return Orders") {
      this.handleReturnOrderRequest();
    }
    if (this.state.actionsConfirmationHeader == "Export Orders") {
      this.handleExportOrder();
    }
    if (this.state.actionsConfirmationHeader == "Export Refund Orders") {
      this.handleExportOrder("refund");
    }
    if (this.state.actionsConfirmationHeader == "Refund Orders") {
      this.handleRefundOrder();
    }
  }
  pushActionsModal = () => {
    this.setState({ actionsConfirmationHeader: "Push Orders", actionsConfirmationActive: true });
  }
  cancelActionsModal = () => {
    this.setState({ actionsConfirmationHeader: "Cancel Orders", actionsConfirmationActive: true });
  }
  returnRequestActionsModal = () => {
    this.setState({ actionsConfirmationHeader: "Return Orders", actionsConfirmationActive: true });
  }
  exportActionsModal = () => {
    this.setState({ actionsConfirmationHeader: "Export Orders", actionsConfirmationActive: true });
  }
  refundExportActionsModal = () => {
    this.setState({ actionsConfirmationHeader: "Export Refund Orders", actionsConfirmationActive: true });
  }
  // refundActionsModal = () => {
  //   this.setState({ actionsConfirmationHeader: "Refund Orders", actionsConfirmationActive: true });
  // }

  handleImportModal = () => {
    this.setState({ importModalActive: !this.state.importModalActive, errorMessage: '', files: [], rejectedFiles: [], isSaveDisable: true });
  }
  // handle dropzone
  handleDrop = (_droppedFiles, files, rejectedFiles) => {
    this.setState({
      files: files,
      rejectedFiles: rejectedFiles
    });
    if (rejectedFiles.length > 0) {
      this.setState({ isSaveDisable: true })
    } else {
      this.setState({ isSaveDisable: false })
    }
  };
  orderListMarkup = () => {
    const { selectedResources } = this.state;
    const { loading, orderListAllData } = this.props;
    let allPage = 1;
    const resourceName = {
      singular: "Order",
      plural: "Orders",
    };
    const bulkActions = [
      {
        content: "Push",
        onAction: this.pushActionsModal,
      },
      {
        content: "Cancel",
        onAction: this.cancelActionsModal,
      },
      {
        content: "Return Request",
        onAction: this.returnRequestActionsModal,
      },
      {
        content: "Export",
        onAction: this.exportActionsModal,
      },
      {
        content: "Export Refund",
        onAction: this.refundExportActionsModal,
      }
      // {
      //   content: "Refund",
      //   onAction: this.refundActionsModal,
      // }
    ];
    let rowMarkup = [];
    if (!loading) {
      const dataObj = orderListAllData.data.data;
      const { page, limit, total, totalPage } = orderListAllData.data;
      allPage = totalPage;
      pagination = setPagination(page, limit, totalPage, total);
      rowMarkup = dataObj.map(
        (
          item,
          index
        ) => (
          <IndexTable.Row
            id={item.order_name}
            key={item.order_name}
            selected={selectedResources.includes(item.order_name)}
            position={index}
          >
            <IndexTable.Cell>{item.shopify_order_name}</IndexTable.Cell>
            <IndexTable.Cell>
              <Button
                plain
                onClick={() => {
                  this.handleOrderDetailModal(item.order_name, item.order_status, item)
                }
                }
              >
                {item.order_name}
              </Button>
            </IndexTable.Cell>
            <IndexTable.Cell>
              <p>{item.sap_order_number}</p>
              <p>{item.sap_billing_number ? "Bill# :" + item.sap_billing_number : ""}</p>
            </IndexTable.Cell>
            <IndexTable.Cell>
              <Badge status="success">{item.middleware_status}</Badge>
            </IndexTable.Cell>
            <IndexTable.Cell>
              {item.orderCustomers ? (
                (item.orderCustomers.first_name == null || item.orderCustomers.first_name == "null") && (item.orderCustomers.last_name == null || item.orderCustomers.last_name == "null") ?
                  <div>
                    {item.orderCustomers.customer_email}
                  </div>
                  :
                  <div className="capitalize">
                    {(item.orderCustomers.first_name != null && item.orderCustomers.first_name != "null" && item.orderCustomers.first_name != "" ? item.orderCustomers.first_name + " " : "") + (item.orderCustomers.last_name != null && item.orderCustomers.last_name != "null" && item.orderCustomers.last_name != "" ? item.orderCustomers.last_name : "")}
                  </div>
              ) : (
                ""
              )}
            </IndexTable.Cell>
            <IndexTable.Cell>
              <div>{item.waybill_number}</div>
            </IndexTable.Cell>
            <IndexTable.Cell>
              <div>
                <p>{item.product_title}</p>
                <p>SKU: {item.sku}</p>
                <p>Qty: {item.quantity}</p>
                <p>Price: 
                {item.currency == "USD" ? "$" : "₹"}
                {item.discount !== null && item.discount !== "" && item.discount !== "null"
                  ? priceConversion(item.order_amount - item.discount)
                  : priceConversion(item.order_amount)}
              </p>
              </div>
            </IndexTable.Cell>
            <IndexTable.Cell>
              {dateConversion(item.order_created_at)}
            </IndexTable.Cell>
            <IndexTable.Cell>
              {item.middleware_status=="DELIVERED"?<Button
                plain
                onClick={() => {
                  this.handleServiceModal(true,item)
                }
                }
              >
                Create Service Request
              </Button>:"-"}
             
            </IndexTable.Cell>
            {/* <IndexTable.Cell>
              {item.order_status !== null &&
                item.order_status !== "" &&
                item.order_status !== "null" ? (
                <Badge status={StatusArray[item.order_status]["color"]}>
                  {StatusArray[item.order_status].lable}
                </Badge>
              ) : (
                item.order_status
              )}
            </IndexTable.Cell>
            <IndexTable.Cell>
              {item.sap_status === "Failed" && item.sapLog.length > 0 ? (
                <Tooltip content={item.sapLog[0].response_message}>
                  <Badge
                    status={StatusArray[item.sap_status]["color"]}
                    progress={StatusArray[item.sap_status]["progress"]}
                  >
                    {StatusArray[item.sap_status].lable}
                  </Badge>
                </Tooltip>
              ) : (
                <Badge
                  status={StatusArray[item.sap_status]["color"]}
                  progress={StatusArray[item.sap_status]["progress"]}
                >
                  {StatusArray[item.sap_status].lable}
                </Badge>
              )}
            </IndexTable.Cell>
            <IndexTable.Cell>
              {item.delhivery_status === "Failed" && item.delhiveryLog.length > 0 ? (
                <Tooltip content={item.delhiveryLog[0].response_message}>
                  <Badge
                    status={StatusArray[item.delhivery_status]["color"]}
                    progress={StatusArray[item.delhivery_status]["progress"]}
                  >
                    {StatusArray[item.delhivery_status].lable}
                  </Badge>
                </Tooltip>
              ) : (
                <Badge
                  status={StatusArray[item.delhivery_status]["color"]}
                  progress={StatusArray[item.delhivery_status]["progress"]}
                >
                  {StatusArray[item.delhivery_status].lable}
                </Badge>
              )}
            </IndexTable.Cell>
            <IndexTable.Cell>
              <div>{item.financial_status}</div>
            </IndexTable.Cell>
            <IndexTable.Cell>
              <div>{item.shipment_status}</div>
            </IndexTable.Cell>
            <IndexTable.Cell>
              <div>{item.orderRefund.length > 0 ? item.orderRefund[0].refund_status == 'processed' ? 'success' : item.orderRefund[0].refund_status : ''}</div>
            </IndexTable.Cell> */}
          </IndexTable.Row>
        )
      );
    }
    return (
      <IndexTable
        resourceName={resourceName}
        itemCount={rowMarkup.length}
        selectedItemsCount={
          this.state.allResourcesSelected
            ? "All"
            : this.state.selectedResources.length
        }
        hasMoreItems={allPage > 1 ? true : false}
        onSelectionChange={this.onSelection}
        loading={this.state.pageLoading}
        bulkActions={bulkActions}
        footerContent={pagination.showing}
        headings={[
          { title: "Shopify Order#" },
          { title: "Order#" },
          { title: "SAP#" },
          { title: "Middleware Status" },
          { title: "Customer" },
          { title: "Waybill" },
          { title: "Product Detail" },
          { title: "Date" },
          { title: "Action" },
          // { title: "Order Status" },
          // { title: "SAP Status" },
          // { title: "Delhivery Status" },
          // { title: "Qty"},
          // { title: "Payment Type" },
          // { title: "Fulfillment" },
          // { title: "Shipment Status" },
          // { title: "Refund Status" },
        ]}
      >
        {rowMarkup}
      </IndexTable>
    );
  };
  render() {
    const hasError = this.state.rejectedFiles.length > 0;
    const errorMessage = hasError && <Banner title="Only xlsx file is allowed" status="critical"></Banner>
    const filters = [
      {
        key: "middlewareStatus",
        label: "Middleware Status",
        filter: (
          <ChoiceList
            title="Middleware Status"
            titleHidden
            choices={[
              { label: "PENDING", value: "PENDING" },
              { label: "PROCESSING", value: "PROCESSING" },
              { label: "READY TO SHIP", value: "READY TO SHIP" },
              { label: "IN TRANSIT", value: "IN TRANSIT" },
              { label: "DELIVERED", value: "DELIVERED" },
              { label: "CANCELLED", value: "CANCELLED" },
              { label: "RTO IN TRANSIT", value: "RTO IN TRANSIT" },
              { label: "RTO RETURNED TO WAREHOUSE", value: "RTO RETURNED TO WAREHOUSE" },
              { label: "RETURN REQUESTED", value: "RETURN REQUESTED" },
              { label: "RETURN APPROVED", value: "RETURN APPROVED" },
              { label: "RETURN PICKUP CANCELLED BY CUSTOMER", value: "RETURN PICKUP CANCELLED BY CUSTOMER" },
              { label: "RETURN IN TRANSIT", value: "RETURN IN TRANSIT" },
              { label: "RETURNED TO WAREHOUSE", value: "RETURNED TO WAREHOUSE" },
              { label: "REFUND PENDING", value: "REFUND PENDING" },
              { label: "REFUNDED", value: "REFUNDED" },
              { label: "LOST IN TRANSIT", value: "LOST IN TRANSIT" },
              { label: "GST ORDER PENDING", value: "GST ORDER PENDING" },
              { label: "OUT OF STOCK", value: "OUT OF STOCK" },
              { label: "OLD ORDERS", value: "OLD ORDERS" },
              { label: "FAILED", value: "FAILED" },
          ]}
            selected={this.state.middlewareStatus || []}
            onChange={this.handleMiddlewareStatusChange}
          />
        ),
        shortcut: true,
      }, 
      // {
      //   key: "orderStatus",
      //   label: "Order Status",
      //   filter: (
      //     <ChoiceList
      //       title="Status"
      //       titleHidden
      //       choices={[
      //         { label: "Pending", value: "Pending" },
      //         { label: "Failed", value: "Failed" },
      //         { label: "Completed", value: "Completed" },
      //         { label: "Out of stock", value: "Out of stock" },
      //         { label: "Cancel", value: "Cancel" },
      //         { label: "PincodeNotAvailable", value: "PincodeNotAvailable" },
      //         { label: "Returned", value: "Returned" },
      //         // {label: "Inprocess" , value:"inprocess"}
      //       ]}
      //       selected={this.state.orderStatus || []}
      //       onChange={this.handleStatusChange}
      //     />
      //   ),
      //   shortcut: true,
      // },
      // {
      //   key: "sapStatus",
      //   label: "SAP Status",
      //   filter: (
      //     <ChoiceList
      //       title="sapStatus"
      //       titleHidden
      //       choices={[
      //         { label: "Pending", value: "Pending" },
      //         { label: "Pushed", value: "Pushed" },
      //         { label: "Invoiced", value: "Invoiced" },
      //         { label: "Failed", value: "Failed" },
      //         { label: "Cancel", value: "Cancel" },
      //         { label: "Replacement", value: "Replacement" },
      //       ]}
      //       selected={this.state.sapStatus || []}
      //       onChange={this.handleSapStatusChange}
      //     />
      //   ),
      //   shortcut: true,
      // },
      // {
      //   key: "delhiveryStatus",
      //   label: "Delhivery Status",
      //   filter: (
      //     <ChoiceList
      //       title="delhiveryStatus"
      //       titleHidden
      //       choices={[
      //         { label: "Pending", value: "Pending" },
      //         { label: "Pushed", value: "Pushed" },
      //         { label: "Failed", value: "Failed" },
      //         { label: "Cancel", value: "Cancel" },
      //       ]}
      //       selected={this.state.delhiveryStatus || []}
      //       onChange={this.handleDelhiveryStatusChange}
      //     />
      //   ),
      //   shortcut: true,
      // },
      // {
      //   key: "paymentStatus",
      //   label: "Payment Type",
      //   filter: (
      //     <ChoiceList
      //       title="Payment Type"
      //       titleHidden
      //       choices={[
      //         { label: "COD", value: "COD" },
      //         { label: "Prepaid", value: "Prepaid" },
      //       ]}
      //       selected={this.state.paymentStatus || []}
      //       onChange={this.handlePaymentStatusChange}
      //     // allowMultiple
      //     />
      //   ),
      //   shortcut: true,
      // }, {
      //   key: "shipmentStatus",
      //   label: "Shipment Status",
      //   filter: (
      //     <ChoiceList
      //       title="Shipment Status"
      //       titleHidden
      //       choices={[
      //         { label: "Manifested", value: "manifested" },
      //         { label: "In transit", value: "in_transit" },
      //         { label: "Pending", value: "pending" },
      //         { label: "Delivered", value: "delivered" },
      //         { label: "Out for delivery", value: "out_for_delivery" },
      //         { label: "Label printed", value: "label_printed" },
      //       ]}
      //       selected={this.state.shipmentStatus || []}
      //       onChange={this.handleShipmentStatusChange}
      //     // allowMultiple
      //     />
      //   ),
      //   shortcut: true,
      // },
      // {
      //   key: "refundStatus",
      //   label: "Refund Status",
      //   filter: (
      //     <ChoiceList
      //       title="Refund Status"
      //       titleHidden
      //       choices={[
      //         { label: "Pending", value: "pending" },
      //         { label: "Failed", value: "failed" },
      //         { label: "Success", value: "success" },
      //         { label: "Credit Note Pending", value: "CN-pending" },
      //         { label: "All", value: "all" },
      //       ]}
      //       selected={this.state.refundStatus || []}
      //       onChange={this.handleRefundStatusChange}
      //     />
      //   ),
      //   shortcut: true,
      // },
      // {
      //   key: "fulfillmentStatus",
      //   label: "Fulfillment Status",
      //   filter: (
      //     <ChoiceList
      //       title="Fulfillment Status"
      //       titleHidden
      //       choices={[
      //         { label: "Unfulfilled", value: "unfulfilled" },
      //         { label: "Partially fulfilled", value: "partially fulfilled" },
      //         { label: "Fulfilled", value: "fulfilled"},
      //       ]}
      //       selected={this.state.fulfillmentStatus || []}
      //       onChange={this.handleFulfillmentStatusChange}
      //       // allowMultiple
      //     />
      //   ),
      //   shortcut: true,
      // },
    ];
    const sortOptions = [
      { label: "Date (oldest first)", value: "oldest" },
      { label: "Date (newest first)", value: "newest" },
    ];

    const appliedFilters = [];
    if (!isEmpty(this.state.orderStatus)) {
      const key = "orderStatus";
      appliedFilters.push({
        key,
        label: disambiguateLabel(key, this.state.orderStatus),
        onRemove: this.handleStatusRemove,
      });
    }
    if (!isEmpty(this.state.fulfillmentStatus)) {
      const key = "fulfillmentStatus";
      appliedFilters.push({
        key,
        label: disambiguateLabel(key, this.state.fulfillmentStatus),
        onRemove: this.handleFulfillmentStatusRemove,
      });
    }
    if (!isEmpty(this.state.paymentStatus)) {
      const key = "paymentStatus";
      appliedFilters.push({
        key,
        label: disambiguateLabel(key, this.state.paymentStatus),
        onRemove: this.handlePaymentStatusRemove,
      });
    }
    if (!isEmpty(this.state.sapStatus)) {
      const key = "sapStatus";
      appliedFilters.push({
        key,
        label: disambiguateLabel(key, this.state.sapStatus),
        onRemove: this.handleSapStatusRemove,
      });
    }
    if (!isEmpty(this.state.delhiveryStatus)) {
      const key = "delhiveryStatus";
      appliedFilters.push({
        key,
        label: disambiguateLabel(key, this.state.delhiveryStatus),
        onRemove: this.handlesDelhiveryStatusRemove,
      });
    }
    if (!isEmpty(this.state.shipmentStatus)) {
      const key = "shipmentStatus";
      appliedFilters.push({
        key,
        label: disambiguateLabel(key, this.state.shipmentStatus),
        onRemove: this.handleShipmentStatusRemove,
      });
    }
    if (!isEmpty(this.state.refundStatus)) {
      const key = "refundStatus";
      appliedFilters.push({
        key,
        label: disambiguateLabel(key, this.state.refundStatus),
        onRemove: this.handleRefundStatusRemove,
      });
    }
    if (!isEmpty(this.state.middlewareStatus)) {
      const key = "middlewareStatus";
      appliedFilters.push({
        key,
        label: disambiguateLabel(key, this.state.middlewareStatus),
        onRemove: this.handleMiddlewareStatusRemove,
      });
    }

    return (
      <div>
        <Page
          fullWidth
          title="Orders"
          primaryAction={
            <Stack>
              {/* <Button primary onClick={this.handlesyncOrderModal}>
                {" "}
                Sync Shopify Orders
              </Button> */}
               <Button primary onClick={this.handleImportModal}>IMPORT REFUND DATA</Button>
              <DateRangePicker
                align="right"
                primary={this.state.primary}
                handleApplyFilter={(value) => {
                  this.setState(
                    {
                      startDate: value.startDate,
                      endDate: value.endDate,
                      selectedDate: value.selectedDate,
                      selectedDateText: value.selectedDateText,
                    },
                    () => {
                      this.getAllOrders();
                    }
                  );
                }}
              />
            </Stack>
          }
        >
          <Layout>
            <Layout.Section>
              <Card>
                <Card.Section>
                  <div style={{ padding: "16px", display: "flex" }}>
                    <div style={{ flex: 1 }}>
                      <Filters
                        queryPlaceholder="Search"
                        label="Title"
                        filters={filters}
                        queryValue={this.state.queryValue}
                        appliedFilters={appliedFilters}
                        onQueryChange={this.handleFiltersQueryChange}
                        onQueryClear={this.handleQueryValueRemove}
                        // value={this.state.searchField}
                        onClearAll={this.handleFiltersClearAll}
                      />
                    </div>
                    <div style={{ paddingLeft: "0.4rem" }}>
                      <Button icon={SortMinor} onClick={this.togglePopover}>
                        Sort
                      </Button>
                    </div>
                    <div>
                      <Popover
                        fluidContent={true}
                        active={this.state.popactive}
                        activator={<div></div>}
                        onClose={this.togglePopover.bind(this)}
                        preferredAlignment="right"
                        preferredPosition="below"
                        footerContent
                      >
                        <Card>
                          <Card.Section>
                            <ChoiceList
                              title="Sort By"
                              choices={sortOptions}
                              selected={this.state.sortValue}
                              onChange={this.handleChange}
                            />
                          </Card.Section>
                        </Card>
                      </Popover>
                    </div>
                  </div>
                </Card.Section>
                {this.orderListMarkup()}
                <Card.Section>
                  <Stack
                    alignment="center"
                    vertical="middle"
                    distribution="center"
                  >
                    <Pagination
                      plain
                      hasPrevious={pagination.hasPrevious}
                      onPrevious={() => {
                        this.handleChanagePage("Previous");
                      }}
                      hasNext={pagination.hasNext}
                      onNext={() => {
                        this.handleChanagePage("Next");
                      }}
                    />
                  </Stack>
                </Card.Section>
              </Card>
            </Layout.Section>
          </Layout>
        </Page>
        {this.state.serviceModalActive != false ?
                    <ServiceRequestModal modalBtnLoading={this.props.modalBtnLoading} orderItemData={this.state.orderItemData} serviceModalActive={this.state.serviceModalActive} handleServiceModal={this.handleServiceModal} handleSubmitService={this.handleSubmitService}/>
                    : ""}
        <Modal
          title="Sync Shopify Orders"
          open={this.state.syncModalActive}
          onClose={this.handlesyncOrderModal}
          primaryAction={{
            content: "Yes",
            onAction: this.handlesyncOrder,
            loading: this.state.syncButtonLoading,
          }}
          secondaryActions={[
            {
              content: "No",
              onAction: this.handlesyncOrderModal,
            },
          ]}
        >
          <Modal.Section>
            <TextContainer>
              <p>Are you sure you want to sync shopify orders?</p>
            </TextContainer>
          </Modal.Section>
        </Modal>

        <Modal
            title="IMPORT REFUND DATA"
            open={this.state.importModalActive}
            onClose={this.handleImportModal}
            primaryAction={{
              content: 'Save',
              onAction: this.saveCfaModal,
              loading: this.state.importButtonLoading,
              disabled: this.state.isSaveDisable
            }}
          >
            <Modal.Section>
              <FormLayout>
                {errorMessage}
                <DropZone
                  label="Select File"
                  allowMultiple={false}
                  error={true}
                  accept=".xlsx"
                  type="file"
                  onDrop={this.handleDrop}
                >
                  <Stack vertical>
                    {this.state.files !== '' ? (
                      this.state.files.map((file, index) => (
                        <Stack alignment="center" key={index}>
                          <div>
                            {file.name} <Caption>{file.size} bytes</Caption>
                          </div>
                        </Stack>
                      ))
                    ) : ""}
                    <DropZone.FileUpload />
                  </Stack>
                </DropZone>
                {/* <div className = "error-text">{this.state.errorMessage}</div>            */}
              </FormLayout>
            </Modal.Section>
          </Modal>

        <Modal
          medium
          title={this.state.modalHeader}
          open={this.state.orderDetailModalActive}
          onClose={this.handleOrderDetailModalClose}
        >
          {this.state.appOrderStatus == "Out Of Stock" ? (
            <Modal.Section>
              <div>
                <Stack>
                  <Stack.Item fill>
                    <Select
                      labelInline
                      label="Select CFA"
                      options={this.state.cfaOption}
                      onChange={this.handleSelectCFA}
                      value={this.state.selectedCFa}
                    />
                  </Stack.Item>
                  <Stack.Item>
                    <Button loading={this.state.cfaButttonLoading} onClick={this.handleSubmitCFA}>Submit</Button>
                  </Stack.Item>
                </Stack>
              </div>
            </Modal.Section>
          ) : null}
          <Modal.Section>

            {Object.values(this.state.cancelOrderData).some(val => val) == true ? (
              <div>
                <div>
                  <Stack distribution="equalSpacing">
                    <Heading>Order Canceled </Heading>
                    <Link onClick={() => this.handleOrderDetailsEdit('cancel')}>Edit</Link>
                  </Stack>
                  <br></br>
                  <Stack distribution="fillEvenly">
                    {(<p>Order#</p>)}
                    {(<p>WayBill#</p>)}
                    {(<p>SAPOrder#</p>)}
                    {(<p>SAPInvoice#</p>)}
                    {(<p>SAPDelivery#</p>)}
                  </Stack>
                  <Stack distribution="fillEvenly">
                    {this.state.createdOrderData.orderName == null || this.state.createdOrderData.orderName == "" ? (<div></div>) : (<Badge>{this.state.createdOrderData.orderName}</Badge>)}
                    {this.state.createdOrderData.waybillNumber == null || this.state.createdOrderData.waybillNumber == "" ? (<div></div>) : this.state.createdOrderData.waybillNumber.split(",").length == 2 ? (
                      <Stack vertical={true} spacing="tight">
                        <Badge>{this.state.createdOrderData.waybillNumber.split(",")[0]}</Badge>
                        <Badge>{this.state.createdOrderData.waybillNumber.split(",")[1]}</Badge>
                      </Stack>
                    ) : (<Badge>{this.state.createdOrderData.waybillNumber}</Badge>)}
                    {this.state.cancelOrderData.sapOrderNumber == null || this.state.cancelOrderData.sapOrderNumber == "" ? (<div></div>) : (<Badge>{this.state.cancelOrderData.sapOrderNumber}</Badge>)}
                    {this.state.cancelOrderData.sapInvoiceNumber == null || this.state.cancelOrderData.sapInvoiceNumber == "" ? (<div></div>) : (<Badge>{this.state.cancelOrderData.sapInvoiceNumber}</Badge>)}
                    {this.state.cancelOrderData.sapDeliveryNumber == null || this.state.cancelOrderData.sapDeliveryNumber == "" ? (<div></div>) : (<Badge>{this.state.cancelOrderData.sapDeliveryNumber}</Badge>)}
                  </Stack>
                </div>
                <br></br>
              </div>
            ) : null}

            {Object.values(this.state.customerToCfaData).some(val => val) == true ? (
              <div>
                <Heading>Order Return</Heading>
                <br></br>
                  
                  <div>
                    <Stack distribution="equalSpacing">
                      <b>Customer - CFA</b>
                      <Link onClick={() => this.handleOrderDetailsEdit('replaced')}>Edit</Link>
                    </Stack>
                    <br></br>
                    <Stack distribution="fillEvenly">
                      {(<p>Order#</p>)}
                      {(<p>WayBill#</p>)}
                      {(<p>SAPOrder#</p>)}
                      {(<p>SAPInvoice#</p>)}
                      {(<p>SAPDelivery#</p>)}
                    </Stack>
                    <Stack distribution="fillEvenly">
                      {this.state.customerToCfaData.orderName == null || this.state.customerToCfaData.orderName == "" ? (<div></div>) : (<Badge>{this.state.customerToCfaData.orderName}</Badge>)}
                      {this.state.customerToCfaData.waybillNumber == null || this.state.customerToCfaData.waybillNumber == "" ? (<div></div>) : this.state.customerToCfaData.waybillNumber.split(",").length == 2 ? (
                        <Stack vertical={true} spacing="tight">
                          <Badge>{this.state.customerToCfaData.waybillNumber.split(",")[0]}</Badge>
                          <Badge>{this.state.customerToCfaData.waybillNumber.split(",")[1]}</Badge>
                        </Stack>
                      ) : (<Badge>{this.state.customerToCfaData.waybillNumber}</Badge>)}
                      {this.state.customerToCfaData.sapOrderNumber == null || this.state.customerToCfaData.sapOrderNumber == "" ? (<div></div>) : (<Badge>{this.state.customerToCfaData.sapOrderNumber}</Badge>)}
                      {this.state.customerToCfaData.sapInvoiceNumber == null || this.state.customerToCfaData.sapInvoiceNumber == "" ? (<div></div>) : (<Badge>{this.state.customerToCfaData.sapInvoiceNumber}</Badge>)}
                      {this.state.customerToCfaData.sapDeliveryNumber == null || this.state.customerToCfaData.sapDeliveryNumber == "" ? (<div></div>) : (<Badge>{this.state.customerToCfaData.sapDeliveryNumber}</Badge>)}
                    </Stack>
                  </div>
                  <br></br>

              </div>
            ) : null}
            {Object.values(this.state.createdOrderData).some(val => val) == true ? (
              <div>
                <Stack distribution="equalSpacing">
                  <Heading>Order Created</Heading>
                  <Link onClick={() => this.handleOrderDetailsEdit('created')}>Edit</Link>
                </Stack>
                <br></br>
                <Stack distribution="fillEvenly">
                  {(<p>Order#</p>)}
                  {(<p>WayBill#</p>)}
                  {(<p>SAPOrder#</p>)}
                  {(<p>SAPInvoice#</p>)}
                  {(<p>SAPDelivery#</p>)}
                </Stack>
                <Stack distribution="fillEvenly" >
                  {this.state.createdOrderData.orderName == null || this.state.createdOrderData.orderName == "" ? (<div></div>) : (<Badge >{this.state.createdOrderData.orderName}</Badge>)}
                  {this.state.createdOrderData.waybillNumber == null || this.state.createdOrderData.waybillNumber == "" ? (<div></div>) : this.state.createdOrderData.waybillNumber.split(",").length == 2 ? (
                    <Stack vertical={true} spacing="tight">
                      <Badge>{this.state.createdOrderData.waybillNumber.split(",")[0]}</Badge>
                      <Badge>{this.state.createdOrderData.waybillNumber.split(",")[1]}</Badge>
                    </Stack>
                  ) : (<Badge>{this.state.createdOrderData.waybillNumber}</Badge>)}
                  {this.state.createdOrderData.sapOrderNumber == null || this.state.createdOrderData.sapOrderNumber == "" ? (<div></div>) : (<Badge>{this.state.createdOrderData.sapOrderNumber}</Badge>)}
                  {this.state.createdOrderData.sapInvoiceNumber == null || this.state.createdOrderData.sapInvoiceNumber == "" ? (<div></div>) : (<Badge>{this.state.createdOrderData.sapInvoiceNumber}</Badge>)}
                  {this.state.createdOrderData.sapDeliveryNumber == null || this.state.createdOrderData.sapDeliveryNumber == "" ? (<div></div>) : (<Badge>{this.state.createdOrderData.sapDeliveryNumber}</Badge>)}
                </Stack>
              </div>
            ) : (
              <div>
                <b>Order is Created</b>
                <br></br>
              </div>)}
          </Modal.Section>
        </Modal>

        <Modal
          medium
          title={this.state.modalHeader}
          open={this.state.editModalActive}
          onClose={this.handleOrderDetailsEdit}
          primaryAction={{
            content: 'Save',
            onAction: this.handleSaveOrderDetails,
            loading: this.state.editModalIsLoading
          }}
          secondaryActions={{
            content: 'Cancel',
            onAction: this.handleOrderDetailsEdit
          }}
        >
          <Modal.Section>
            <Stack distribution="fillEvenly">
              <b>WayBill Number</b>
              <TextField
                value={this.state.editedOrderStatus == 'cancel' ? this.state.cancelOrderData.waybillNumber :
                  this.state.editedOrderStatus == 'replaced' ? this.state.customerToCfaData.waybillNumber : this.state.createdOrderData.waybillNumber}
                onChange={(val) => this.handleOrderDetailsText(val, 'waybillNumber')}
                placeholder="Enter waybill number"
                autoComplete="off"
              />
            </Stack><br />
            <Stack distribution="fillEvenly">
              <b>SAPInvoice Number</b>
              <TextField
                value={this.state.editedOrderStatus == 'cancel' ? this.state.cancelOrderData.sapInvoiceNumber :
                  this.state.editedOrderStatus == 'replaced' ? this.state.customerToCfaData.sapInvoiceNumber : this.state.createdOrderData.sapInvoiceNumber}
                onChange={(val) => this.handleOrderDetailsText(val, 'sapInvoiceNumber')}
                placeholder="Enter SAPInvoice number"
                autoComplete="off"
              />
            </Stack><br />
            <Stack distribution="fillEvenly">
              <b>SAPDelivery Number</b>
              <TextField
                value={this.state.editedOrderStatus == 'cancel' ? this.state.cancelOrderData.sapDeliveryNumber :
                  this.state.editedOrderStatus == 'replaced' ? this.state.customerToCfaData.sapDeliveryNumber : this.state.createdOrderData.sapDeliveryNumber}
                onChange={(val) => this.handleOrderDetailsText(val, 'sapDeliveryNumber')}
                placeholder="Enter SAPDelivery number"
                autoComplete="off"
              />
            </Stack><br />
            <Stack distribution="fillEvenly">
              <b>SAPOrder Number</b>
              <TextField
                value={this.state.editedOrderStatus == 'cancel' ? this.state.cancelOrderData.sapOrderNumber :
                  this.state.editedOrderStatus == 'replaced' ? this.state.customerToCfaData.sapOrderNumber : this.state.createdOrderData.sapOrderNumber}
                onChange={(val) => this.handleOrderDetailsText(val, 'sapOrderNumber')}
                placeholder="Enter SAPOrder number"
                autoComplete="off"
              />
            </Stack><br />
            <Stack distribution="fillEvenly">
              <b>CFA Number</b>
              <TextField
                value={this.state.editedOrderStatus == 'cancel' ? this.state.cancelOrderData.cfaNumber :
                  this.state.editedOrderStatus == 'replaced' ? this.state.customerToCfaData.cfaNumber : this.state.createdOrderData.cfaNumber}
                onChange={(val) => this.handleOrderDetailsText(val, 'cfaNumber')}
                placeholder="Enter CFA number"
                autoComplete="off"
              />
            </Stack>
          </Modal.Section>
        </Modal>

        <Modal
          medium
          title={this.state.actionsConfirmationHeader}
          open={this.state.actionsConfirmationActive}
          onClose={this.actionsConfirmationClose}
          primaryAction={{
            content: 'Yes',
            onAction: this.actionsConfirmationModal,
            loading: this.state.ConfirmationLoadingButton
          }}
          secondaryActions={{
            content: 'No',
            onAction: this.actionsConfirmationClose
          }}
        >
          <Modal.Section>
            <TextContainer>
              <p>Are you sure you want to do this?</p>
            </TextContainer>
          </Modal.Section>
        </Modal>
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  orderListAllData: state.order.data,
  loading: state.order.loading,
  error: state.order.error,
  orderResponseData: state.order.orderData,
  orderLoading: state.order.orderLoading,
  cfaListData: state.cfa.cfaData,
  cfaListloading: state.cfa.cfaListloading
});
const mapDispatchToProps = (dispatch) => ({
  getOrdersList: (orderData) => dispatch(getOrdersListRequest(orderData)),
  pushOrders: (object) => dispatch(pushOrdersRequest(object)),
  cancelOrders: (object) => dispatch(cancelOrdersRequest(object)),
  returnOrders: (object) => dispatch(orderReturnRequest(object)),
  exportOrders: (object) => dispatch(createExportOrdersRequest(object)),
  refundOrders: (object) => dispatch(createRefundOrdersRequest(object)),
  syncOrder: (OrderSyncData) => dispatch(syncOrderRequest(OrderSyncData)),
  submitOrderCFA: (object) => dispatch(submitOrderCFARequest(object)),
  getCFAList: (orderData) => dispatch(getMannualCFAListRequest(orderData)),
  getCFAList: (orderData) => dispatch(getMannualCFAListRequest(orderData)),
  saveOrderDetails: (object) => dispatch(saveOrderDetailsRequest(object)),
  handleSubmitRequest:(object)=>dispatch(handleSubmitServiceRequest(object)),
  saveRefundFile: (object) => dispatch(saveRefundFileRequest(object)),

});

export default connect(mapStateToProps, mapDispatchToProps)(Orders);
