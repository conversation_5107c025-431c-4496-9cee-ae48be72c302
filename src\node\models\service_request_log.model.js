const Sequelize = require("sequelize");
module.exports = (sequelize) => {
  return sequelize.define("service_request_log", {
    id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      unique: true
    },
    order_name: {
      type: Sequelize.STRING
    },
    request_id: {
      type: Sequelize.STRING
    },
    customer_code: {
      type: Sequelize.STRING,
      defaultValue: null
    },
    response_id: {
      type: Sequelize.STRING,
      defaultValue: null
    },
    call_id: {
      type: Sequelize.STRING,
      defaultValue: null
    },
    message_description: {
      type: Sequelize.STRING,
      defaultValue: null
    },
    call_registration_date: {
      type: Sequelize.STRING,
      defaultValue: null
    },
    call_closed_date: {
      type: Sequelize.STRING,
      defaultValue: null
    },
    call_status: {
      type: Sequelize.STRING,
      defaultValue: null
    },
  });
};