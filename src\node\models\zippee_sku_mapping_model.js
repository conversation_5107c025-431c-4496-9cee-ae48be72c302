const Sequelize = require("sequelize");
module.exports = (sequelize) => {
    return sequelize.define("zippee_sku_mapping", {
        id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            unique: true
        },
        store_id: {
            allowNull: false,
            type: Sequelize.INTEGER,
            references: {
                model: 'stores',
                key: 'id'
            },
        },
        cfa: {
            type: Sequelize.STRING
        },
        sku: {
            type: Sequelize.STRING
        }
    },
        {
            indexes: [
                {
                    unique: true,
                    fields: ['sku', 'cfa']
                }
            ]
        });
};