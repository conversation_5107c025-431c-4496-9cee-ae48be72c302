const Sequelize = require("sequelize");
module.exports = (sequelize) => {
    return sequelize.define("order_log_history", {
        id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            unique: true
        },
        shopify_order_id: {
            allowNull: true,
            type: Sequelize.STRING,
            defaultValue: null
        },
        shopify_order_name: {
            allowNull: true,
            type: Sequelize.STRING,
            defaultValue: null
        },
        order_name: {
            allowNull: true,
            type: Sequelize.STRING,
            defaultValue: null
        },
        platform_type: {
            allowNull: true,
            type: Sequelize.STRING,
            defaultValue: null
        },
        request_data: {
            allowNull: true,
            type: Sequelize.TEXT,
            defaultValue: null
        },
        response_status_code: {
            allowNull: true,
            type: Sequelize.STRING,
            defaultValue: null
        },
        response_data: {
            allowNull: true,
            type: Sequelize.TEXT,
            defaultValue: null
        },

    });
};