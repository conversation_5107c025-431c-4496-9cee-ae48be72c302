const express = require('express');
const app = express();
const cors = require('cors');
const path = require('path');
const dotenv = require("dotenv");
dotenv.config();
global.__basedir = __dirname;

process.env.NODE_TLS_REJECT_UNAUTHORIZED = "0";
const userAuth = require("./middleware/userAuth");
const db = require("./models");

db.sequelize.sync({ alter: false });

app.use(express.static(path.join(__dirname, 'assests')));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cors());

app.use(express.static(path.join(__dirname, 'bundle')));

app.get('/app/*', (req, res) => {
    res.sendFile(path.join(__dirname, 'bundle', 'index.html'));
});

app.all('/api/*', userAuth.authenticateToken, function (req, res, next) {
    next();
});
app.all('/limechat/*', userAuth.authenticateLimeChat, function (req, res, next) {
    next();
});

app.get('/shoplogin', async (req, res) => {
    res.sendFile(path.join(__dirname, 'assests', 'views', 'login.html'));
});

process.on('uncaughtException', function (err) {
    console.error(err.message);
});

require("./routes/auth-route")(app);
require("./cronJobs")();
require("./routes/fulfillmentService-route")(app);
require("./routes/delhivery-route")(app);
require("./routes/crm-route")(app);
require("./routes/gstValidation-route")(app);
require("./routes/order-route")(app);
require("./routes/product-route")(app);
require("./routes/cfa-route")(app);
require("./routes/sap-route")(app);
require("./routes/ftp-route")(app);
require("./routes/settings-route")(app);
require("./routes/track-order-route")(app);
require("./routes/email-route")(app);
require("./routes/webhook-route")(app);
require("./routes/warranty-route")(app);
require("./routes/limechat-route")(app);
require("./routes/zippee-route.js")(app);
require("./routes/utils-route")(app);

const port = process.env.PORT || 5000;
app.listen(port, () => {
    console.log(`APP LISTENING ON PORT ${port}`);
});