const dbConnection = require("../models");
const sapController = require("./sap_controller")
const delhiveryController = require("./delhivery-controller");
const orderSyncController = require("./ordersync-controller");
const refundController = require("./refund-controller");

const DELHIVERY_SHIPPING_STATUS = {
  'In Transit': 'in_transit',
  'Dispatched': 'out_for_delivery',
  'Delivered': 'delivered'
};

exports.webhook = async (req, res) => {
  try {
    const data = req.body;
    if (!data) {
      res.status(500).send({ status: false, message: "Didn't get any data from body" })
    }else{
      res.status(200).send({ status: true, message: "Success" })
    }
    const status = data.Shipment.Status.Status;
    const statusType = data.Shipment.Status.StatusType;
    // const orderName = data.Shipment.ReferenceNo.split("-R")[0];
    const orderName = data.Shipment.ReferenceNo.match(/[^-]+-[^-]+/)[0];
    const shipmentStaus = getStatusValue(DELHIVERY_SHIPPING_STATUS, status);
    let orderData = await delhiveryController.getorderSplitDataById(orderName);
    if (!orderData) {
      return { status: false, message: "orderData is not found" };
    }

    let orderObj = {
      shipment_status: status,
    };
   
    const previousMiddlewareStatus = orderData.data.middleware_status
    let fulfillmentevent = false
    if (status == "Delivered" && statusType == "DL") {
      orderObj.middleware_status = "DELIVERED";
      orderObj.is_replaceable = "1";
      orderObj.delivered_at = new Date().toISOString();
      fulfillmentevent = true
      await dbConnection.orderItemSplit.update(orderObj, { where: { order_name: orderName }}); //need to check

      let orderDataName = await orderSyncController.getorderSplitDataByName(orderName)
      orderDataName = JSON.parse(JSON.stringify(orderDataName.data))

      let cloudData = await dbConnection.childSku.findOne({ where: { child_sku: orderDataName.sku, is_cloud: "1" }});
      if (cloudData) {
        orderSyncController.callCloudApi(orderDataName);
      }
    }
    
    if (status == "Dispatched" && statusType == "UD") {
      fulfillmentevent = true
    }
    
    if (status == "In Transit" && statusType == "UD") {
      fulfillmentevent = true
      orderObj.is_pickup = "1";
      orderObj.middleware_status = "IN TRANSIT";
      let pickupDate = new Date().toISOString();
      if(previousMiddlewareStatus != "IN TRANSIT"){
        await dbConnection.delhiveryLog.update({ pickup_date: pickupDate },{ where: { order_name: orderName, is_cancel: "0", is_return: "0" } });
      }
    }

    if ((status == "DTO" || status == "dto") && statusType == "DL") {
      orderObj.middleware_status = "RETURNED TO WAREHOUSE";
      let dbRes = await dbConnection.orderCancellation.create({
        order_name: orderData.data.order_name,
        status_type: "order_replacement_webhook",
        return_order_name: orderData.data.return_order_name,
        middleware_status: "RETURNED TO WAREHOUSE",
      });
      if (dbRes.dataValues.id) {
        sapController.returnSaleOrderSap(orderData.data);
        refundController.checkingForRefund(orderData.data);
      }
    }

    if ((status == "RTO" || status == "rto") && statusType == "DL" && orderData.shipment_status != "delivered") {
      let dbRes = await dbConnection.orderCancellation.create({ order_name: orderData.data.order_name, status_type: "order_cancel_rto",
        return_order_name: orderData.data.return_order_name ? orderData.data.return_order_name : orderData.data.order_name,
        middleware_status: 'CANCELLED RTO ORDERS',
      });
      if (dbRes.dataValues.id) {
        orderObj.middleware_status= 'RTO RETURNED TO WAREHOUSE'
        delhiveryController.cancelOrder(orderData.data.order_name, true);
        refundController.checkingForRefund(orderData.data);
      }
    }

    if (status == "Canceled" && statusType == "CN") {
      orderObj.middleware_status = "RETURN PICKUP CANCELLED BY CUSTOMER";
      orderObj.order_status = "Completed";
      orderObj.return_applied_status = null;
      orderObj.is_return = "0";
    }

    if (status == "In Transit" && statusType == "PU") {
      orderObj.middleware_status = "RETURN IN TRANSIT";
      let pickupDate = new Date().toISOString()
      await dbConnection.delhiveryLog.update({ pickup_date: pickupDate }, { where: { order_name: orderName, is_return: "1" } })
    }

    if (status == "In Transit" && statusType == "RT") {
      orderObj.middleware_status = "RTO IN TRANSIT";
    }

    await dbConnection.orderItemSplit.update(orderObj, {where: { order_name: orderName }});
    let checkMiddlewareStatus = await this.checkMiddlewareStatus(previousMiddlewareStatus,orderObj.middleware_status);
    if(fulfillmentevent && checkMiddlewareStatus){
      let getFulfillData = await dbConnection.fulfillmentItem.findOne({where: { order_id: orderData.data.id }});
      if (getFulfillData) {
        let shopResponse = await dbConnection.shop.findOne({where: { id: orderData.data.store_id }});
        const paramObj = {
          orderId: orderData.data.shopify_order_id,
          fulfillmentId: getFulfillData.dataValues.fulfillment_id,
          shopResponse: shopResponse,
          shipmentStatus: shipmentStaus,
        };
        const updateEvent = delhiveryController.updateFulfillmentEvent(paramObj);
        return { status: true, message: "Success" };
      } else {
        return { status: false, message: "tracking number is not present" };
      }
    }
    return { status: true, message: "Success" };
  } catch (err) {
    console.log("error==>", err);
    res.status(500).send({ status: false, message: err?.message })
  }
}

exports.checkMiddlewareStatus = async (oldmiddlewareStatus, newmiddlewareStatus) => oldmiddlewareStatus !== newmiddlewareStatus;
const getStatusValue = (statusObject, statusKey) => statusObject?.[statusKey] || null;

exports.shipmentStatus = async (status) => {
  if (status == 'In Transit') {
    return "in_transit"
  }
  if (status == 'Manifested') {
    return "label_printed"
  }
  if (status == 'Dispatched') {
    return "out_for_delivery"
  }
  if (status == 'Delivered') {
    return "delivered"
  }
}