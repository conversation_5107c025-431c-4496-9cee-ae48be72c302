const { sendEmailToCustomer } = require('../controllers/email-controller');
const delhiveryController = require(".././controllers/delhivery-controller");
const sapController = require(".././controllers/sap_controller");
const orderSynController = require(".././controllers/ordersync-controller")
const cfaController = require(".././controllers/cfa-controller.js");
const upload = require('.././helper/uploadFile')
const { razorpaySettlement } = require('.././controllers/razorpay-controller');
const { getSapInvoiceNumber, createOrderOnSAPforWarranty } = require(".././controllers/extendedWarranty/extendedWarrantyService-controller");
const moment = require('moment/moment');
const { Op, QueryTypes } = require('sequelize');
const db = require(".././models");
const { fulfillOrders } = require("../cronJobs.js");

const {createWarrantyFulfillment}= require('../controllers/extendedWarranty/extendedWarrantyService-controller.js')
module.exports = app => {
  const { encryptString, decryptString } = require('../helper/helper');
  const router = require("express").Router();

  router.get('/create-warranty-fulfillment', async (req, res) => {
    try {
      const result = await createWarrantyFulfillment();
      res.status(201).json(result);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });

  // Encrypt route
  router.post('/encrypt', (req, res) => {
    try {
      const { data } = req.body;
      if (!data) return res.status(400).json({ error: 'Missing data' });
      const encrypted = encryptString(data);
      res.json({ encrypted });
    } catch (err) {
      res.status(500).json({ error: err.message });
    }
  });

  // Test route for the fulfillment
  router.post('/test/fulfillment', async (req, res) => {
    try {
      const result = await fulfillOrders();
      console.log("Fulfillment test result:", result);
      res.status(200).json({ message: result});
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });

  // Decrypt route
  router.post('/decrypt', (req, res) => {
    try {
      const { data } = req.body;
      if (!data) return res.status(400).json({ error: 'Missing data' });
      const decrypted = decryptString(data);
      res.json({ decrypted });
    } catch (err) {
      res.status(500).json({ error: err.message });
    }
  });


  //mail sending route
  router.get('/send-email', async (req, res) => {
    let orderSplitData = await db.sequelize.query("SELECT * FROM order_item_splits where is_send_mail = '0' and invoice_url is not null and is_cancel != '1' AND sap_billing_number IS NOT NULL and createdAt >'2022-12-31'  order by createdAt desc ", { type: QueryTypes.SELECT })
    res.status(200).send("email sent to customers")
    for (let order of orderSplitData) {
      await sendEmailToCustomer(order.order_name, order.invoice_url)
    }
  })

  //manully call sapDelivery biling api
  router.get('/sapBillingOrdereveryhour', async (req, res) => {
    try {
      res.status(200).send("sapBillingOrdereveryhour")
      let orderNames = await db.sequelize.query(
        `SELECT order_name, order_created_at  FROM order_item_splits WHERE middleware_status NOT IN ('FAILED', 'OLD ORDERS') 
              AND sap_status ='Pushed' 
              AND is_return!='1' 
              AND is_cancel!='1' 
              AND sku != 'ACOPE379'
              AND delhivery_status= 'Pushed'
              AND order_created_at > '2024-04-09'
              order by order_created_at ASC`,
        { type: QueryTypes.SELECT }
      );
      if (orderNames.length > 0) {
        for (let orderName of orderNames) {
          await delhiveryController.sapDelivery(orderName.order_name);
          console.log('index sapBillingOrdereveryhour -->', orderName.order_name)
          await sleep(3000); // 3 second break
        }
      }
    } catch (error) {
      console.log("error sapBillingOrdereveryhour", error)
    }
  })


  //manully call installation api
  router.post('/cloud/installation', async (req, res) => {
    let orderNames = req.body.names
    res.status(200).send({ status: "suceess" })
    for (let orderName of orderNames) {
      let orderDataName = await orderSynController.getorderSplitDataByName(orderName)
      let orderResponse = JSON.parse(JSON.stringify(orderDataName.data))
      await orderSynController.callCloudApi(orderResponse)
    }
  })

  function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  router.post('/cancel/sap/delhivery', async (req, res) => {
    let orderNames = req.body.names
    if (orderNames.length > 0) {
      for (let orderName of orderNames) {
        console.log('orderName--->', orderName);
        await delhiveryController.sapDelivery(orderName)
      }
    }
  })

  router.post('/cancel/delhivery/rto', async (req, res) => {
    let orderNames = req.body.names
    res.status(200).send("cancel delhivery rti api called")
    console.log("cancel delhivery rti api called------>")
    if (orderNames.length > 0) {
      for (let orderName of orderNames) {
        await delhiveryController.cancelOrder(orderName, true)
      }
    }
  })

  router.post('/create/creditNote', async (req, res) => {
    let orderNames = req.body.names
    res.status(200).send("Credit Note Generated")
    for (let orderName of orderNames) {
      await delhiveryController.sapDelivery(orderName)
    }
  })

  router.post('/fetchSapId', async (req, res) => {
    const sapOrderLogs = await db.sapLog.findAll({
      where: {
        sap_billing_number: { [Op.ne]: null },
        sap_conformation_id: null,
        is_clear: "0",
      },
    });

    await sapController.sapConfirmationId(sapOrderLogs);
  })



  router.post('/warranty/invoice', async (req, res) => {

    res.status(200).send({ status: "suceess" })

    const ordersData = await db.extendedWarrantyOrder.findAll({
      where: { sap_status: "pushed", middleware_status: ["PROCESSING", "FAILED"] },
      raw: true,
    });

    if (ordersData.length) {
      for (let orderData of ordersData) {
        await getSapInvoiceNumber(orderData)
      }
    }
  })


  router.post('/warranty/order', async (req, res) => {

    res.status(200).send({ status: "suceess" })

    const ordersData = await db.extendedWarrantyOrder.findAll({
      include: [
        {
          model: db.orderItemSplit, attributes: ["product_serial_number"],
          as: "orderItemSplit",
          required: true,
          where: { middleware_status: "DELIVERED" }
        },
      ],
      where: { middleware_status: "PENDING", sap_status: "pending" },
      raw: true,
      nest: true,
    });
    console.log("ordersDatacreateOrderOnSAPforWarranty===>", ordersData.length)
    if (ordersData.length) {
      for (let orderData of ordersData) {
        await createOrderOnSAPforWarranty(orderData.main_order_name)
      }
    }
  })

  router.post('/warranty/orderfailed', async (req, res) => {

    res.status(200).send({ status: "suceess" })

    let orderNames = req.body.names
    if (orderNames.length > 0) {
      for (let orderName of orderNames) {
        await createOrderOnSAPforWarranty(orderName)
      }
    }
  })

  router.get('/sapstock', async (req, res) => {
    sapController.syncSapInventory()
    res.status(200).send("sapstock sync successful")
  })

  router.get("/fetch/razorpay_settlement", async (req, res) => {
    try {
      let orders = await db.orderItemSplit.findAll({
        where: {
          settlement_id: {
            [Op.is]: null
          },
          is_cancel: '0',
          financial_status: 'paid',
          gateway: {
            [Op.not]: "Cash on Delivery (COD)"
          },
          is_return: '0',
          is_replaceable: '0',
          shipment_status: {
            [Op.like]: '%delivered%'
          },
          delivered_at: {
            [Op.gt]: moment().subtract(60, 'days').format("YYYY-MM-DD"),
          }
        },
        attributes: ['id', 'store_id', 'shopify_order_id', 'order_name', 'order_status', 'order_amount', 'settlement_id', 'checkout_id']
      })
      res.send({ message: "success", data: orders.length })
      await razorpaySettlement(orders);
    } catch (error) {
      console.log("error razorpay_settlement", error)
      res.send({ error })
    }
  })

  router.post("/pincode/import", upload.single('file'), cfaController.importCityStateData)
  //API Routes
  app.use('/', router);
};
