# Variables for staging environment
variables:
  PROD_DEPLOY_PACKAGE_NAME: "prod-node-deploy-${CI_COMMIT_SHA}.zip"
  S3_BUCKET_NAME: "symphony-cicd"
  PROD_EC2_HOST: "5.5555.33.23"
  EC2_USER: "ec2-user"
  AWS_DEFAULT_REGION: "ap-south-1"

# Debug job to confirm staging.yml is executing
debug_production:
  stage: pre
  script:
    - echo "ci/production.yml is successfully included and running for production environment"
    - ls -l # Debug: List current directory

build_and_package:
  stage: build_and_package
  image: node:20
  script:
    - apt-get update && apt-get install -y curl zip
    - curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
    - unzip awscliv2.zip
    - ./aws/install || { echo "AWS CLI installation failed"; exit 1; }

    - echo "AWS CLI installed successfully and running"
    - aws --version

    - echo "Configuring AWS CLI with provided credentials"
    - aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID
    - aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY
    - aws configure set region ${AWS_DEFAULT_REGION}

    - echo "Creating .env file with NODE_ENV"
    - echo "NODE_ENV=production" > .env
    - echo ".env file created:"
    - cat .env

    - echo "Installing dependencies"
    - npm install --legacy-peer-deps
    - npm run build;
    - echo "Build completed. React build is already placed in the Node folder using Webpack."
    - echo "Zipping the built Node.js folder"
    - cd src/node
    - zip -qr ../../${PROD_DEPLOY_PACKAGE_NAME} . -x '*.git*' './config/config_development.json'

    - echo "Uploading ZIP to S3"
    - aws s3 cp ../../${PROD_DEPLOY_PACKAGE_NAME} s3://${S3_BUCKET_NAME}/

  artifacts:
    paths:
      - ${PROD_DEPLOY_PACKAGE_NAME}

deploy:
  stage: deploy
  before_script:
    - echo "Setting up SSH configuration"
    - mkdir -p ~/.ssh
    - echo "${EC2_SSH_PRIVATE_KEY}" > ~/.ssh/private_key.pem
    - chmod 600 ~/.ssh/private_key.pem
    - ssh-keyscan -H ${PROD_EC2_HOST} >> ~/.ssh/known_hosts

  script:
    - echo "Installing required tools"
    - apt-get update && apt-get install -y curl unzip zip
    - |
      if ! command -v aws &> /dev/null; then
        echo "AWS CLI not found. Installing AWS CLI..."
        curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
        unzip awscliv2.zip
        ./aws/install || { echo "AWS CLI installation failed"; exit 1; }
      else
        echo "AWS CLI is already installed."
      fi

    - echo "Configuring AWS CLI"
    - aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID
    - aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY
    - aws configure set region ${AWS_DEFAULT_REGION}

    - echo "Configuring deployment target"
    - export EC2_HOST=$PROD_EC2_HOST

    - echo "Deploying to EC2"
    - |
      ssh -i ~/.ssh/private_key.pem -o StrictHostKeyChecking=no ${EC2_USER}@${EC2_HOST} <<EOF
      echo "SSH connection established"
      cd /var/www/html || { echo "Failed to cd to /var/www/html"; exit 1; }

      # Set permissions for the directory if needed
      sudo chmod 775 /var/www/html
      sudo chown ec2-user:ec2-user /var/www/html

      # Explicitly pass AWS credentials to the remote instance
      export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID
      export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY
      export AWS_DEFAULT_REGION=${AWS_DEFAULT_REGION}

      echo "Downloading ZIP from S3"
      aws s3 cp s3://symphony-cicd/${PROD_DEPLOY_PACKAGE_NAME} ./ || { echo "Failed to download ZIP from S3"; exit 1; }

      echo "Extracting ZIP file"
      unzip -o ${PROD_DEPLOY_PACKAGE_NAME} || { echo "Failed to unzip ${PROD_DEPLOY_PACKAGE_NAME}"; exit 1; }

      echo "NODE_ENV=production" > .env
      echo ".env file created:"
      cat .env

      echo "Cleaning up ZIP file"
      rm -f ${PROD_DEPLOY_PACKAGE_NAME}

      echo "Installing Node.js modules"
      npm install || { echo "Failed to install Node modules"; exit 1; }

      echo "Restarting application with PM2"
      pm2 restart 0 || { echo "Failed to restart PM2"; exit 1; }

      echo "Deployment completed successfully"
      exit
      EOF
