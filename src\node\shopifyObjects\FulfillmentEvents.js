const axios = require('axios')

module.exports = class FulfillmentEvent {
    constructor(shop,token,orderId,fulfillmentId){
        this.options = {
            headers:{
                'X-Shopify-Access-Token':token,
                'Content-Type':'application/json'
            }
        }
        this.shop = shop
        this.path = `https://${shop}/admin/api/2023-07/orders/${orderId}/fulfillments/${fulfillmentId}/events.json`
    }
    create = (data) => {
        return new Promise(async(resolve,reject) => {
            let bodyObj = {
                "event":data
            }
            await axios.post(this.path,bodyObj,this.options)
            .then(res => {
                resolve({data:JSON.parse(JSON.stringify(res.data.fulfillment_event))})}
                )
            .catch(err => {
                console.log("error==>",err)
                resolve({status: err.response.status})
            })
            
        })
    }
}
