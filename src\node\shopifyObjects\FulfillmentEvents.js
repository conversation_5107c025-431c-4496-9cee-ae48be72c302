const axios = require('axios')

module.exports = class FulfillmentEvent {
    constructor(shop, token) {
        this.options = {
            headers: {
                'X-Shopify-Access-Token': token,
                'Content-Type': 'application/json'
            }
        }
        this.shop = shop
        this.path = `https://${shop}/admin/api/2025-07/graphql.json`
    }

    create = async (data) => {
        try {
            const mutation = `
                mutation fulfillmentEventCreate($fulfillmentEvent: FulfillmentEventInput!) {
                  fulfillmentEventCreate(fulfillmentEvent: $fulfillmentEvent) {
                    fulfillmentEvent {
                      id
                      status
                      message
                      happenedAt
                    }
                    userErrors {
                      field
                      message
                    }
                  }
                }
            `;
            // Map REST-style keys to GraphQL input keys
            const input = {
                fulfillmentEvent: {
                    fulfillmentId: `gid://shopify/Fulfillment/${data.fulfillmentId}`,
                    status: data.status,
                }
            };
            const res = await axios.post(
                this.path,
                { query: mutation, variables: { input } },
                this.options
            );
            const result = res.data?.data?.fulfillmentEventCreate;
            if (result?.userErrors && result.userErrors.length > 0) {
                return { errors: result.userErrors };
            }
            // Return in a compatible format
            return { data: result.fulfillmentEvent };
        } catch (err) {
            return { status: err?.response?.status || err?.message };
        }
    }
}
