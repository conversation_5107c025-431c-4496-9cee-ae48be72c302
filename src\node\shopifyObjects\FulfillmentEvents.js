const axios = require('axios')

module.exports = class FulfillmentEvent {
    constructor(shop, token) {
        this.options = {
            headers: {
                'X-Shopify-Access-Token': token,
                'Content-Type': 'application/json'
            }
        }
        this.shop = shop
        this.path = `https://${shop}/admin/api/2025-07/graphql.json`
    }

    create = async (data) => {
        try {
            const mutation = `
                mutation fulfillmentEventCreate($fulfillmentEvent: FulfillmentEventInput!) {
                  fulfillmentEventCreate(fulfillmentEvent: $fulfillmentEvent) {
                    fulfillmentEvent {
                      id
                      status
                      message
                      happenedAt
                    }
                    userErrors {
                      field
                      message
                    }
                  }
                }
            `;
            // Map REST-style keys to GraphQL input keys
            const fulfillmentEvent = {
                fulfillmentId: `gid://shopify/Fulfillment/${data.fulfillmentId}`,
                status: data.status.toUpperCase(),
            };
            const res = await axios.post(
                this.path,
                { query: mutation, variables: { fulfillmentEvent } },
                this.options
            );

            // Check for GraphQL errors first
            if (res.data?.errors) {
                console.error('GraphQL errors in fulfillment event creation:', res.data.errors);
                return { status: false, errors: res.data.errors };
            }

            const result = res.data?.data?.fulfillmentEventCreate;
            if (!result) {
                console.error('No fulfillmentEventCreate result in response');
                return { status: false, error: 'No fulfillmentEventCreate result' };
            }

            if (result?.userErrors && result.userErrors.length > 0) {
                console.error('User errors in fulfillment event creation:', result.userErrors);
                return { errors: result.userErrors };
            }

            // Return in a compatible format
            if (!result.fulfillmentEvent) {
                console.error('No fulfillmentEvent in result:', result);
                return { status: false, error: 'No fulfillmentEvent in result' };
            }

            return { data: result.fulfillmentEvent };
        } catch (err) {
            console.log('FulfillmentEvent===>', err)
            console.error('Error details:', {
                message: err.message,
                response: err.response?.data,
                status: err.response?.status
            });
            return { status: err?.response?.status || err?.message };
        }
    }
}
