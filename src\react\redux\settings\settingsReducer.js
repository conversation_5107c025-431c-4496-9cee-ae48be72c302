import { settingsTypes } from "./settingsTypes";

const initialState = {
    error: false,
    cloudloading: true,
    coverloading: true,
    cloudSkuListData: null,
    coverSkuListData: null,
    coverSkuExport: null,
    cloudSkuExport: null,
    skuModalLoader: false
}
export default function settingsReducer(state = initialState, action) {
    switch (action.type) {
        case settingsTypes.GET_CLOUD_SKU_LIST_REQUEST:
            return { ...state, cloudloading: true };

        case settingsTypes.GET_CLOUD_SKU_LIST_SUCCESS:
            return { ...state, cloudSkuListData: action.payload, cloudloading: false };

        case settingsTypes.GET_COVER_SKU_LIST_REQUEST:
            return { ...state, coverloading: true };

        case settingsTypes.GET_COVER_SKU_LIST_SUCCESS:
            return { ...state, coverSkuListData: action.payload, coverloading: false };

        case settingsTypes.SAVE_CLOUD_SKU_FILE_REQUEST:
            return { ...state, error: false };

        case settingsTypes.SAVE_CLOUD_SKU_FILE_SUCCESS:
            return { ...state, error: false };

        case settingsTypes.SAVE_CLOUD_SKU_FILE_ERROR:
            return { ...state, error: true };

        case settingsTypes.SAVE_COVER_SKU_FILE_REQUEST:
            return { ...state, error: false };

        case settingsTypes.SAVE_COVER_SKU_FILE_SUCCESS:
            return { ...state, error: false };

        case settingsTypes.SAVE_COVER_SKU_FILE_ERROR:
            return { ...state, error: true };

        case settingsTypes.COVER_SKU_EXPORT_REQUEST:
            return { ...state, error: false };

        case settingsTypes.COVER_SKU_EXPORT_SUCCESS:
            return { ...state, error: false, coverSkuExport: action.payload };

        case settingsTypes.COVER_SKU_EXPORT_ERROR:
            return { ...state, error: true };

        case settingsTypes.CLOUD_SKU_EXPORT_REQUEST:
            return { ...state, error: false };

        case settingsTypes.CLOUD_SKU_EXPORT_SUCCESS:
            return { ...state, error: false, cloudSkuExport: action.payload };

        case settingsTypes.CLOUD_SKU_EXPORT_ERROR:
            return { ...state, error: true };

        case settingsTypes.DELETE_COVER_SKU_REQUEST:
            return { ...state, skuModalLoader: true, error: false };

        case settingsTypes.DELETE_COVER_SKU_SUCCESS:
            return { ...state, skuModalLoader: false, error: false };

        case settingsTypes.DELETE_COVER_SKU_ERROR:
            return { ...state, skuModalLoader: false, error: true };

        case settingsTypes.DELETE_CLOUD_SKU_REQUEST:
            return { ...state, skuModalLoader: true, error: false };

        case settingsTypes.DELETE_CLOUD_SKU_SUCCESS:
            return { ...state, skuModalLoader: false, error: false };

        case settingsTypes.DELETE_CLOUD_SKU_ERROR:
            return { ...state, skuModalLoader: false, error: true };

        default:
            return state;
    }
}