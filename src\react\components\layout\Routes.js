import { C<PERSON><PERSON>outer, RoutePropagator, Provider as App<PERSON>ridgeProvider } from '@shopify/app-bridge-react';
import React, { Component } from "react";
import { Route, Switch, withRouter } from "react-router-dom";
import GSTOrders from "../../pages/gstOrders/gstOrders";
import CFAListDetail from "../../pages/cfaDetails/cfaList";
import OrderList from "../../pages/orderList/orderLst";
import Settings from "../../pages/settings/settings";
import ReturnOrders from '../../pages/returnOrders/returnOrders';
import FailedOrders from '../../pages/failedOrders/failedOrders';
import ManageCfa from '../../pages/manageCfa/manageCfaList';
import WarrantyOrders from '../../pages/warrantyOrders/warrantyOrders';

class Routes extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }
  render() {
    const { history, location } = this.props;
    return (
      <>
        {/* <ClientRouter history={history} />
      <RoutePropagator location={location} /> */}
        <Switch>
          <Route path="/app/orders" component={OrderList}></Route>
          <Route path="/app/gst_orders" component={GSTOrders}></Route>
          <Route path="/app/returns" component={ReturnOrders}></Route>
          <Route path="/app/failed" component={FailedOrders}></Route>
          <Route path="/app/warranty" component={WarrantyOrders}></Route>
          <Route path="/app/cfaDetails" component={CFAListDetail}></Route>
          <Route path="/app/settings" component={Settings}></Route>
          <Route path="/app/manage_cfa" component={ManageCfa}></Route>
        </Switch>
      </>
    );
  }
}

export default withRouter(Routes);
