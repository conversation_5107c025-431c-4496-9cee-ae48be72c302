import { Card, DataTable, Page, SkeletonBodyText, Stack, TextStyle, Layout, Button, Heading, Modal, Link, DropZone, Caption, FormLayout } from "@shopify/polaris";
import React, { Component } from "react";
import { connect } from "react-redux";
import Search from "./search-icon.png";
import { exportAllCfaRequest, getManageCfaListRequest, saveManageCfaFileRequest } from "../../redux/cfa/cfaActions";
import { isEmpty } from "../../helpers/appHelpers";

export class ManageCfa extends Component {
    constructor(props) {
        super(props);
        this.state = {
            importModalActive: "",
            file: "",
            buttonLoading: false,
            pageLoading: false
        }
    }

    componentDidMount() {
        document.title = "CFA Stock";
        this.getAllManageCfaData();
    }

    getAllManageCfaData = () => {
        this.setState({ pageLoading: true });
        let { getManageCfaAllData } = this.props;
        getManageCfaAllData({
            callback: () => {
                this.setState({ pageLoading: false });
            },
        });
    };

    handleImportModal = (val) => {
        this.setState({ importModalActive: val, file: "", buttonLoading: false })
    }

    handleDrop = (_droppedFiles, files) => {
        this.setState({ file: files[0] });
    };

    importFile = () => {
        this.setState({ buttonLoading: true });
        let fileObj = this.state.file;
        let formData = new FormData();
        formData.append('file', fileObj);
        formData.append('importType', this.state.importModalActive);
        let { saveManageCfaFile } = this.props;
        saveManageCfaFile({
            formData,
            callback: () => {
                if (!this.props.error) {
                    this.setState({ buttonLoading: false, file: "", importModalActive: "" },
                        () => this.getAllManageCfaData());
                } else {
                    this.setState({ buttonLoading: false });
                }
            },
        });
    }

    exportAll = () => {
        this.setState({ buttonLoading: true })
        let { exportAllCfaData } = this.props;
        exportAllCfaData({
            callback: () => {
                if (!this.props.error) {
                    let response = this.props.exportedCfaData;
                    const downloadUrl = window.URL.createObjectURL(
                        new Blob([response.data])
                    );
                    const link = document.createElement("a");
                    link.href = downloadUrl;
                    let name = "CFA LIST";
                    link.setAttribute("download", name + ".xlsx");
                    document.body.appendChild(link);
                    link.click();
                    link.remove();
                }
                this.setState({ buttonLoading: false })
            },
        });
    }

    cfaListMarkup = () => {
        const { loading, manageCfaListData } = this.props;
        let rows = [];
        if (!loading) {
            const dataObj = manageCfaListData.data.data;
            if (!isEmpty(dataObj)) {
                dataObj.forEach(function (cfa) {
                    const row = [
                        cfa.plant_code,
                        cfa.plant_name,
                        cfa.is_active == true ? 'Yes' : 'No',
                        cfa.order_limit ? cfa.order_limit : '',
                        cfa.order_limit ? cfa.total_order  : ''
                    ];
                    rows.push(row);
                }, this);

            } else {
                return (<div style={{ padding: "40px" }}>
                    <Stack vertical alignment="center">
                        <img width={"50px"} src={Search} />
                        <TextStyle>No CFA Data Found</TextStyle>
                    </Stack>
                </div>);
            }
        } else {
            for (let i = 0; i <= 5; i++) {
                rows.push([
                    <SkeletonBodyText lines={1} />,
                    <SkeletonBodyText lines={1} />,
                    <SkeletonBodyText lines={1} />,
                    <SkeletonBodyText lines={1} />,
                    <SkeletonBodyText lines={1} />,
                ]);
            }
        }

        return (
            <DataTable verticalAlign="middle"
                columnContentTypes={["text", "text", "text", "text", "text"]}
                headings={[
                    <b>Plan Code</b>,
                    <b>Plan Name</b>,
                    <b>Active</b>,
                    <b>Order Limit</b>,
                    <b>Total Orders</b>
                ]}
                rows={rows}
            />
        );
    };
    render() {
        let { importModalActive } = this.state
        let rows = [];
        if (true) {
            for (let i = 0; i <= 2; i++) {
                rows.push([
                    <SkeletonBodyText lines={1} />,
                    <SkeletonBodyText lines={1} />,
                    <SkeletonBodyText lines={1} />
                ]);
            }
        }

        return (
            <Page fullWidth title="CFA List"
                primaryAction={
                    <Stack>
                        <Button primary onClick={() => this.handleImportModal("cfa")}>Import CFA</Button>
                        <Button onClick={() => this.handleImportModal("sku")}>Import Block SKU</Button>
                        <Button loading={this.state.buttonLoading} primary onClick={this.exportAll}>Export All</Button>
                    </Stack>
                }
                secondaryActions={{ content: "", onAction: () => { } }}
            >
                <Layout sectioned>
                    <Card>
                        {this.cfaListMarkup()}
                    </Card>
                </Layout>
                <Modal
                    title={importModalActive == "cfa" ? "Import CFA" : "Import Block SKU"}
                    open={importModalActive != ""}
                    onClose={() => this.handleImportModal("")}
                    primaryAction={{
                        content: 'Save',
                        onAction: this.importFile,
                        loading: this.state.buttonLoading,
                        disabled: this.state.file == ""
                    }}
                >
                    <Modal.Section>
                        <FormLayout>
                            {importModalActive !== "cfa" ? <Link external={false} url="https://slipinvoice.s3.ap-south-1.amazonaws.com/template/sku_block.xlsx" >Click here to download sample</Link> : ""}
                            <DropZone
                                allowMultiple={false}
                                error={true}
                                accept=" .xlsx, .csv"
                                type="file"
                                onDrop={this.handleDrop}
                            >
                                <Stack vertical>
                                    {this.state.file != "" ? (
                                        <Stack alignment="center">
                                            {this.state.file.name} <Caption>{this.state.file.size} bytes</Caption>
                                        </Stack>
                                    ) : ""}
                                    <DropZone.FileUpload />
                                </Stack>
                            </DropZone>
                        </FormLayout>
                    </Modal.Section>
                </Modal>
            </Page>)
    }
}

const mapStateToProps = (state) => ({
    manageCfaListData: state.cfa.data,
    loading: state.cfa.loading,
    exportedCfaData: state.cfa.cfaData,
});

const mapDispatchToProps = (dispatch) => ({
    getManageCfaAllData: (cfaData) => dispatch(getManageCfaListRequest(cfaData)),
    saveManageCfaFile: (cfaData) => dispatch(saveManageCfaFileRequest(cfaData)),
    exportAllCfaData: (cfaData) => dispatch(exportAllCfaRequest(cfaData)),
});

export default connect(mapStateToProps, mapDispatchToProps)(ManageCfa);