let mysql = require('mysql');
const dotenv = require('dotenv');
dotenv.config();
exports.BASE_URL = process.env.NODE_ENV == 'production' ? "https://symphonyapp.lucentinnovation.com" : process.env.NODE_ENV == 'local' ? "http://localhost:8000" : "https://appsymphony.lucentinnovation.com"
exports.WARRANTY_PRODUCTS = process.env.NODE_ENV == 'production' ? ["EXT-WARR", "EXT-WARR-MV"] : ["extended-warranty", "extended-warranty-Movicool"]

if (process.env.NODE_ENV == 'local') {
    exports.con = mysql.createConnection({
        host: "localhost",
        user: "root",
        password: "",
        database: "symphony-staging"
    });
} else {
    exports.con = mysql.createConnection({
        host: "symphony-prod-db.cnsig8a8aa1z.ap-south-1.rds.amazonaws.com",
        user: "sapsymphony",
        password: "Oaj0xtGdOS05ZZ7sPRZg",
        database: process.env.NODE_ENV == 'production' ? "symphony" : "symphony-staging"
    });
}

this.con.connect(function (err) { if (err) { return } });