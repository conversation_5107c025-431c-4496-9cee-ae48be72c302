module.exports = app => {
  const settingController = require("../controllers/settings-controller.js");
  const router = require("express").Router();
  const upload = require("../helper/uploadFile")

  router.post("/master/upsert", settingController.upsertCoverMasterSku)
  router.get("/master/get", settingController.getCoverSku)
  router.get("/child/get", settingController.getChildSku)
  router.delete("/master/delete", settingController.deleteCoverMasterSku)
  router.post("/child/upsert", settingController.upsertChildSku)
  router.delete("/child/delete", settingController.deleteChildSku)
  router.post("/master/import", upload.single('file'), settingController.importCoverMasterFile)
  router.get("/master/export", settingController.exportCoverMasterFile)
  router.post("/child/import", upload.single('file'), settingController.importChildSkuFile)
  router.get("/child/export", settingController.exportChildSkuFile)
  router.post("/update/creditnote/import", upload.single('file'), settingController.updateCreditNote)
  router.post("/update/sap/import", upload.single('file'), settingController.updateSODelivery)
  //API Routes
  app.use('/api/settings', router);
};