const Sequelize = require("sequelize");
module.exports = (sequelize) => {
    return sequelize.define("cover_master_sku", {
        id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            unique: true
        },
        master_sku: {
            type: Sequelize.STRING
        },
        child_sku: {
            type: Sequelize.STRING,
        },
    },
        {
            indexes: [
                {
                    unique: true,
                    fields: ['master_sku', 'child_sku']
                }
            ]
        });
};