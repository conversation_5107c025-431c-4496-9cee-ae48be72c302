import React, { Component } from "react";
import {
  Card,
  Button,
  Popover,
  Scrollable,
  FormLayout,
  DatePicker,
  TextField
} from "@shopify/polaris";
import {
  CalendarMajor,
} from "@shopify/polaris-icons";
import PropTypes from 'prop-types';
import moment from "moment";
const momentDate = moment();


class DateRangePicker extends Component {
  constructor(props) {
    super(props);
    this.state = {
      month: momentDate.month(),
      year: momentDate.year(),
      selectedDate: {
        start: moment().subtract(30, "days").toDate(),
        end: moment().toDate(),
      },
      selectedDateText: {
        start: moment().subtract(30, "days").format("YYYY-MM-DD"),
        end: moment().format("YYYY-MM-DD"),
      },
      popactive: false,
      startDate: moment().subtract(30, "days").format("YYYY-MM-DD"),
      endDate: moment().format("YYYY-MM-DD"),
    };
  }  
  

  togglePopover = () => {
    this.setState({
      popactive: !this.state.popactive,
    });
  }

  handleMonthChange = (month, year) => {
    this.setState({ month: month, year: year });
  }

  handleChangeDateInput = (input, value) => {
    var selectedDateText = this.state.selectedDateText;
    var selectedDate = this.state.selectedDate;

    if (input === "start") {
      selectedDateText.start = value;

      if (moment(selectedDateText.start).isValid()) {
        selectedDate.start = moment(selectedDateText.start).toDate();
      }
    }
    if (input === "end") {
      if (moment(value).isValid()) {
        var d1 = moment(value);
        var d2 = moment(moment());
        var dff = d1.diff(d2, "hours");

        if (dff <= 0) {
          selectedDate.end = moment(value).toDate();
        }
      }
      selectedDateText.end = value;
    }

    this.setState({
      selectedDateText: selectedDateText,
      selectedDate: selectedDate,
    });
  }
  handleChangeDate = (selectedDate) => {
    var selectedDateText = this.state.selectedDateText;

    if (moment(selectedDate.start).isValid()) {
      selectedDateText.start = moment(selectedDate.start).format("YYYY-MM-DD");
    }
    if (moment(selectedDate.end).isValid()) {
      selectedDateText.end = moment(selectedDate.end).format("YYYY-MM-DD");
    }

    this.setState({
      selectedDate: selectedDate,
      selectedDateText: selectedDateText,
    });
  }


  handleApplyFilter = () => {
    this.togglePopover();
    this.setState({ ispageLoad: false });
    const { start, end } = this.state.selectedDate;
    const startDate = moment(start).format("YYYY-MM-DD");
    const endDate = moment(end).format("YYYY-MM-DD");
    const dateRequest = {
      startDate: startDate,
      endDate: endDate,
      selectedDate:this.state.selectedDate,
      selectedDateText:this.state.selectedDateText
    };
    this.setState({
      startDate: startDate,
      endDate: endDate,
    });
    this.props.handleApplyFilter(dateRequest);
  }



  render() {
    const { month, year, selectedDate } = this.state;

    return (
      <div>
        <Button
            primary={this.props.primary}
            onClick={this.togglePopover}
            icon={CalendarMajor}
          >
            {moment(this.state.selectedDate.start).format(
              "DDMMMYYYY"
            ) == moment(this.state.selectedDate.end).format("DDMMMYYYY")
              ? moment(this.state.selectedDate.start).format(
                  "DD MMM YYYY"
                )
              : moment(this.state.selectedDate.start).format(
                  "DD MMM YYYY"
                ) +
                " - " +
                moment(this.state.selectedDate.end).format(
                  "DD MMM YYYY"
                )}
          </Button>
          <div>
                  <Popover
                    fluidContent={true}
                    active={this.state.popactive}
                    activator={<div></div>}
                    onClose={this.togglePopover}
                    preferredAlignment="right"
                    icon={CalendarMajor}
                    footerContent
                  >
                    <Card>
                      <Scrollable
                        shadow
                        style={{ height: "300px", width: "500px" }}
                      >
                        <Card.Section>
                          <FormLayout>
                            <FormLayout.Group condensed>
                              <TextField
                                type="text"
                                value={this.state.selectedDateText.start}
                                autoComplete={false}
                                label="Starting"
                                onChange ={() => {this.handleChangeDateInput("start");}}
                              />
                              <TextField
                                type="text"
                                value={this.state.selectedDateText.end}
                                autoComplete={false}
                                label="Ending"
                                onChange ={() => {this.handleChangeDateInput("end");}}
                              />
                            </FormLayout.Group>
                            <FormLayout.Group>
                              <DatePicker
                                month={month}
                                year={year}
                                disableDatesAfter={momentDate.toDate()}
                                onChange={this.handleChangeDate}
                                onMonthChange={this.handleMonthChange}
                                selected={selectedDate}
                                allowRange={true}
                              />
                            </FormLayout.Group>
                          </FormLayout>
                        </Card.Section>
                      </Scrollable>
                      <Card.Section>
                        <div style={{ textAlign: "right" }}>
                          <Button onClick={this.togglePopover}>
                            Cancel
                          </Button>
                          &nbsp;&nbsp;
                          <Button
                            primary
                            onClick={this.handleApplyFilter}
                          >
                            Apply
                          </Button>
                        </div>
                      </Card.Section>
                    </Card>
                  </Popover>
                </div>
      </div>
    );
  }
}

DateRangePicker.propTypes = {
  preferredAlignment: PropTypes.string,
};

export default DateRangePicker;
