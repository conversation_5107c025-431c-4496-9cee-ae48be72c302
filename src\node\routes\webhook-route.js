const express = require("express");
const router = express.Router();
const delhiveryController = require("../controllers/delhivery-controller");
const orderSyncController = require("../controllers/ordersync-controller");
const sapController = require("../controllers/sap_controller");
const refundController = require("../controllers/refund-controller");

const SHOP_SYMPHONY_HEADER = 'shop';
const delhiveryWebhookValidate = (req, res, next) => {
  const headerValue = req.headers[SHOP_SYMPHONY_HEADER];
  const expectedValue = 'symphony.myshopify.com';

  if (headerValue !== expectedValue) {
    return res.status(401).json({ status: false, error: 'Unauthorized' });
  }
  next();
};

module.exports = (app) => {
    app.use('/delhivery-webhook', delhiveryWebhookValidate);
    
    router.post('/call-cloud-api', async (req, res) => {
        try {
            res.json({ status: true });
            const orderName = req.body.orderName;
            let orderDataName = await orderSyncController.getorderSplitDataByName(orderName)
            let orderResponse = JSON.parse(JSON.stringify(orderDataName.data))
            const orderData = await orderSyncController.callCloudApi(orderResponse);
            //res.json(orderData);
        } catch (error) {
            console.log('API call-cloud-api Error--->',error.message)
            //res.status(500).json({ status: false, error: error.message });
        }
    });

    router.post('/returnsaleordersap', async (req, res) => {
        try {
            res.json({ status: true, message: 'Sale order returned successfully.' });
            const orderData = req.body.orderData;
            await sapController.returnSaleOrderSap(orderData);
            await refundController.checkingForRefund(orderData);
        } catch (error) {
            console.log('API returnsaleordersap--->',error.message)
            //res.status(500).json({ status: false, error: error.message });
        }
    });

    router.post('/cancelorder', async (req, res) => {
        try {
            res.json({ status: true, message: 'Order canceled successfully.' });
            const orderData = req.body.orderData;
            await delhiveryController.cancelOrder(orderData.order_name, true);
            await refundController.checkingForRefund(orderData);
        } catch (error) {
            console.log('API cancelorder--->',error.message)
           // res.status(500).json({ status: false, error: error.message });
        }
    });

    router.post('/updatefulfillmentevent', async (req, res) => {
        try {
            res.json({ status: true, message: 'Fulfillment event updated successfully.' });
            const paramObj = req.body.paramObj;
            await delhiveryController.updateFulfillmentEvent(paramObj);
        } catch (error) {
            console.log('API updatefulfillmentevent--->',error.message)
            //res.status(500).json({ status: false, error: error.message });
        }
    });

    app.use('/delhivery-webhook', router);
};
