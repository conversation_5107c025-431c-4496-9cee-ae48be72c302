{"shopify": {"apiKey": "63e46cc1b6f8fafb90fa09dcf7aa879d", "secreteKey": "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", "redirectUrl": "http://localhost:5000/auth/callback", "scopes": "read_products,write_products,read_orders,write_orders,read_fulfillments,write_fulfillments,write_assigned_fulfillment_orders,write_merchant_managed_fulfillment_orders,write_third_party_fulfillment_orders,write_draft_orders,read_draft_orders,read_locations,read_markets_home,read_inventory", "appUrl": "http://localhost:5000", "appName": "", "webhookUrl": "https://c95f-110-227-210-68.ngrok-free.app", "encryptionkey": "2Z3obqOFXl2qT94S8mVgaJxT7jgE2L7r"}, "database": {"host": "localhost", "user": "root", "password": "root@123", "db": "symphony-staging", "dialect": "mysql", "pool": {"max": 5, "min": 0, "acquire": 30000, "idle": 10000}}, "delhivery": {"post_url": "https://staging-express.delhivery.com/api/cmu/create.json", "get_url": "https://staging-express.delhivery.com/api/v1/packages/json/", "cancel_url": "https://staging-express.delhivery.com/api/p/edit", "waybill_url": "https://staging-express.delhivery.com/waybill/api/bulk/json/?token=8341fd075e11cdc7d82bdf656774e62db3a8794f&count=2", "token": "8341fd075e11cdc7d82bdf656774e62db3a8794f", "CFA_NAME": "SYMPHONY SURFACE", "CFA_CLIENT": "SYMPHONYSURFACE-B2C", "billing_url": "https://staging-express.delhivery.com/api/p/packing_slip", "track_order_url": "https://staging-express.delhivery.com/api/v1/packages/json/?token=8341fd075e11cdc7d82bdf656774e62db3a8794f"}, "sap": {"SO_URL": "https://symerpqa.symphony.com:8001/sap/bc/zspysoapi", "INV_URL": "https://symerpqa.symphony.com:8001/sap/bc/zspystkapi", "DEL_URL": "https://symerpqa.symphony.com:8001/sap/bc/zspydlvyapi", "CUS_URL": "https://symerpqa.symphony.com:8001/sap/bc/zspycustapi", "CLR_URL": "http://symerpqa.symphony.com:8000/sap/bc/zb2c_clr_doc", "userId": "dev06", "pass": "Advait@123456"}, "crm": {"log_url": "http://***************:7080/ServitiumCRM_IS_SYMPHONY_CUST/IS/submitData", "warranty_url": "http://***************:7080/ServitiumCRM_IS_DL_SYMPHONY/IS/submitData", "track_url": "http://***************:7080/ServitiumCRM_IS_DL_SYMPHONY/IS/getCallList", "getCallList": "https://symphonydlrapp.servitiumcrm.com/ServitiumCRM_IS_DL_SYMPHONY/IS/getCallList", "distributorCode": 0, "dealerCode": 0, "customerType": 1, "brandCode": 1, "productCategoryCode": 1, "productCode": 1, "serviceTypeCode": 1, "cityDesc": "0", "loginType": "0", "companyCode": "86007", "userId": 9999, "userLogId": 4874, "warrantyUserId": 9998, "version": "9", "token": "OIYSZZ30072021SHOPTIMIZELLQKZT5U", "warrantyToken": "IVRSZZ6Z2ZWYEOSYMPHONYYNLLQKZIVR"}, "gst": {"url": "https://passthroughapi.cygnetgsp.in/addon-commonapi/v1.1/search", "client_id": "W3lA8VKZxT9UKQetpPN+LzZ7zhba74Mfyd4QizL+ing=", "client_secret": "Z42u/dTLZ1odH5QgosoexHVSH8gqVhRVZnX66Hj/i3zWyvGyG7EqIS1SNzldhrYIHpA+o7Mhbvmzlq1TsPWelQ==", "client_secret_encode": "787D1LfdLpsZTnSyOpG9lBIOxXGnqJturqq/zFGWFlwe58UpE0stkE2+m4E8/RpWzqn0HuQbB0HYXMyIOSgEavKyTTfIHsreRiiheRM4Ru3sS2YtSDM5Pxr9QCkTnTjyi8qQpxIBvz38Xe1adyeGx30jnHNiQE4YRq+pjGTfLUXC9LjZTgMbKIQMk6ZnE6tKyUMJbuFORLJDN6nz/64py7ppnBRqq8u/iYdB3HQELeyIqvjsVOgO5zptvm4ohQYo", "client_id_encode": "rn+ZypqRDVD5WwLOVAx7at96edI+7g5kSjoRLrgKZyIslOluDWDkpeqY4qjf4Ucj88D/vlKmmwiY1FI3f6YweEROQsH1C7nablSCb5FSskhSsw3bRMnyVm8YYofmAlSI", "ip_usr": "***********", "txn": "", "username": "ashwin.prajapa<PERSON>@symphonylimited.com", "password": "Admin@123", "token": ""}, "msg": {"SUCCESS": "Success", "UPDATE": "Updated Successfully", "CREATE": "Created Successfully", "FAILED": "Failed", "ERROR": "Something went wrong", "PARAMETER": "Required parameter not found", "NO_DATA": "Data not found", "DELETE": "Deleted Successfully", "REFUND": "Refund Successfull", "UPLOAD": "File Uploaded Successfully", "CANCEL": "Canceled Successfully"}, "status": {"SUCCESS": 200, "FAILED": 500, "ERROR": 500}, "otp": {"url": "http://ip.shreesms.net/smsserver/SMS10N.aspx", "user_id": "symphony", "password": "12345", "entity_id": "1301159108961563511", "template_id": "1307161578314629650", "gsm": "SYMPNY", "otp_secret": "FaRjfZuO0k4"}, "aws": {"accessKey": "****************************************************************", "secretKey": "1c1b49b37ffe3f5c819c655593d52eac60679a7d53fb8dd7ea66a3b1545369020c0dcb3d27adcf66d83ca257c3dcc937", "bucketName": "symphony-object"}, "fulfillment_service": {"name": "Symphony-local-test", "callback_url": "https://e631-110-227-210-68.ngrok-free.app/", "inventory_management": true, "tracking_support": true, "requires_shipping_method": false, "fulfillment_orders_opt_in": true, "format": "json"}, "jwt": {"token": "lucent_innovation"}, "razorpay": {"keyId": "rzp_test_pzFVLfyAxldb9m", "keySecret": "3vu7ehSdToxLEvJCve6ai1OT", "url": "https://api.razorpay.com/v1/"}, "webhookAuth": {"userName": "symphony", "password": "gmyqmgjz8s415zqs"}, "ftp": {"host": "***************", "user": "TPL", "password": "g2xJeM*m$g"}, "googleDistanceMatrixApi": {"url": "https://maps.googleapis.com/maps/api/distancematrix/json", "apiKey": "AIzaSyBQu1eTCLo9fdinnMg1OmYXuIqmvD0Yg_M"}, "email": {"username": "<EMAIL>", "password": "pgyllkclnwsvjjyv", "host": "************", "port": 587, "from": "<EMAIL>", "to": "<EMAIL>", "exportto": "<EMAIL>", "pendingPickupMail": "<EMAIL>,<EMAIL>,<EMAIL>,<PERSON><PERSON><PERSON>.<EMAIL>,<EMAIL>", "pendingDeliveryMail": "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>", "escalation_to": "<EMAIL>", "escalation_from": "<EMAIL>"}, "clickpost": {"courier_partner": "289", "account_code": "Symphony-Delhivery-B2C", "url": "https://www.clickpost.in/api/v3/tracking/awb-register/", "key": "9522d924-297d-4d23-9efc-89096d95524a"}, "extendedwarranty": {"url": "https://slerp.symphonycooler.in/api/b2c-warranty-purchase", "authkey": "FzkSK3W2FW71vKUhhfG2Naf8RYw7", "ew_product": {"extended-warranty": "********"}}, "limechat": {"username": "symphony@linechat", "password": "symphony@linechat"}, "zippee": {"createOrder": "hhttps://api.loginextsolutions.com/ShipmentApp/mile/v2/create", "cancelOrder": "https://api.loginextsolutions.com/ShipmentApp/mile/v2/cancel", "authHeader": "a1e13d07-4f01-4421-ba9d-cf8533ba07e2", "branch": "DEL_malviya-nagar"}}