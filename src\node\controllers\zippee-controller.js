const dbConnection = require("../models");
const fs = require('fs').promises;

const fss = require('fs');
const xlsx = require('xlsx');
const CONFIG = require('../config/config_' + [process.env.NODE_ENV || 'local'] + '.json');
const { Op } = require('sequelize');
const delhiveryController = require('../controllers/delhivery-controller');
const zippeeClient = require("../helper/zippeeClient");
const clickpostController = require('./clickpost-controller');
const orderHistoryHelper = require('../helper/orderHistory');
const JsBarcode = require('jsbarcode');
const pdf = require("pdf-creator-node");
const path = require('path');
const { createCanvas } = require('canvas');
const orderSyncController = require("./ordersync-controller");
const s3 = require("../helper/awsClient");

exports.checkZippeeOrder = async (sku, pincode, plantCode) => {
    try {
        if (!sku || !pincode) return false;

        //stock check helper function
        const checkStockAvailability = async (cfaCode) => {
            const stock = await dbConnection.cfaStockMapping.findOne({ where: { sku, pincode_group: cfaCode, local_stock: { [Op.gt]: 0 } } });
            return stock ? { status: true, zippee: true, plantCode: cfaCode } : null;
        };

        // Case 1: With plantCode
        if (plantCode) {
            const zippeeMapping = await dbConnection.zippeePincodeMapping.findOne({
                where: { cfa: plantCode },
            });

            if (zippeeMapping) {
                const result = await checkStockAvailability(zippeeMapping.cfa);
                if (result) return result;
            }
            //this is return status false because this order can process due to our of stock and this cfa is zippee so return true
            return { status: false, zippee: true };
        }

        // Case 2: Without plantCode, lookup by pincode
        if (!plantCode) {
            const zippeeMapping = await dbConnection.zippeePincodeMapping.findOne({
                where: { pincode },
            });

            if (zippeeMapping) {
                const result = await checkStockAvailability(zippeeMapping.cfa);
                if (result) return result;
            }
        }

        // Fallback: Check non-zippee CFA mapping
        const cfaMapping = await dbConnection.cfaPincodeMapping.findOne({
            where: { pincode },
        });

        if (cfaMapping) return { status: true, zippee: false };
        console.log("cfaMapping return false")
        return { status: false, zippee: false };
    } catch (error) {
        console.error('Error in checking logistics', error);
        return { status: false, zippee: false };
    }
};

exports.pushOrderOnZippee = async (item) => {
    try {
        const shopResponse = await dbConnection.shop.findOne({ where: { id: item.store_id } })
        const shipObj = {
            shopResponse: shopResponse.dataValues,
            orderId: item?.shopify_order_id,
            plantCode: item?.plant_code,
            orderData: item
        }
        await this.createOrderOnZippee(shipObj)
        return
    } catch (err) {
        console.log('err in pushing order in zippee', err)
        return
    }
}

exports.importData = async (req, res) => {
    try {
        const { type } = req.body;
        const shop = req.query.shop
        if (!shop || !type || (type !== 'sku' && type !== 'pincode')) {
            return res.status(400).send({
                status: CONFIG.status.FAILED,
                message: 'Invalid request parameters.'
            });
        }

        let userData = await dbConnection.shop.findOne({ where: { myshopify_domain: shop } })
        if (!userData) {
            return res.status(400).send({
                status: CONFIG.status.FAILED,
                message: 'Invalid shop name.'
            });
        }
        const file = xlsx.readFile(req.file.path);
        const sheetName = file.SheetNames[0];
        const sheetData = xlsx.utils.sheet_to_json(file.Sheets[sheetName], {
            header: type === 'sku' ? ['sku', 'cfa'] : ['pincode'],
            skipHeader: true
        });

        sheetData.splice(0, 1);

        if (type === 'sku') {
            const cleanData = sheetData
                .filter(row => row.sku && row.cfa)
                .map(row => ({
                    sku: row.sku.toString().trim(),
                    cfa: row.cfa.toString().trim(),
                    store_id: userData.id
                }));

            // Deduplicate the data from file (sku + cfa combo)
            const uniqueMap = new Map();
            cleanData.forEach(item => {
                const key = `${item.sku}_${item.cfa}`;
                if (!uniqueMap.has(key)) {
                    uniqueMap.set(key, item);
                }
            });
            const uniqueData = Array.from(uniqueMap.values());

            //Check for existing entries in the database
            const existingRecords = await dbConnection.zippeeSkuMapping.findAll({
                where: {
                    [Op.or]: uniqueData.map(item => ({
                        sku: item.sku,
                        cfa: item.cfa
                    }))
                },
                attributes: ['sku', 'cfa']
            });

            //Create a Set of existing keys for easy comparison
            const existingSet = new Set(existingRecords.map(e => `${e.sku}_${e.cfa}`));

            //Filter new entries that aren't already in the DB
            const newEntries = uniqueData.filter(
                item => !existingSet.has(`${item.sku}_${item.cfa}`)
            );

            //Insert only new entries
            if (newEntries.length > 0) {
                await dbConnection.zippeeSkuMapping.bulkCreate(newEntries);
            }
        } else if (type === 'pincode') {
            const pincodes = sheetData
                .map(row => row.pincode)
                .filter(pincode => !!pincode);

            const uniquePincodes = [...new Set(pincodes)];
            const existing = await dbConnection.zippeePincodeMapping.findAll({
                where: {
                    pincode: {
                        [Op.in]: uniquePincodes
                    }
                }
            });

            const pin = existing.map(e => e.pincode);
            const existingSet = new Set(pin);
            const newPincodes = uniquePincodes
                .filter(pincode => !existingSet.has(pincode))
                .map(pincode => ({ pincode, store_id: userData.id }));
            if (newPincodes.length > 0) {
                await dbConnection.zippeePincodeMapping.bulkCreate(newPincodes);
            }
        }

        await fs.unlink(req.file.path);
        console.log("File deleted successfully");
        return res.status(200).send({
            status: CONFIG.status.SUCCESS,
            message: CONFIG.msg.UPLOAD
        });

    } catch (err) {
        console.error('file upload error:', err);
        return res.status(500).send({
            status: CONFIG.status.ERROR,
            message: CONFIG.msg.ERROR
        });
    }
};


exports.getZippeeObj = async (orderData) => {
    try {
        const address = orderData.orderAddresses?.[0] || {};
        const validate = str => (str || "").replace(/[^a-zA-Z0-9,\- ]/g, "");

        const zippeeObj = [
            {
                orderNo: orderData?.order_name,
                shipmentOrderTypeCd: "DELIVER",
                orderState: "FORWARD",
                shipmentOrderDt: new Date().toISOString(),
                distributionCenter: orderData?.plant_code,
                originalOrderValue: orderData.order_amount,
                packageValue: orderData.order_amount,
                paymentType: orderData?.gateway == "Cash on Delivery (COD)" ? "COD" : "prepaid",
                deliverStartTimeWindow: new Date().toISOString(),
                deliverEndTimeWindow: new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString(),
                cancellationAllowedFl: "Y",
                returnAllowedFl: "N",
                deliverAccountCode: orderData?.shopify_customer_id,
                deliverAccountName: `${validate(address?.first_name)} ${validate(address?.last_name)}`.trim(),
                deliverPhoneNumber: (address?.phone || "").replace(/\s+/g, ""),
                deliverStreetName: `${validate(address?.address1)}`.trim() || "",
                deliverLocality: `${validate(address?.address2)}`.trim() || "",
                deliverApartment: `${validate(address?.address2)}`.trim() || "",
                deliverCity: address?.city || "",
                deliverState: (address?.province_code || "").replace(/&/g, "and"),
                deliverCountry: "IND",
                deliverPinCode: address?.zip_code || "",
                returnBranch: CONFIG.zippee.branch,
                deliverBranch: CONFIG.zippee.branch,
                parentOrderNo: orderData?.shopify_order_name,
                shipmentCrateMappings: [
                    {
                        crateCd: orderData?.order_name,
                        crateName: orderData?.order_name,
                        crateAmount: "0",
                        crateType: "BOX",
                        noOfUnits: "1"
                    }
                ]
            }
        ]
        return zippeeObj;
    } catch (error) {
        console.log('Error in getZippeeObj', error)
    }
}

exports.createOrderOnZippee = async (paramObj) => {
    try {
        let { shopResponse, orderId, orderData } = paramObj
        const zippeeobj = await this.getZippeeObj(orderData);
        console.log("zippeeobj==>", JSON.stringify(zippeeobj))
        const response = await zippeeClient(CONFIG.zippee.createOrder, zippeeobj, 'post')
        console.log("response==>", response.data)
        let zipeedata = response?.data;
        let helperObj = {
            shopify_order_id: orderData.shopify_order_id,
            shopify_order_name: orderData.shopify_order_name,
            order_name: orderData.order_name,
            platform_type: "Zippee",
            request_data: JSON.stringify(zippeeobj),
            response_status_code: zipeedata?.status ? zipeedata.status : response?.status,
            // response_data: zipeedata.data[0] || zipeedata?.error[0]
            response_data: zipeedata?.data ? (typeof zipeedata?.data === 'object' ? JSON.stringify(zipeedata?.data) : zipeedata?.data) : JSON.stringify(zipeedata)
        };
        await orderHistoryHelper.insertOrderHistory(helperObj)
        const orderInfo = zipeedata?.data?.[0];
        const isSuccess = orderInfo?.referenceId;
        if (orderInfo && isSuccess) {
            await dbConnection.delhiveryLog.create({
                store_id: orderData.store_id,
                order_id: orderId,
                order_name: orderData.order_name,
                tracking_number: orderInfo.order_name,
                status: "success",
                order_id: orderData.shopify_order_id,
                line_item_id: orderData.line_item_id,
                is_return: "0",
                is_cancel: "0",
                pickup_date: new Date().toISOString(),
                delivery_date: new Date().toISOString(),
                tracking_url: orderInfo.order_name,
                waybill_number: orderInfo.orderNumber,
            });
            let clickpostObj = this.clickpostObject(zippeeobj, orderData)
            clickpostController.registerShipmentForTracking(clickpostObj)
            await dbConnection.orderItemSplit.update({ delhivery_status: "Pushed", middleware_status: "PROCESSING", shipment_status: 'manifested', waybill_number: orderInfo?.orderNumber, logistics_partner: "Zippee", zippee_reference_number: orderInfo?.referenceId }, { where: { order_name: orderData.order_name } })
            const logs = {
                line_item_id: orderData.line_item_id,
                price: orderData.order_amount,
                orderId: orderId,
                orderRef: orderData.id,
                shopResponse: shopResponse,
                storeId: orderData.store_id,
                status: "confirmed",
                waybill: orderInfo?.orderNumber,
                logistics_partner: "Zippee",
            }
            setTimeout(async () => {
                await delhiveryController.createFulfillment(logs);
            }, 15000);
        } else {
            await dbConnection.orderItemSplit.update({ delhivery_status: "Failed", middleware_status: "FAILED", failed_reason: zipeedata?.message || "Zippee order creation failed" }, { where: { order_name: orderData.order_name } });
            return;
        }

    } catch (error) {
        console.log("error in creating zippee order", error || error.message)
    }
}

exports.cancelOrderOnZippee = async (order) => {
    try {
        let orderName = order?.order_name;
        console.log(order?.zippee_reference_number, "order?.zippee_reference_number")
        const orderReferenceIds = [order?.zippee_reference_number];
        const payload = {
            orderReferenceIds: orderReferenceIds
        }
        if (!orderReferenceIds || orderReferenceIds.length === 0) {
            return { status: false, message: "Order reference ids not found" }
        }

        //cancel order on zippee
        const response = await zippeeClient(CONFIG.zippee.cancelOrder, payload, "put")
        let zippeeData = response?.data;
        let helperObj = {
            shopify_order_id: order.shopify_order_id,
            shopify_order_name: order.shopify_order_name,
            order_name: orderName,
            platform_type: "Zippee-cancel",
            request_data: payload?.orderReferenceIds.toString(),
            response_status_code: zippeeData?.status ? zippeeData.status : response?.status,
            response_data: zippeeData?.data ? (typeof zippeeData?.data === 'object' ? JSON.stringify(zippeeData?.data) : zippeeData?.data) : JSON.stringify(zippeeData)
        };
        await orderHistoryHelper.insertOrderHistory(helperObj);
        let zippeeObj = {
            store_id: order.store_id,
            order_id: order.shopify_order_id,
            waybill_number: order.waybill_number,
            response_message: "Order cancelled successfully",
            packing_slip_url: "",
            line_item_id: order.line_item_id,
            order_name: order.order_name,
            rto_delivered_date: new Date().toISOString(),
            is_cancel: "1"
        }

        const dlvRes = await dbConnection.delhiveryLog.findOne({ where: { order_name: orderName, is_cancel: "1" } });
        if (zippeeData?.status === 200) {
            //check if order is already cancelled
            if (dlvRes) {
                await dbConnection.delhiveryLog.update(zippeeObj, { where: { order_name: orderName, is_cancel: "1" } });
            } else {
                await dbConnection.delhiveryLog.create(zippeeObj);
            }
            await dbConnection.orderItemSplit.update({ delhivery_status: "cancel", middleware_status: "CANCELLED", shipment_status: 'cancel' }, { where: { order_name: orderName } });
            return { status: true, message: "Order cancelled successfully" };
        } else {
            if (dlvRes) {
                await dbConnection.delhiveryLog.update({ response_message: "Order is not cancelled from Zippee" }, { where: { order_name: order.order_name, is_cancel: "1" } });
            } else {
                zippeeObj.response_message = "Order is not cancelled from Zippee";
                await dbConnection.delhiveryLog.create(zippeeObj);
            }
            await dbConnection.orderItemSplit.update({ delhivery_status: "failed", middleware_status: "FAILED" }, { where: { order_name: order.order_name } });
        }
    } catch (error) {
        console.log("error in creating zippee order", error)
    }
}

exports.clickpostObject = async (zippeeObj, orderData) => {
    try {
        clickpostObject = {
            "drop_name": zippeeObj[0].deliverAccountName,
            "drop_phone": zippeeObj[0].deliverPhoneNumber,
            "drop_email": orderData?.orderCustomers.length > 0 ? orderData.orderCustomers[0]?.customer_email : '',
            "order_id": orderData.shopify_order_name.replace('#', ''),
            "order_date": orderData.order_created_at,
            "waybillStr": orderData.waybill_number,
            "drop_add": zippeeObj[0].deliverStreetName,
            "pick_add": "Symphony Warehouse",
            "order_name": orderData.order_name,
            "pick_name": "Symphony Warehouse",
            "pick_phone": "***********",
            "pick_email": "<EMAIL>",
            "account_code": CONFIG.clickpost.account_code,
            "courier_partner": CONFIG.clickpost.courier_partner
        }
        return clickpostObject;

    } catch (error) {
        console.log('Error in clickpostObject', error)
    }
}

exports.zippeePackingSlip = async (wayBillNum, orderName) => {
    try {
        console.log("zippee packing slip")
        // let invoicePdf = "https://slipinvoice.s3.ap-south-1.amazonaws.com/invoiceFiles/*************.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAZI5PGVP2G6VZHHVM%2F20211223%2Fap-south-1%2Fs3%2Faws4_request&X-Amz-Date=20211223T035205Z&X-Amz-Expires=900&X-Amz-Signature=db382fe8244a6a9d4a9b3c38d5269fba63baa3195c8d1a4680fbc001462a698c&X-Amz-SignedHeaders=host"
        let invoicePdf = ''

        let pdfPath = await zippeeSlip(wayBillNum, orderName)
        console.log("pdfPath", pdfPath)

        invoicePdf = await delhiveryController.invoiceUpload(pdfPath, wayBillNum)
        return invoicePdf
    } catch (err) {
        console.log('eror pdf=====>', err)
        return
    }
}

const zippeeSlip = async (wayBillNum, orderName) => {
    try {
        let filePath = path.resolve(__dirname, '../template/zippee-invoice.html')
        let orderData = await orderSyncController.getorderSplitDataByName(orderName);
        if (!orderData || orderData.status == false) {
            return { status: false, message: "orderData is not found" };
        }
        let response = orderData.data.dataValues
        console.log(response, "response")
        const canvas = createCanvas(300, 150)
        JsBarcode(canvas, wayBillNum, {
            format: 'CODE128',
            lineColor: '#000',
            width: 2,
            height: 100,
            displayValue: true,
        });
        const buffer = canvas.toBuffer('image/png');
        const base64 = `data:image/png;base64,${buffer.toString('base64')}`;
        console.log(base64);
        console.log(buffer)
        let html = fss.readFileSync(filePath, "utf8");
        let price = response.discount != null && response.discount != "null" ? response.order_amount - parseFloat(response.discount) : response.order_amount
        let newPrice = price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")

        let cfaData = await dbConnection.cfaPlantLocation.findOne({ where: { plant_code: response.plant_code } })
        console.log("cfaData", cfaData)
        cfaData = JSON.parse(JSON.stringify(cfaData))
        let users = [{
            packageType: "Standard",
            pin: response.orderAddresses[0].zip_code,
            sortCode: response.orderAddresses[0].zip_code,
            product: response.product_title,
            sellerName: cfaData.plant_name,
            sellerAdd: cfaData.address,
            returnAdd: cfaData.address,
            name: response.orderCustomers[0].first_name + " " + response.orderCustomers[0].last_name,
            address: response.orderAddresses[0].address1,
            payment: response.financial_status,
            destination: response.orderAddresses[0].city,
            price: newPrice,
            barcode: base64,
            orderIdBarcodeUrl: base64,
            waybillNum: response.waybill_number,
        }]

        let options = {
            format: "A4",
            orientation: "portrait"
        }
        let pdfPath = path.resolve(__dirname, `../uploads/output-${response.waybill_number}.pdf`)
        console.log(pdfPath)
        let document = {
            html: html,
            data: {
                users: users,
            },
            path: pdfPath,
            type: "",
        };
        const pdfCreate = await pdf.create(document, options);
        console.log("pdfPath", pdfCreate)
        return pdfPath
    } catch (err) {
        console.log('errrr=====>', err || err.message)
        return
    }
}
