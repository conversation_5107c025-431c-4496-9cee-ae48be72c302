const Sequelize = require("sequelize");
module.exports = (sequelize) => {
    return sequelize.define("zippee_pincode_mapping", {
        id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            unique: true
        },
        store_id: {
            allowNull: false,
            type: Sequelize.INTEGER,
            references: {
                model: 'stores',
                key: 'id'
            },
        }, pincode: {
            type: Sequelize.STRING,
            primaryKey: true
        },
        cfa: {
            type: Sequelize.STRING,
            allowNull: true,
        },
    },
        {
            indexes: [
                {
                    unique: true,
                    fields: ['pincode']
                }
            ]

        });
};