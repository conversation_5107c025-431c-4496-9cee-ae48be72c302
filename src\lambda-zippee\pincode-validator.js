const db = require('./db');

exports.pincodeValidator = async (sku, pincode) => {
  try {
    if (!sku || !pincode) {
      return { success: false, message: "SKU and pincode are required" };
    }

    const [zippiePincodeRows] = await db.execute(
      'SELECT * FROM zippee_pincode_mappings WHERE pincode = ? LIMIT 1',
      [pincode]
    );

    if (zippiePincodeRows.length > 0) {
      const zippieSkuData = zippiePincodeRows[0];
      const cfaFromMapping = zippieSkuData.cfa;
        
      const [getStockRows] = await db.execute(
        'SELECT local_stock FROM cfa_stock_mappings WHERE sku = ? AND pincode_group = ? AND local_stock > 0 LIMIT 1',
        [sku, cfaFromMapping]
      );

      if (getStockRows.length>0){
        return { 
          success: true, 
          message: "Same-day delivery available for orders placed before 2 PM. Order will be delivered by 9 PM."
        };
      }
    }

    return { success: true, message: "Same day Dispatch, 80% of Orders delivered within 2 Business days." };

  } catch (error) {
    console.error("Error in validating pincode:", error);
    return {
      success: false,
      message: "Error validating pincode",
      error: error.message,
    };
  }
};
