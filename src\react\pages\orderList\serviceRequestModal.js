import { Heading, Modal, Select, Stack, TextField } from "@shopify/polaris";
import React, { Component } from "react";
import '../gstOrders/gstOrders.css'
import { checkNullOrUndefined, statesArr, modelArray, textServiceRequestArr } from "../../helpers/appHelpers";
import { openToast } from "../../redux/toast/toastActions";
import { connect } from "react-redux";

class ServiceRequestModal extends Component {
    constructor(props) {
        super(props);
        this.state = {
            textFieldObj: {
                'first_name': "",
                'last_name': "",
                'model_type': "",
                'customer_email': "",
                'province_code': "",
                'city': "",
                'phone': "",
                'zip_code': "",
                'address1': "",
                'product_serial_number':'',
                'reason':""
            }
        }
    }

    componentDidMount() {
        document.title = "gstOrders";
        this.setDefaultStates()
    }
    setDefaultStates() {
        this.setState({
            textFieldObj: {
                'first_name': checkNullOrUndefined(this.props.orderItemData?.orderAddresses?.first_name),
                'last_name': checkNullOrUndefined(this.props.orderItemData?.orderAddresses?.last_name),
                'province_code': checkNullOrUndefined(this.props.orderItemData?.orderAddresses?.province_code),
                'city': checkNullOrUndefined(this.props.orderItemData?.orderAddresses?.city),
                'phone': checkNullOrUndefined(this.props.orderItemData?.orderAddresses?.phone),
                'zip_code': checkNullOrUndefined(this.props.orderItemData?.orderAddresses?.zip_code),
                'customer_email': checkNullOrUndefined(this.props.orderItemData?.orderAddresses?.customer_email),
                'address1': checkNullOrUndefined(this.props.orderItemData?.orderAddresses?.address1),
                "model_type": "",
                'product_serial_number': checkNullOrUndefined(this.props.orderItemData?.product_serial_number),
                "reason":"",
            }
        })
    }

    clearTextFieldState() {
        this.props.handleServiceModal(false)
        this.setDefaultStates()
    }
    addSelect(index) {
        if (index == 'province_code') {
            return <Select
                label={"State"}
                options={statesArr}
                onChange={(val) => this.handleTextFieldArr(val, index)}
                value={this.state.textFieldObj[index]}
            />
        } else {
            return <Select
                label={"Model Type"}
                options={modelArray}
                onChange={(val) => this.handleTextFieldArr(val, index)}
                value={this.state.textFieldObj[index]}
            />
        }
    }
    callTextField = () => {
        let arr = Object.keys(this.state.textFieldObj);
        let elements = []
        for (let index = 0; index < arr.length; index += 2) {
            elements.push(<div id="billing_fields">
                <Stack spacing="loose" key={index} distribution="fill" vertical={false}>
                    {arr[index] == "province_code" || arr[index] == "model_type" ?
                        this.addSelect(arr[index]) :
                        <TextField label={textServiceRequestArr[arr[index]]} value={this.state.textFieldObj[arr[index]]} onChange={(val) => this.handleTextFieldArr(val, arr[index])} />}
                    {arr[index + 1] ? <TextField label={textServiceRequestArr[arr[index + 1]]} value={this.state.textFieldObj[arr[index + 1]]} onChange={(val) => this.handleTextFieldArr(val, arr[index + 1])} /> : ""}
                </Stack>
            </div>)
        }
        return elements
    }

    handleTextFieldArr = (val, valueType) => {
        this.setState({ textFieldObj: { ...this.state.textFieldObj, [valueType]: val } })
    }
    
    callToEditBilling = async () => {
        let textFieldValues = this.state.textFieldObj
        console.log("orderItemData",this.props.orderItemData)
        console.log("in above",textFieldValues.model_type)
        if(textFieldValues.model_type==""){
            return this.props.openToast({message: "Please select all field", isActive: true, isError: true})
        }
        let req = {
            address: textFieldValues.address1,
            city: textFieldValues.city,
            customerName: textFieldValues.first_name + textFieldValues.last_name,
            mobileNumber: textFieldValues.phone,
            province_code: textFieldValues.province_code,
            pinCode: textFieldValues.zip_code,
            email: textFieldValues.customer_email,
            model: textFieldValues.model_type,
            customerRemarks:textFieldValues.reason,
            productSerialNumber:textFieldValues.product_serial_number,
            dateOfPurchase:this.props.orderItemData.order_created_at
        }
        console.log("reqqqq-->",req);
        await this.props.handleSubmitService(req,this.props.orderItemData.order_name);
        this.clearTextFieldState()
    }

    render() {
        return (
            <Modal
                title={<Heading element="h2" >Create Service Request</Heading>}
                open={this.props.serviceModalActive}
                onClose={() => this.clearTextFieldState()}
                primaryAction={{
                    content: 'Submit',
                    onAction: this.callToEditBilling,
                    loading: this.props.modalBtnLoading,
                }}
                secondaryActions={{
                    content: 'Cancel',
                    onAction: () => this.clearTextFieldState()
                }}
            >
                <Modal.Section>
                    {this.callTextField()}
                </Modal.Section>
            </Modal>
        )
    }
}

// export default ServiceRequestModal;
const mapStateToProps = (state) => ({

  });

const mapDispatchToProps = (dispatch) => ({
    openToast: (data) => dispatch(openToast(data))
})
export default connect(mapStateToProps, mapDispatchToProps)(ServiceRequestModal);

