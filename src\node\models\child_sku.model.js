const Sequelize = require("sequelize");
module.exports = (sequelize) => {
    return sequelize.define("child_sku", {
        id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            unique: true
        },
        child_sku: {
            allowNull : true,
            type: Sequelize.STRING,
        },
        cfa : {
            type : Sequelize.STRING
        },
        is_cloud: {
            type: Sequelize.ENUM("0","1","2"),
            defaultValue : "2"
        }
    },
        {
            indexes: [
                {
                    unique: true,
                    fields: ['child_sku']
                }
            ]
        });
};