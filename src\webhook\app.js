const express = require('express');
const app = express();
let { con } = require('./sqlConnection')
let port = process.env.PORT || 8000;
const order = require("./order")
const product = require("./product")

app.use(express.json());
app.use(express.urlencoded({ extended: true }))

app.post("/", async (req, res) => {

    if (process.env.NODE_ENV == 'local') {
        var data = req.body
        var metaData = req.headers
        var myshopifyDomain = metaData["x-shopify-shop-domain"]
        var topic = metaData["x-shopify-topic"]
    } else {
        var data = req.body.detail.payload
        var metaData = req.body.detail.metadata
        var myshopifyDomain = metaData["X-Shopify-Shop-Domain"]
        var topic = metaData["X-Shopify-Topic"]
    }

    // console.log("data==========>", JSON.stringify(data))
    con.query(`SELECT id,token FROM stores WHERE myshopify_domain = '${myshopifyDomain}'`, async (err, result) => {
        if (err) {
            console.log("errstores =====>", err)
            return
        }
        console.log("result result.length =====>", result.length)
        if (result && result.length) {
            for (let value of result) {
                let storeId = value.id
                let token = value.token
                if (storeId) {
                    if (topic == 'orders/create' || topic == 'orders/updated') {
                        const start = new Date("2022-01-24 00:00:00")
                        const orderCreatedAt = new Date(data.created_at)
                        if (orderCreatedAt >= start) {
                            await order.shopifyOrders(storeId, data)
                            let isprocced = await order.shopifyOrderSplit(storeId, data, myshopifyDomain, token)
                            if (isprocced.temp) {
                                await order.shopifyOrderAddress(storeId, data, isprocced?.billingCompany)
                                await order.shopifyOrderCustomer(storeId, data)
                            }
                            // await order.shopifyOrderItem(storeId, data)
                        }
                        // if (topic == 'orders/create') {
                        //     axios.post(`http://localhost:5000/push/delhivery?shop=${myshopifyDomain}`, {
                        //         shopifyOrderId: data.id,
                        //     })
                        // }
                    }
                    if (topic == 'products/create' || topic == 'products/update') {
                        await product.shopifyProducts(storeId, data)
                        // await product.shopifyProductVariant(storeId, data)
                        // await product.shopifyProductImage(storeId, data)
                    }
                }
            }
        }

    })
    res.status(200).send();
})
app.use((err, req, res, next) => {
    console.log("err-->", err)
    next()
})
app.listen(port, () => {
    console.log('app listen on =====>>>', port)
})

process.on('uncaughtException', function (err) {
    console.error("euncaughtException", err)
});
process.on('unhandledRejection', function (err) {
    console.error("unhandledRejection", err)
});
