const dbConnection = require("../models")
const CONFIG = require('../config/config_' + [process.env.NODE_ENV || 'local'] + '.json');
const response = require('../helper/appResponse');
const xlsx = require("xlsx");
const xlsxJson = require('json-as-xlsx')
const Sequelize = require("sequelize");
const modelOperators = Sequelize.Op;
const helper = require('../helper/helper');
const fs = require('fs');
const { promisify } = require('util')
const unlinkAsync = promisify(fs.unlink)
const emailController = require('./email-controller');
const path = require('path');
const s3 = require("../helper/awsClient");

exports.getCfaData = async (req, res) => {
    let successMessage = response.responseMessage(CONFIG.status.FAILED, CONFIG.msg.FAILED)
    let status = 500
    try {
        let shopName = req.query.shop
        if (shopName) {
            let userData = await dbConnection.shop.findOne({ where: { myshopify_domain: shopName } })
            if (userData) {
                let allData = await dbConnection.cfaPlantLocation.findAll({ where: { store_id: userData.id } })
                let data = []
                if (allData.length > 0) {
                    for (item of allData) {
                        if (item.address != null || item.address != '' || item.address != undefined) {
                            data.push({
                                plantCode: item.plant_code,
                                plantName: item.plant_name
                            })
                        }
                    }
                }
                successMessage.status = CONFIG.status.SUCCESS
                successMessage.message = CONFIG.msg.SUCCESS
                successMessage.data = data
                let status = 200
            } else {
                successMessage.status = CONFIG.status.FAILED
                successMessage.message = CONFIG.msg.NO_DATA

            }
        } else {
            successMessage.status = CONFIG.status.FAILED
            successMessage.message = CONFIG.msg.PARAMETER
        }
    } catch (err) {
        console.log("Error==>", err)
        successMessage.status = CONFIG.status.ERROR
        successMessage.message = CONFIG.msg.ERROR
    }
    res.status(successMessage.status).send(successMessage)
}

exports.createCfaData = async (req, res) => {
    let successMessage = response.responseMessage(CONFIG.status.FAILED, CONFIG.msg.FAILED)
    try {
        let shopName = req.query.shop
        if (shopName) {
            let userData = await dbConnection.shop.findOne({ where: { myshopify_domain: shopName } })
            if (userData) {
                let cfaObj = {
                    store_id: userData.id,
                    pincode: req.body.pincode,
                    address_1: req.body.address_1,
                    address_2: req.body.address_2,
                    city: req.body.city,
                    state: req.body.state,
                    country: req.body.country,
                    cfa_name: req.body.cfa_name
                }
                await dbConnection.cfaLocation.create(cfaObj)
                successMessage.status = CONFIG.status.SUCCESS
                successMessage.message = CONFIG.msg.CREATE
            } else {
                successMessage.status = CONFIG.status.FAILED
                successMessage.message = CONFIG.msg.NO_DATA
            }
        } else {
            successMessage.status = CONFIG.status.FAILED
            successMessage.message = CONFIG.msg.PARAMETER
        }
    } catch (err) {
        successMessage.status = CONFIG.status.ERROR
        successMessage.message = CONFIG.msg.ERROR
        console.log("error", err);
    }
    res.status(200).send(successMessage)
}

exports.editCfaData = async (req, res) => {
    let successMessage = response.responseMessage(CONFIG.status.FAILED, CONFIG.msg.FAILED)
    try {
        let shopName = req.query.shop
        if (shopName) {
            let userData = await dbConnection.shop.findOne({ where: { myshopify_domain: shopName } })
            if (userData) {
                let id = req.params.id
                let cfaObj = {
                    store_id: userData.id,
                    pincode: req.body.pincode,
                    address_1: req.body.address_1,
                    address_2: req.body.address_2,
                    city: req.body.city,
                    state: req.body.state,
                    country: req.body.country,
                    cfa_name: req.body.cfa_name
                }
                let tableData = await dbConnection.cfaLocation.findOne({ where: { id: id } })
                if (tableData) {
                    await dbConnection.cfaLocation.update(cfaObj, { where: { id: id } })
                    successMessage.status = CONFIG.status.SUCCESS
                    successMessage.message = CONFIG.msg.UPDATE
                }
            } else {
                successMessage.status = CONFIG.status.FAILED
                successMessage.message = CONFIG.msg.NO_DATA
            }
        } else {
            successMessage.status = CONFIG.status.FAILED
            successMessage.message = CONFIG.msg.PARAMETER
        }
    } catch (err) {
        successMessage.status = CONFIG.status.ERROR
        successMessage.message = CONFIG.msg.ERROR
    }
    res.status(200).send(successMessage)
}

exports.deleteCfaData = async (req, res) => {
    let successMessage = response.responseMessage(CONFIG.status.FAILED, CONFIG.msg.FAILED)
    try {
        let shopName = req.query.shop
        if (shopName) {
            let userData = await dbConnection.shop.findOne({ where: { myshopify_domain: shopName } })
            if (userData) {
                let id = req.params.id
                dbConnection.cfaLocation.destroy({ where: { id: id } })
                successMessage.status = CONFIG.status.SUCCESS
                successMessage.message = CONFIG.msg.DELETE
            } else {
                successMessage.status = CONFIG.status.FAILED
                successMessage.message = CONFIG.msg.NO_DATA
            }
        } else {
            successMessage.status = CONFIG.status.FAILED
            successMessage.message = CONFIG.msg.PARAMETER
        }
    } catch (err) {
        successMessage.status = CONFIG.status.ERROR
        successMessage.message = CONFIG.msg.ERROR
    }
    res.status(200).send(successMessage)
}

exports.importCfaData = async (req, res) => {
    let successMessage = response.responseMessage(CONFIG.status.FAILED, CONFIG.msg.FAILED)
    let status = 500
    try {
        let shopName = req.query.shop
        if (shopName) {
            const file = xlsx.readFile(req.file.path)
            let data = []
            const sheets = file.SheetNames
            for (let i = 0; i < sheets.length; i++) {
                const fileUpload = xlsx.utils.sheet_to_json(
                    file.Sheets[file.SheetNames[i]], { header: ["Pincode", "City", "State", "Region", "Country"], skipHeader: true })
                fileUpload.forEach((res) => {
                    data.push(res)
                })
            }
            data.splice(0, 1)
            var createDataArr = [];
            let userData = await dbConnection.shop.findOne({ where: { myshopify_domain: shopName } })
            if (userData) {
                let isSendResponse = false
                if (data.length > 0) {
                    isSendResponse = true
                }
                let count = 1;
                for (let value of data) {
                    if (isSendResponse) {
                        successMessage.status = CONFIG.status.SUCCESS
                        successMessage.message = CONFIG.msg.UPLOAD
                        res.status(CONFIG.status.SUCCESS).send(successMessage)
                    }
                    let dataObj = {
                        store_id: userData.id,
                        pincode: value.Pincode,
                        city: value.City,
                        state: value.State,
                        region: value.Region,
                        country: "India",
                        cfa_type: 'primary',
                        pincode_group: value.Pincode,
                    }
                    createDataArr.push(dataObj)
                    if (createDataArr.length == 500) {
                        await dbConnection.cfaPincodeMapping.bulkCreate(createDataArr, { updateOnDuplicate: ["pincode"] })
                            .then(() => {
                                console.log("pincode============>", count)
                                count++;
                                return dbConnection.cfaPincodeMapping.findAll();
                            })
                            .then(() => {
                                createDataArr = [];
                            })
                    }
                    isSendResponse = false;
                }
                if (createDataArr.length > 0) {
                    await dbConnection.cfaPincodeMapping.bulkCreate(createDataArr, { updateOnDuplicate: ["pincode"], individualHooks: true })
                        .then(() => {
                            return dbConnection.cfaPincodeMapping.findAll();
                        }).then(() => {
                            console.log("file uploaded ==>>>")
                        })
                }
            } else {
                successMessage.status = CONFIG.status.FAILED
                successMessage.message = CONFIG.msg.PARAMETER
                res.status(status).send(successMessage)

            }
            unlinkAsync(req.file.path)
        } else {
            successMessage.status = CONFIG.status.FAILED
            successMessage.message = CONFIG.msg.PARAMETER
            res.status(status).send(successMessage)

        }
    } catch (err) {
        console.log("error==>", err)
        successMessage.status = CONFIG.status.ERROR
        successMessage.message = CONFIG.msg.ERROR
        res.status(status).send(successMessage)
    }

}

// Insert city state pincode data
exports.importCityStateData = async (req, res) => {
    let successMessage = response.responseMessage(CONFIG.status.FAILED, CONFIG.msg.FAILED)
    let status = 500
    try {
        let shopName = req.query.shop
        if (shopName) {
            const file = xlsx.readFile(req.file.path)
            let data = []
            const sheets = file.SheetNames
            for (let i = 0; i < sheets.length; i++) {
                const fileUpload = xlsx.utils.sheet_to_json(
                    file.Sheets[file.SheetNames[i]], { header: ["", "STATE_CODE", "STATE", "CITY_CODE", "CITY", "AREA", "PIN_CODE"], skipHeader: true })
                fileUpload.forEach((res) => {
                    data.push(res)
                })
            }
            data.splice(0, 1)
            var createDataArr = [];
            let userData = await dbConnection.shop.findOne({ where: { myshopify_domain: shopName } })
            if (userData) {
                let count = 1;
                for (let value of data) {
                    let dataObj = {
                        store_id: userData.id,
                        city: value.CITY,
                        city_code: value.CITY_CODE,
                        state: value.STATE,
                        state_code: value.STATE_CODE,
                        area: value.AREA,
                        area_code: value.PIN_CODE,
                        pincode: value.PIN_CODE
                    }
                    createDataArr.push(dataObj)
                    if (createDataArr.length == 500) {
                        await dbConnection.cityStatePin.bulkCreate(createDataArr, { updateOnDuplicate: ["city_code", "state_code", "area_code"] })
                            .then(() => {
                                console.log("pincode============>", count)
                                count++;
                                return dbConnection.cityStatePin.findAll();
                            })
                            .then(() => {
                                createDataArr = [];
                            })
                    }
                }
            }
            if (createDataArr.length > 0) {
                await dbConnection.cityStatePin.bulkCreate(createDataArr, { updateOnDuplicate: ["city_code", "state_code", "area_code"], individualHooks: true })
                    .then(() => {
                        return dbConnection.cityStatePin.findAll();
                    }).then(() => {
                        console.log("file uploaded ==>>>", createDataArr.length)
                    })
            }
            unlinkAsync(req.file.path)
            successMessage.status = CONFIG.status.SUCCESS
            successMessage.message = CONFIG.msg.UPLOAD
            successMessage.data = data
            res.status(successMessage.status).send(successMessage)

        } else {
            successMessage.status = CONFIG.status.FAILED
            successMessage.message = CONFIG.msg.PARAMETER
            res.status(status).send(successMessage)
        }
    } catch (err) {
        successMessage.status = CONFIG.status.ERROR
        successMessage.message = CONFIG.msg.ERROR
        console.log("error--------", err);
        res.status(status).send(successMessage)
    }


}

// Insert plant locations data
exports.importCfaPlantLocation = async (req, res) => {

    let successMessage = response.responseMessage(CONFIG.status.FAILED, CONFIG.msg.FAILED)
    let status = 500
    try {
        const file = xlsx.readFile(req.file.path)
        let data = []
        const sheets = file.SheetNames
        for (let i = 0; i < sheets.length; i++) {
            const fileUpload = xlsx.utils.sheet_to_json(
                file.Sheets[file.SheetNames[i]], { header: ["PLANTCODE", "PLANTNAME", "PINCODE", "CITY", "STATE", "STATECODE", "ADDRESS", "MOBILE", "EMAIL", "ACTIVE", "ORDERLIMIT"], skipHeader: true })
            fileUpload.forEach((res) => {
                data.push(res)
            })
        }
        data.splice(0, 1)
        for (let value of data) {
            let dataObj = {
                store_id: '1',
                plant_code: value.PLANTCODE,
                plant_name: value.PLANTNAME,
                search: "CFA",
                pincode: value.PINCODE,
                city: value.CITY,
                country: 'IN',
                state: value.STATE,
                state_code: value.STATECODE,
                address: value.ADDRESS?.replace(/[^a-zA-Z0-9,\- ]/g, ''),
                phone: value.MOBILE,
                email: value.EMAIL,
                is_active: (Number(value.ACTIVE) === 0) ? 0 : 1,
                order_limit: value.ORDERLIMIT ? Number(value.ORDERLIMIT) : null
            }
            let tableData = await dbConnection.cfaPlantLocation.findOne({ where: { plant_code: value.PLANTCODE } })
            if (tableData) {
                await dbConnection.cfaPlantLocation.update(dataObj, { where: { plant_code: value.PLANTCODE } })
            } else {
                await dbConnection.cfaPlantLocation.create(dataObj)
            }
        }
        unlinkAsync(req.file.path)
        successMessage.status = CONFIG.status.SUCCESS
        successMessage.message = CONFIG.msg.UPLOAD
        status = 200
        res.status(status).send(successMessage)
    } catch (err) {
        successMessage.status = CONFIG.status.ERROR
        successMessage.message = CONFIG.msg.ERROR
        res.status(status).send(successMessage)
    }
}

exports.getAllCfa = async (req, res) => {
    try {
        const { page, per_page, search, sort } = req.query;
        var options = {};
        var sortOrder = 'DESC';
        if (search) {
            options = {
                [modelOperators.or]: [
                    { sku: { [modelOperators.like]: `%${search}%` } },
                    { pincode_group: { [modelOperators.like]: `%${search}%` } }
                ]
            }
        }
        if (sort == 'oldest') {
            sortOrder = 'ASC'
        }
        const { limit, offset } = helper.getPagination(page, per_page);
        dbConnection.cfaStockMapping.findAndCountAll({ where: options, limit: limit, offset: offset, order: [['createdAt', `${sortOrder}`]] })
            .then(data => {
                const response = helper.getPagingData(data, page, limit);
                res.status(CONFIG.status.SUCCESS).send(response);
            }).catch(err => {
                console.log('cfa error==>', err)
                res.status(CONFIG.status.FAILED).send(CONFIG.msg.ERROR);
            });
    } catch (error) {
        console.log('cfa catch error==>', error)
        res.status(CONFIG.status.FAILED).send(CONFIG.msg.ERROR);
    }
}

exports.importInvoiceData = async (req, res) => {
    let successMessage = response.responseMessage(CONFIG.status.FAILED, CONFIG.msg.FAILED)
    let status = 500
    try {
        let shopName = req.query.shop
        if (shopName) {
            const file = xlsx.readFile(req.file.path)
            let data = []
            const sheets = file.SheetNames
            for (let i = 0; i < sheets.length; i++) {
                const fileUpload = xlsx.utils.sheet_to_json(
                    file.Sheets[file.SheetNames[i]], {
                    header: ["ORDER_NO", "SHOPIFY_ORDER_ID", "ORDER_DATE", "CUSTOMER_NAME", "AWB_NO", "PRODUCT_TITEL", "QUANTITY", "ORDER_AMOUNT", "DELIVERY_STATUS", "ORDER_STATUS", "SAP_STATUS", "TRACKING_STATUS", "PAYMENT_TYPE", "REPLACEMENT_REASON", "CRM_NO", "CRM_STATUS", "WARRENTY_CODE", "PLANT_CODE", "PICKUP_AWBNO", "DISPATCHED_AWB", "INVOICE_DATE", "CANCEL_DATE", "SAP_DELIVERY_NO", "INVOICE_NO"],
                    skipHeader: true
                })
                fileUpload.forEach((res) => {
                    data.push(res)
                })
            }
            data.splice(0, 1)
            for (let value of data) {
                let userData = await dbConnection.shop.findOne({ where: { myshopify_domain: shopName } })
                if (userData) {
                    if (value.INVOICE_NO) {
                        if (value.ORDER_STATUS == 'Completed' && value.SAP_STATUS == 'Pushed') {
                            await dbConnection.orderItemSplit.update({ sap_billing_number: value.INVOICE_NO, sap_status: 'Invoiced' }, { where: { order_name: value.ORDER_NO } })
                            await dbConnection.sapLog.update({ sap_billing_number: value.INVOICE_NO }, { where: { order_name: value.ORDER_NO } })
                        } else {
                            await dbConnection.sapLog.update({ sap_billing_number: value.INVOICE_NO }, { where: { order_name: value.ORDER_NO, is_cancel: '0', is_return: '0' } })
                        }
                    }
                }
            }
            fs.unlinkSync(req.file.path)
            successMessage.status = CONFIG.status.SUCCESS
            successMessage.message = CONFIG.msg.UPLOAD
            res.status(status).send(successMessage)
        } else {
            successMessage.status = CONFIG.status.FAILED
            successMessage.message = CONFIG.msg.PARAMETER
            res.status(status).send(successMessage)
        }
    } catch (err) {
        console.log("error--------", err);
        successMessage.status = CONFIG.status.ERROR
        successMessage.message = CONFIG.msg.ERROR
        res.status(status).send(successMessage)
    }
}


exports.importCfaPlantEmail = async (req, res) => {

    let successMessage = response.responseMessage(CONFIG.status.FAILED, CONFIG.msg.FAILED)
    let status = 500
    try {
        let shopName = req.query.shop
        if (shopName) {
            const file = xlsx.readFile(req.file.path)
            let data = []
            const sheets = file.SheetNames
            for (let i = 0; i < sheets.length; i++) {
                const fileUpload = xlsx.utils.sheet_to_json(
                    file.Sheets[file.SheetNames[i]], { header: ["PLANTCODE", "EMAIL"], skipHeader: true })
                fileUpload.forEach((res) => {
                    data.push(res)
                })
            }
            // data.splice(0, 1)
            for (let value of data) {
                let userData = await dbConnection.shop.findOne({ where: { myshopify_domain: shopName } })
                if (userData) {
                    let tableData = await dbConnection.cfaPlantLocation.findOne({ where: { plant_code: value.PLANTCODE } })
                    if (tableData) {
                        await dbConnection.cfaPlantLocation.update({ email: value.EMAIL }, { where: { plant_code: value.PLANTCODE } })
                    }
                }
            }
            fs.unlinkSync(req.file.path)
            successMessage.status = CONFIG.status.SUCCESS
            successMessage.message = CONFIG.msg.UPLOAD
            res.status(status).send(successMessage)

        } else {
            successMessage.status = CONFIG.status.FAILED
            successMessage.message = CONFIG.msg.PARAMETER
            res.status(status).send(successMessage)
        }
    } catch (err) {
        console.log("error--------", err);
        successMessage.status = CONFIG.status.ERROR
        successMessage.message = CONFIG.msg.ERROR
        res.status(status).send(successMessage)
    }


}

exports.exportServiceablePincodeDummyFile = async (req, res) => {
    let successMessage = response.responseMessage(CONFIG.status.FAILED, CONFIG.msg.FAILED)
    let buffer
    try {
        let tableData = [{
            "pincode": "380057",
            "city": "Ahmedabad",
            "state": "Gujarat",
            "region": "Gujarat"
        }]

        const mainsettings = {
            writeOptions: {
                type: 'buffer',
                bookType: 'xlsx'
            }
        }
        const data = [
            {
                sheet: 'Serviceable Pincode',
                columns: [
                    { label: 'Pincode', value: row => (row.pincode) },
                    { label: 'City', value: row => (row.city) },
                    { label: 'State', value: row => (row.state) },
                    { label: 'Region', value: row => (row.region) },
                ],
                content: tableData
            }
        ]
        buffer = xlsxJson(data, mainsettings)
        successMessage.status = 200

        res.writeHead(200, {
            'Content-Type': 'application/octet-stream',
            'Content-disposition': 'attachment; filename=ChildSKUCloud_data.xlsx'
        })
    } catch (e) {
        successMessage.status = CONFIG.status.ERROR
        successMessage.message = CONFIG.msg.ERROR
    }
    if (successMessage.status == 200) res.end(buffer)
    else res.status(200).send(successMessage)
}

exports.cfaStockExport = async (req, res) => {
    try {
        res.status(CONFIG.status.SUCCESS).send(CONFIG.msg.SUCCESS);
        let finalData = await dbConnection.cfaStockMapping.findAll({
            where: {
                stock: { [Sequelize.Op.ne]: null },
            },
            attributes: ['is_active', 'stock', 'pincode_group', 'sku']
        })
        const mainsettings = {
            writeOptions: {
                type: 'buffer',
                bookType: 'xlsx'
            }
        }
        let data = [
            {
                sheet: 'Order Data',
                columns: [
                    { label: 'CFA', value: row => (row.pincode_group ? row.pincode_group : "") },
                    { label: 'SKU', value: row => (row.sku ? row.sku : "") },
                    { label: 'STOCK', value: row => (row.stock ? row.stock : "") },
                    { label: 'ACTIVE', value: row => (row.is_active ? "true" : "false") },
                ],
                content: finalData,
            },
        ];
        buffer = xlsxJson(data, mainsettings);
        const fileName = 'orderData.xlsx';
        let filePath = path.join(__dirname, '../uploads', fileName);
        fs.writeFileSync(filePath, buffer);

        const fileContent = fs.readFileSync(filePath);
        var fileData = {
            Bucket: CONFIG.aws.bucketName,
            Key: 'exportOrderDataFile/' + fileName,
            Body: fileContent,
            ContentEncoding: 'base64',
            ACL: 'public-read',
            ContentType: 'application/xlsx',
        };

        const awsData = await s3.upload(fileData).promise();
        fs.unlinkSync(filePath);

        if (awsData) {
            let exportFileUrl = awsData.Location;
            console.log('exportFileUrl---->', exportFileUrl);
            await emailController.sendExportDataEmail(fileName, exportFileUrl, CONFIG.email.exportto, null, 'Exported Order Data');
        }
    } catch (error) {
        res.status(CONFIG.status.SUCCESS).send(CONFIG.msg.SUCCESS);
    }

}

exports.getAllCfaData = async (req, res) => {
    let successMessage = {
        status: CONFIG.status.FAILED,
        message: CONFIG.msg.FAILED,
        data: null
    };
    try {
        let shopName = req.query.shop;
        if (shopName) {
            let userData = await dbConnection.shop.findOne({ where: { myshopify_domain: shopName } });
            if (userData) {
                let allData = await dbConnection.cfaPlantLocation.findAll({
                    attributes: [
                        'id',
                        'plant_code',
                        'plant_name',
                        'is_active',
                        'pincode',
                        'city',
                        'state',
                        'state_code',
                        'phone',
                        'email',
                        'address',
                        'order_limit',
                        'total_order'
                    ],
                    where: { store_id: userData.id }
                });
                successMessage.status = CONFIG.status.SUCCESS;
                successMessage.message = CONFIG.msg.SUCCESS;
                successMessage.data = allData;
            } else {
                successMessage.message = CONFIG.msg.NO_DATA;
            }
        } else {
            successMessage.message = CONFIG.msg.PARAMETER;
        }
    } catch (err) {
        console.log("Error getAllCfaData==>", err);
        successMessage.status = CONFIG.status.ERROR;
        successMessage.message = CONFIG.msg.ERROR;
    }
    res.status(successMessage.status).send(successMessage);
};

exports.exportCfaPlantLocation = async (req, res) => {
    let successMessage = response.responseMessage(CONFIG.status.FAILED, CONFIG.msg.FAILED)
    let buffer
    try {
        let cfaData = await dbConnection.cfaPlantLocation.findAll()

        const mainsettings = {
            writeOptions: {
                type: 'buffer',
                bookType: 'xlsx'
            }
        }
        const data = [
            {
                sheet: 'CFA List',
                columns: [
                    { label: 'PLANTCODE', value: row => (row.plant_code ? row.plant_code : "") },
                    { label: 'PLANTNAME', value: row => (row.plant_name ? row.plant_name : "") },
                    { label: 'PINCODE', value: row => (row.pincode ? row.pincode : "") },
                    { label: 'CITY', value: row => (row.city ? row.city : "") },
                    { label: 'STATE', value: row => (row.state ? row.state : "") },
                    { label: 'STATECODE', value: row => (row.state_code ? row.state_code : "") },
                    { label: 'ADDRESS', value: row => (row.address ? row.address : "") },
                    { label: 'MOBILE', value: row => (row.phone ? row.phone : "") },
                    { label: 'EMAIL', value: row => (row.email ? row.email : "") },
                    { label: 'ACTIVE', value: row => (row.is_active ? "true" : "false") },
                    { label: 'ORDERLIMIT', value: row => (row.order_limit ? row.order_limit : "") }
                ],
                content: cfaData
            }
        ]
        buffer = xlsxJson(data, mainsettings)
        successMessage.status = 200

        res.writeHead(200, {
            'Content-Type': 'application/octet-stream',
            'Content-disposition': 'attachment; filename=cfa-list.xlsx'
        })
    } catch (e) {
        console.log('error ', e)
        successMessage.status = CONFIG.status.ERROR
        successMessage.message = CONFIG.msg.ERROR
    }
    if (successMessage.status == 200) res.end(buffer)
    else res.status(200).send(successMessage)
}


exports.importCfaStockMapping = async (req, res) => {
    let successMessage = response.responseMessage(CONFIG.status.FAILED, CONFIG.msg.FAILED)
    let status = 500
    try {
        const file = xlsx.readFile(req.file.path)
        let data = []
        const sheets = file.SheetNames
        for (let i = 0; i < sheets.length; i++) {
            const fileUpload = xlsx.utils.sheet_to_json(
                file.Sheets[file.SheetNames[i]], { header: ["CFA", "SKU", "ACTIVE"], skipHeader: true })
            fileUpload.forEach((res) => {
                data.push(res)
            })
        }
        data.splice(0, 1)
        for (let value of data) {
            let dataObj = {
                is_active: (Number(value.ACTIVE) === 0) ? 0 : 1,
            }
            let tableData = await dbConnection.cfaStockMapping.findOne({ where: { pincode_group: value.CFA, sku: value.SKU } })
            if (tableData) {
                await dbConnection.cfaStockMapping.update(dataObj, { where: { pincode_group: value.CFA, sku: value.SKU } })
            }
        }
        unlinkAsync(req.file.path)
        successMessage.status = CONFIG.status.SUCCESS
        successMessage.message = CONFIG.msg.UPLOAD
        status = 200
        res.status(status).send(successMessage)
    } catch (err) {
        successMessage.status = CONFIG.status.ERROR
        successMessage.message = CONFIG.msg.ERROR
        res.status(status).send(successMessage)
    }
}