export const orderTypes = {
     GET_ORDERS_LIST_REQUEST: "GET_ORDERS_LIST_REQUEST",
     GET_RETURN_ORDERS_LIST_REQUEST: 'GET_RETURN_ORDERS_LIST_REQUEST',
     GET_ORDERS_LIST_SUCCESS: "GET_ORDERS_LIST_SUCCESS",

     GET_ORDERS_SYNC_REQUEST: "GET_ORDERS_SYNC_REQUEST",
     GET_ORDERS_SYNC_SUCCESS: "GET_ORDERS_SYNC_SUCCESS",
     GET_ORDERS_SYNC_ERROR: "GET_ORDERS_SYNC_ERROR",

     PUSH_ORDERS_REQUEST: "PUSH_ORDERS_REQUEST",
     PUSH_ORDERS_SUCCESS: "PUSH_ORDERS_SUCCESS",
     PUSH_ORDERS_ERROR: "PUSH_ORDERS_ERROR",

     CANCEL_ORDERS_REQUEST: "CANCEL_ORDERS_REQUEST",
     CANCEL_ORDERS_SUCCESS: "CANCEL_ORDERS_SUCCESS",
     CANCEL_ORDERS_ERROR: "CANCEL_ORDERS_ERROR",

     EXPORT_ORDERS_REQUEST: "EXPORT_ORDERS_REQUEST",
     EXPORT_ORDERS_SUCCESS: "EXPORT_ORDERS_SUCCESS",
     EXPORT_ORDERS_ERROR: "EXPORT_ORDERS_ERROR",

     EXPORT_FAILED_ORDERS_REQUEST: "EXPORT_FAILED_ORDERS_REQUEST",
     EXPORT_FAILED_ORDERS_SUCCESS: "EXPORT_FAILED_ORDERS_SUCCESS",
     EXPORT_FAILED_ORDERS_ERROR: "EXPORT_FAILED_ORDERS_ERROR",

     REPLACEMENT_ORDERS_REQUEST: "REPLACEMENT_ORDERS_REQUEST",
     REPLACEMENT_ORDERS_SUCCESS: "REPLACEMENT_ORDERS_SUCCESS",
     REPLACEMENT_ORDERS_ERROR: "REPLACEMENT_ORDERS_ERROR",
     
     ORDER_RETURN_REQUEST: "ORDER_RETURN_REQUEST",
     ORDER_RETURN_SUCCESS: "ORDER_RETURN_SUCCESS",
     ORDER_RETURN_ERROR: "ORDER_RETURN_ERROR",

     SUBMIT_ORDER_CFA_REQUEST: "SUBMIT_ORDER_CFA_REQUEST",
     SUBMIT_ORDER_CFA_SUCCESS: "SUBMIT_ORDER_CFA_SUCCESS",
     SUBMIT_ORDER_CFA_ERROR: "SUBMIT_ORDER_CFA_ERROR",

     REFUND_ORDERS_REQUEST: "REFUND_ORDERS_REQUEST",
     REFUND_ORDERS_SUCCESS: "REFUND_ORDERS_SUCCESS",
     REFUND_ORDERS_ERROR: "REFUND_ORDERS_ERROR",

     RETURN_ORDER_REQUEST: "RETURN_ORDER_REQUEST",
     RETURN_ORDER_REQUEST_SUCCESS: "RETURN_ORDER_REQUEST_SUCCESS",
     RETURN_ORDER_REQUEST_ERROR: "RETURN_ORDER_REQUEST_ERROR",

     EXPORT_ORDER_RETURN_REQUEST: "EXPORT_ORDER_RETURN_REQUEST",
     EXPORT_ORDER_RETURN_SUCCESS: "EXPORT_ORDER_RETURN_SUCCESS",
     EXPORT_ORDER_RETURN_ERROR: "EXPORT_ORDER_RETURN_ERROR",

     SAVE_ORDER_DETAILS_REQUEST: "SAVE_ORDER_DETAILS_REQUEST",
     SAVE_ORDER_DETAILS_SUCCESS: "SAVE_ORDER_DETAILS_SUCCESS",
     SAVE_ORDER_DETAILS_ERROR: "SAVE_ORDER_DETAILS_ERROR",

     SUBMIT_SERVICE_REQUEST: "SUBMIT_SERVICE_REQUEST",
     SUBMIT_SERVICE_SUCCESS: "SUBMIT_SERVICE_SUCCESS",
     SUBMIT_SERVICE_ERROR: "SUBMIT_SERVICE_ERROR",

     GET_FAILED_ORDERS_REQUEST:"GET_FAILED_ORDERS_REQUEST",
     GET_FAILED_ORDERS_SUCCESS:"GET_FAILED_ORDERS_SUCCESS",

     GET_WARRANTY_ORDERS_REQUEST:"GET_WARRANTY_ORDERS_REQUEST",
     GET_WARRANTY_ORDERS_SUCCESS:"GET_WARRANTY_ORDERS_SUCCESS",

     EXPORT_WARRANTY_ORDERS_REQUEST: "EXPORT_WARRANTY_ORDERS_REQUEST",
     EXPORT_WARRANTY_ORDERS_SUCCESS: "EXPORT_WARRANTY_ORDERS_SUCCESS",
     EXPORT_WARRANTY_ORDERS_ERROR: "EXPORT_WARRANTY_ORDERS_ERROR",

     SEND_REFUND_FILE_REQUEST:"SEND_REFUND_FILE_REQUEST",
     SEND_REFUND_FILE_SUCCESS:"SEND_REFUND_FILE_SUCCESS",
     SEND_REFUND_FILE_ERROR:"SEND_REFUND_FILE_ERROR",

     PUSH_FOR_WARRANTY_REQUEST: "PUSH_FOR_WARRANTY_REQUEST",
     PUSH_FOR_WARRANTY_SUCCESS: "PUSH_FOR_WARRANTY_SUCCESS",
     PUSH_FOR_WARRANTY_ERROR: "PUSH_FOR_WARRANTY_ERROR",

}

export const toastObject = {
     message: "",
     isError: false,
     isActive: false
}
