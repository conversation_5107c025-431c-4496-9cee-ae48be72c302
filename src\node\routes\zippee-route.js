module.exports = app => {
  const zippeeController = require("../controllers/zippee-controller");
  const upload = require('../helper/uploadFile')
  const router = require("express").Router();
  const helper = require('../middleware/userAuth')

  router.post("/importZippee",helper.authenticateWebhook, upload.single('file'), zippeeController.importData);
  // router.post("/create-order",zippeeController.createOrder)
  
  app.use('/', router);
};
    