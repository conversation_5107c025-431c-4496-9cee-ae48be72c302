import { put, takeLatest } from "redux-saga/effects";
import Api from "../../apis/Api";
import { openToast } from "../toast/toastActions";
import { settingsTypes, toastObject } from "./settingsTypes";
import {
    getCloudSkuListSuccess,
    getCoverSkuListSuccess,
    saveCloudSkuFileSuccess,
    saveCoverSkuFileSuccess,
    saveCoverSkuFileError,
    saveCloudSkuFileError,
    cloudExportError,
    coverExportError,
    coverExportSuccess,
    cloudExportSuccess,
    deleteCoverSuccess,
    deleteCloudSuccess
} from "./settingsActions";

let toast = toastObject;

function* getCloudSkuList(data) {
    try {
        const response = yield Api.postAsync(
            Api.getCloudSkuList,
            data.payload
        )
        yield put(getCloudSkuListSuccess(response));
        data.payload.callback();
    } catch (e) {
        data.payload.callback();
    }
}

function* getCoverSkuList(data) {
    try {
        const response = yield Api.postAsync(
            Api.getCoverSkuList,
            data.payload
        )
        yield put(getCoverSkuListSuccess(response));
        data.payload.callback();
    } catch (e) {
        data.payload.callback();
    }
}

function* saveCoverFileData(data) {
    let message = "Oppss...There is something wrong.";
    let isError = false;
    try {
        isError = false;
        message = "Data Saved";
        const response = yield Api.postAsync(
            Api.saveCoverFileData,
            data.payload.formData
        )
        yield put(saveCoverSkuFileSuccess(response));
    } catch (e) {
        isError = true;
        let message = "Oppss...There is something wrong.";
        yield put(saveCoverSkuFileError(e.response));
    } finally {
        toast = { ...toast, message: message, isActive: true, isError: isError }
        yield put(openToast(toast));
        data.payload.callback();
    }
}

function* saveCloudFileData(data) {
    let message = "Oppss...There is something wrong.";
    let isError = false;
    try {
        isError = false;
        message = "Data Saved";
        const response = yield Api.postAsync(
            Api.saveCloudFileData,
            data.payload.formData
        )
        yield put(saveCloudSkuFileSuccess(response));
    } catch (e) {
        isError = true;
        message = "Oppss...There is something wrong.";
        yield put(saveCloudSkuFileError(e.response));
    } finally {
        toast = { ...toast, message: message, isActive: true, isError: isError }
        yield put(openToast(toast));
        data.payload.callback();
    }
}

function* coverSkuExport(data) {
    let message = "Oppss...There is something wrong.";
    let isError = false;
    try {
        isError = false;
        message = "Cover Sku has been exported successfully";
        const response = yield Api.postAsync(
            Api.exportCoverSku,
            data.payload
        )
        yield put(coverExportSuccess(response));
    } catch (e) {
        isError = true;
        message = "Oppss...There is something wrong.";
        yield put(coverExportError(e.response));
    } finally {
        toast = { ...toast, message: message, isActive: true, isError: isError }
        yield put(openToast(toast));
        data.payload.callback();
    }
}

function* cloudSkuExport(data) {
    let message = "Oppss...There is something wrong.";
    let isError = false;
    try {
        isError = false;
        message = "Cloud Sku has been exported successfully";
        const response = yield Api.postAsync(
            Api.exportCloudSku,
            data.payload
        )
        yield put(cloudExportSuccess(response));
    } catch (e) {
        isError = true;
        message = "Oppss...There is something wrong.";
        yield put(cloudExportError(e.response));
    } finally {
        toast = { ...toast, message: message, isActive: true, isError: isError }
        yield put(openToast(toast));
        data.payload.callback();
    }
}
function* deleteCoverSku(data) {
    let message = "Oppss...There is something wrong.";
    let isError = false;
    try {
        isError = false;
        message = "Deleted successfully";
        const response = yield Api.postAsync(
            Api.deleteCoverSkuData,
            data.payload.request
        )
        yield put(deleteCoverSuccess(response));
    } catch (e) {
        isError = true;
        message = "Oppss...There is something wrong.";
        yield put(deleteCoverError(e.response));
    } finally {
        toast = { ...toast, message: message, isActive: true, isError: isError }
        yield put(openToast(toast));
        data.payload.callback();
    }
}

function* deleteCloudSku(data) {
    let message = "Oppss...There is something wrong.";
    let isError = false;
    try {
        isError = false;
        message = "Deleted successfully";
        const response = yield Api.postAsync(
            Api.deleteCloudSkuData,
            data.payload.request
        )
        yield put(deleteCloudSuccess(response));
    } catch (e) {
        isError = true;
        message = "Oppss...There is something wrong.";
        yield put(deleteCloudError(e.response));
    } finally {
        toast = { ...toast, message: message, isActive: true, isError: isError }
        yield put(openToast(toast));
        data.payload.callback();
    }
}
export default function* settingsSaga() {
    yield takeLatest(settingsTypes.GET_CLOUD_SKU_LIST_REQUEST, getCloudSkuList);
    yield takeLatest(settingsTypes.GET_COVER_SKU_LIST_REQUEST, getCoverSkuList);
    yield takeLatest(settingsTypes.SAVE_CLOUD_SKU_FILE_REQUEST, saveCloudFileData);
    yield takeLatest(settingsTypes.SAVE_COVER_SKU_FILE_REQUEST, saveCoverFileData);
    yield takeLatest(settingsTypes.COVER_SKU_EXPORT_REQUEST, coverSkuExport);
    yield takeLatest(settingsTypes.CLOUD_SKU_EXPORT_REQUEST, cloudSkuExport);
    yield takeLatest(settingsTypes.DELETE_COVER_SKU_REQUEST, deleteCoverSku);
    yield takeLatest(settingsTypes.DELETE_CLOUD_SKU_REQUEST, deleteCloudSku);
}