const Sequelize = require("sequelize");
module.exports = (sequelize) => {
  return sequelize.define("product", {
    id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      unique: true
    },
    store_id: {
      allowNull:false,
      type: Sequelize.INTEGER,
      references: {
        model: 'stores',
        key: 'id'
      },
    },
    shopify_product_id: {
      allowNull:false,
      type: Sequelize.STRING,
    },
    title: {
      allowNull:true,
      type: Sequelize.STRING(100),
      defaultValue : null
    },
    description: {
      type: Sequelize.TEXT('long'),
    },
    tags: {
      type: Sequelize.TEXT('long'),
    },
    handle: {
      type: Sequelize.STRING,
    },
    published_at : {
      type: Sequelize.DATE,
    },
    product_type: {
      type: Sequelize.STRING
    },
    product_created_at: {
      type: Sequelize.DATE,
    },
    product_updated_at: {
      type: Sequelize.DATE
    },
  },
    {
      indexes: [
        {
          unique: true,
          fields: ['store_id','shopify_product_id']
        }
      ]
    });
};