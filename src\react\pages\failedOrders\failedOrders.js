import {
    Card,
    Layout,
    Page,
    Filters,
    ChoiceList,
    Badge,
    IndexTable,
    Stack,
    Pagination,
    Button,
    Modal,
    TextContainer,
    Popover,
    Heading,
    Select,
    Link,
    TextField,
  } from "@shopify/polaris";
  import React, { Component } from "react";
  import { connect } from "react-redux";
  import {
    pushOrdersRequest,
    getAllFailedOrdersRequest,
    submitOrderCFARequest,
    saveOrderDetailsRequest,
    exportOrderFailedRequest
  } from "../../redux/orders/ordersActions";
  
  import { getMannualCFAListRequest } from "../../redux/cfa/cfaActions";
  import {
    setPagination,
    priceConversion,
    dateConversion,
  } from "../../helpers/appHelpers";
  import DateRangePicker from "../../childComponent/DateRangePicker";
  import moment from "moment";
  import { debounce } from "lodash";
  
  let pagination = setPagination();
  var sDate = moment().subtract(30, "days").toDate();
  var eDate = moment().toDate();
  
  class FailedOrders extends Component {
    constructor(props) {
      super(props);
      this.state = {
        queryValue: "", 
        selectedResources: [],
        allResourcesSelected: false,
        orderData: {},
        popactive: false,
        sortValue: [],
        cfaOption: [],
        pageLoading: false,
        syncModalActive: false,
        orderDetailModalActive: false,
        syncButtonLoading: false,
        ConfirmationLoadingButton: false,
        startDate: moment(sDate).format("YYYY-MM-DD"),
        endDate: moment(eDate).format("YYYY-MM-DD"),
        selectedDate: {
          start: moment().subtract(30, "days").toDate(),
          end: moment().toDate(),
        },
        selectedCFa: "",
        selectedDateText: {
          start: moment().subtract(30, "days").format("YYYY-MM-DD"),
          end: moment().format("YYYY-MM-DD"),
        },
        appOrderStatus: "",
        appOrderName: "",
        createdOrderData: {},
        cancelOrderData: {},
        cfaToCustomerData: {},
        customerToCfaData: {},
        shipmentStatus: [],
        actionsConfirmationActive: false,
        actionsConfirmationHeader: null,
        editModalActive: false,
        editModalIsLoading: false,
        editedOrderStatus: "",
        orderDetailsTexts: {
          waybillNumber: "",
          sapOrderNumber: "",
          sapInvoiceNumber: "",
          sapDeliveryNumber: "",
          cfaNumber: ''
        },
        orderDetailsTextErrors: {
          waybillNumber: false,
          sapOrderNumber: false,
          sapInvoiceNumber: false,
          sapDeliveryNumber: false,
          cfaNumber: false
        },
      };
  
      this.props.history.replace("/app/failed");
    }
    componentDidMount() {
      document.title = "Failed Orders";
      this.getAllOrders();
    }
  
    handleChanagePage = (action) => {
      this.setState({ allResourcesSelected: false, selectedResources: [] });
  
      action === "Next" ? pagination.page++ : pagination.page--;
      this.getAllOrders();
    };
  
    debounceEvent(...args) {
      this.debounceEvent = debounce(...args);
      return (e) => {
        e.perstist();
        return this.debounceEvent(e);
      };
    }
  
    getAllOrders = () => {
      this.setState({ pageLoading: true });
      let { perpage, page } = pagination;
      const {        
        queryValue,
        sortValue,
        startDate,
        endDate,
      } = this.state;
      const request = {
        page: page,
        perpage: perpage,
        search: queryValue,        
        sort: sortValue,
        startDate: startDate,
        endDate: endDate,
      };
      let { getOrdersList } = this.props;
      getOrdersList({
        request,
        callback: () => {
          if (!this.props.error) {
            const { orderListAllData } = this.props;
            const { page, limit, total, totalPage } = orderListAllData.data;
            pagination = setPagination(page, limit, totalPage, total);
          }
          this.setState({ pageLoading: false });
        },
      });
    };
  
    handleFiltersQueryChange = (value) => {
      this.setState({ queryValue: value, selectedResources: [] });
      this.debounceEvent(() => {
        pagination.page = 1;
        this.getAllOrders();
      }, 300);
    };
  
    handleQueryValueRemove = () => {
      this.setState(
        {
          queryValue: "",
        },
        () => {
          this.getAllOrders();
        }
      );
    };
  
    handleFiltersClearAll = () => {
      this.handleQueryValueRemove();
    };  
  
    pushOrder = () => {
      let {
        selectedResources,
        queryValue,
        sortValue,
        allResourcesSelected,
        startDate,
        endDate,
      } = this.state;
      let { pushOrders } = this.props;
      if (allResourcesSelected === true) {
        selectedResources = [];
      }
      const objdata = {
        order_ids: selectedResources,
        isAllResource: allResourcesSelected,
        filters: {
          search: queryValue,
          sort: sortValue,
          startDate: startDate,
          endDate: endDate,
        },
      };
      pushOrders({
        objdata,
        callback: () => {
          if (!this.props.error) {
            this.setState({
              selectedResources: [],
              allResourcesSelected: false,
              actionsConfirmationActive: false,
              ConfirmationLoadingButton: false,
            });
            // this.props.history.push("/app/orders");
            this.getAllOrders();
          } else {
            this.setState({ ConfirmationLoadingButton: false });
          }
        },
      });
    };
  

    handleExportOrder = () => {
      let {
        selectedResources,
        queryValue,
        allResourcesSelected,
        startDate,
        endDate,
      } = this.state;
      let { exportFailedOrders } = this.props;
      if (allResourcesSelected === true) {
        selectedResources = [];
      }
      const objdata = {
        order_ids: selectedResources,
        isAllResource: allResourcesSelected,
        filters: {
          search: queryValue,
          start_date: startDate,
          end_date: endDate,
        },
      };
      exportFailedOrders({
        objdata,
        callback: () => {
          if (!this.props.error) {
            var response = this.props.orderResponseData;
            // const downloadUrl = window.URL.createObjectURL(
            //   new Blob([response.data])
            // );
            // const link = document.createElement("a");
            // link.href = downloadUrl;
            // var name = "orders";
            // link.setAttribute("download", name + ".xlsx");
            // document.body.appendChild(link);
            // link.click();
            // link.remove();
            this.setState({
              ConfirmationLoadingButton: false,
              selectedResources: [],
              allResourcesSelected: false,
              actionsConfirmationActive: false,
            });
            this.getAllOrders();
          } else {
            this.setState({ ConfirmationLoadingButton: false });
          }
        },
      });
    };
  
  
    handleSaveOrderDetails = () => {
      this.setState({ editModalIsLoading: true });
      let objdata = {
        orderStatus: this.state.editedOrderStatus,
      };
      if (this.state.editedOrderStatus == "cancel") {
        objdata.data = { ...this.state.cancelOrderData };
      } else if (this.state.editedOrderStatus == "replaced") {
        objdata.data = { ...this.state.customerToCfaData };
      } else {
        objdata.data = { ...this.state.createdOrderData };
      }
      objdata.data.orderName = this.state.appOrderName;
      let { saveOrderDetails } = this.props;
      saveOrderDetails({
        objdata,
        callback: () => {
          if (!this.props.error) {
            this.setState({
              editModalIsLoading: false,
              editModalActive: !this.state.editModalActive,
              orderDetailModalActive: false,
              selectedResources: [],
            });
            this.getAllOrders();
          } else {
            this.setState({
              editModalIsLoading: false,
              orderDetailModalActive: false,
              selectedResources: [],
            });
          }
        },
      });
    };
    handleOrderDetailsText = (value, key) => {
      if (this.state.editedOrderStatus == "cancel") {
        let tempObj = { ...this.state.cancelOrderData };
        tempObj[key] = value;
        this.setState({
          cancelOrderData: { ...tempObj },
        });
      } else if (this.state.editedOrderStatus == "replaced") {
        let tempObj = { ...this.state.customerToCfaData };
        tempObj[key] = value;
        this.setState({
          customerToCfaData: { ...tempObj },
        });
      } else {
        let tempObj = { ...this.state.createdOrderData };
        tempObj[key] = value;
        this.setState({
          createdOrderData: { ...tempObj },
        });
      }
    };
  
    handleOrderDetailsEdit = (order_status) => {
      this.setState({
        editModalActive: !this.state.editModalActive,
        modalHeader: `${this.state.modalHeader.includes("Edit")
          ? this.state.modalHeader
          : `Edit ${this.state.modalHeader}`
          }`,
        editedOrderStatus: order_status,
      });
    };
  
    handleRefundStatusChange = (value) => {
      this.setState(
        {
          refundStatus: value,
          selectedResources: [],
        },
        () => {
          pagination.page = 1;
          this.getAllOrders();
        }
      );
    };
    togglePopover = () => {
      this.setState({
        popactive: !this.state.popactive,
      });
    };
  
    handleChange = (value) => {
      this.setState(
        {
          sortValue: value,
        },
        () => {
          pagination.page = 1;
          this.getAllOrders();
        }
      );
    };
    handleOrderDetailModal = (appOrderName, appOrderStatus, item) => {
      if (appOrderStatus == "Out Of Stock") {
        let { getCFAList } = this.props;
        getCFAList({
          callback: () => {
            if (!this.props.error) {
              const { cfaListData } = this.props;
              let cfaData = cfaListData.data.data;
              let cfaSelectList = [];
              for (const data of cfaData) {
                cfaSelectList.push({
                  label: data.plantCode + " (" + data.plantName + ")",
                  value: data.plantCode,
                });
              }
              if (cfaSelectList.length > 0) {
                this.setState({ selectedCFa: cfaSelectList[0].value });
              }
              this.setState({ cfaOption: cfaSelectList });
            }
            this.setState({ cfaButttonLoading: false });
          },
        });
      }
  
      let createdOrderObj = {};
      let cancelOrderObj = {};
      let cfaToCustomer = {};
      let customerToCfa = {};
  
      if (item.is_cancel == "1") {
        let sapArr = item.sapLog.filter((el) => {
          return el.is_cancel == "1";
        });
        let delhiveryArr = item.delhiveryLog.filter((el) => {
          return el.is_cancel == "1";
        });
  
        for (let sapValue of sapArr) {
          cancelOrderObj.sapOrderNumber = sapValue.sap_order_number;
          cancelOrderObj.sapInvoiceNumber = sapValue.sap_billing_number;
          cancelOrderObj.sapDeliveryNumber = sapValue.sap_delivery_number;
          cancelOrderObj.cfaNumber = sapValue.plant_code
        }
        for (let delhiveryValue of delhiveryArr) {
          cancelOrderObj.orderName = delhiveryValue.order_name;
          cancelOrderObj.waybillNumber = delhiveryValue.waybill_number;
        }
        this.setState({
          cancelOrderData: cancelOrderObj,
        });
      } else if (item.order_status == "Replacement" || item.order_status == "Returned") {
        let sapArr = item.sapLog.filter((el) => {
          return el.is_return == "1";
        });
        let delhiveryArr = item.delhiveryLog.filter((el) => {
          return el.is_return == "1";
        });
        if (delhiveryArr.length > 0 && sapArr.length > 1) {
          for (let value of delhiveryArr) {
            // if (value.return_order_name) {
            //   cfaToCustomer.waybillNumber = value.waybill_number
            // } else {
            customerToCfa.waybillNumber = value.waybill_number;
            // }
          }
  
          for (let value of sapArr) {
            // if (value.return_order_name) {
            //   cfaToCustomer.orderName = value.order_name
            //   cfaToCustomer.sapOrderNumber = value.sap_order_number
            //   cfaToCustomer.sapInvoiceNumber = value.sap_billing_number
            //   cfaToCustomer.sapDeliveryNumber = value.sap_delivery_number
            // } else {
            customerToCfa.orderName = value.order_name;
            customerToCfa.sapOrderNumber = value.sap_order_number;
            customerToCfa.sapInvoiceNumber = value.sap_billing_number;
            customerToCfa.sapDeliveryNumber = value.sap_delivery_number;
            customerToCfa.cfaNumber = value.plant_code;
            // }
          }
        } else {
          for (let delhiveryValue of delhiveryArr) {
            customerToCfa.waybillNumber = delhiveryValue.waybill_number;
            customerToCfa.orderName = delhiveryValue.order_name;
          }
  
          for (let sapValue of sapArr) {
            customerToCfa.sapOrderNumber = sapValue.sap_order_number;
            customerToCfa.sapInvoiceNumber = sapValue.sap_billing_number;
            customerToCfa.sapDeliveryNumber = sapValue.sap_delivery_number;
            customerToCfa.cfaNumber = sapValue.plant_code;
          }
        }
        this.setState({
          customerToCfaData: customerToCfa,
          // cfaToCustomerData: cfaToCustomer
        });
      }
      let sapArr = item.sapLog.filter(el => {
        return el.is_cancel == "0" && el.is_return == "0";
      });
      let delhiveryArr = item.delhiveryLog.filter((el) => {
        return el.is_cancel == "0" && el.is_return == "0";
      });
  
      createdOrderObj.orderName = sapArr.length > 0 ? sapArr[0].order_name : "";
      createdOrderObj.sapOrderNumber = sapArr.length > 0 ? sapArr[0].sap_order_number : "";
      createdOrderObj.sapInvoiceNumber = sapArr.length > 0 ? sapArr[0].sap_billing_number : "";
      createdOrderObj.sapDeliveryNumber = sapArr.length > 0 ? sapArr[0].sap_delivery_number : "";
      createdOrderObj.cfaNumber = sapArr.length > 0 ? sapArr[0].plant_code : ''
      createdOrderObj.waybillNumber = delhiveryArr.length > 0 ? delhiveryArr[0].waybill_number : "";
      this.setState({
        orderDetailModalActive: true,
        appOrderStatus: appOrderStatus,
        appOrderName: appOrderName,
        createdOrderData: createdOrderObj,
        selectedResources: [],
        allResourcesSelected: false,
        modalHeader: "Order Detail /" + appOrderName,
      });
    };
    handleOrderDetailModalClose = () => {
      this.setState({
        orderDetailModalActive: false,
        appOrderStatus: "",
        appOrderName: "",
        selectedResources: [],
        allResourcesSelected: false,
        createdOrderData: {},
        cancelOrderData: {},
        cfaToCustomerData: {},
        customerToCfaData: {},
      });
    };
    handleSelectCFA = (value) => {
      this.setState({ selectedCFa: value });
    };
  
    handleSubmitCFA = () => {
      if (this.state.selectedCFa != "") {
        this.setState({ cfaButttonLoading: true });
        const { submitOrderCFA } = this.props;
        let orderObj = {};
        orderObj.order_ids = [this.state.appOrderName];
        orderObj.plantCode = this.state.selectedCFa;
        submitOrderCFA({
          orderObj,
          callback: () => {
            if (!this.props.error) {
              this.setState({
                cfaButttonLoading: false,
                orderDetailModalActive: false,
                appOrderStatus: "",
                appOrderName: "",
                selectedResources: [],
                allResourcesSelected: false,
              });
              this.getAllOrders();
            } else {
              this.setState({ cfaButttonLoading: false });
            }
          },
        });
      } else {
        this.setState({ cfaButttonLoading: false });
      }
    };
  
    onSelection = (type, isChecked, id) => {
      let { selectedResources, allResourcesSelected } = this.state;
      const { loading, orderListAllData } = this.props;
      if (!loading) {
        const dataObj = orderListAllData.data.data;
        switch (type) {
          case "page":
            allResourcesSelected = false;
            if (isChecked) {
              dataObj.forEach(function (order_data) {
                let id = order_data.order_name;
                if (selectedResources.indexOf(id) === -1) {
                  selectedResources.push(id);
                }
              });
            } else {
              selectedResources = [];
            }
            break;
          case "single":
            allResourcesSelected = false;
            if (isChecked) {
              selectedResources.push(id);
            } else {
              selectedResources = selectedResources.filter(function (value) {
                return value !== id;
              });
            }
            break;
          case "all":
            if (isChecked) {
              allResourcesSelected = true;
              dataObj.forEach(function (order_data) {
                let id = order_data.order_name;
                if (selectedResources.indexOf(id) === -1) {
                  selectedResources.push(id);
                }
              });
            }
            break;
          default:
            break;
        }
      }
      this.setState({
        selectedResources: selectedResources,
        allResourcesSelected: allResourcesSelected,
      });
    };
    actionsConfirmationClose = () => {
      this.setState({
        actionsConfirmationActive: false,
        ConfirmationLoadingButton: false,
      });
    };
    actionsConfirmationModal = () => {
      this.setState({ ConfirmationLoadingButton: true });

      if (this.state.actionsConfirmationHeader == "Export Orders") {
        this.handleExportOrder("export");
      }
      if (this.state.actionsConfirmationHeader == "Push Orders") {
        this.pushOrder();
      }
  
    };
    exportActionsModal = () => {
      this.setState({
        actionsConfirmationHeader: "Export Orders",
        actionsConfirmationActive: true,
      });
    };
    pushActionsModal = () => {
      this.setState({
        actionsConfirmationHeader: "Push Orders",
        actionsConfirmationActive: true,
      });
    };
    orderListMarkup = () => {
      const { selectedResources } = this.state;
      const { loading, orderListAllData } = this.props;
      let allPage = 1;
      const resourceName = {
        singular: "Order",
        plural: "Orders",
      };
      const bulkActions = [
        {
          content: "Export",
          onAction: this.exportActionsModal,
        }
        // {
        //   content: "Push",
        //   onAction: this.pushActionsModal,
        // },
      ];
      let rowMarkup = [];
      if (!loading) {
        const dataObj = orderListAllData.data.data;
        const { page, limit, total, totalPage } = orderListAllData.data;
        allPage = totalPage;
        pagination = setPagination(page, limit, totalPage, total);
        rowMarkup = dataObj.map(
          (
            item,
            index
          ) => (
            <IndexTable.Row
              id={item.order_name}
              key={item.order_name}
              selected={selectedResources.includes(item.order_name)}
              position={index}
            >
              <IndexTable.Cell>{item.shopify_order_name}</IndexTable.Cell>
              <IndexTable.Cell>
                <Button
                  plain
                  onClick={() => {
                    this.handleOrderDetailModal(
                      item.order_name,
                      item.order_status,
                      item
                    );
                  }}
                >
                  {item.order_name}
                </Button>
              </IndexTable.Cell>
              <IndexTable.Cell>
                <div>
                  <p>{item.product_title}</p>
                  <p>SKU: {item.sku}</p>
                  <p>Qty: {item.quantity}</p>
                </div>
              </IndexTable.Cell>
              <IndexTable.Cell>
                {item.orderCustomers ? (
                  (item.orderCustomers.first_name == null ||
                    item.orderCustomers.first_name == "null") &&
                    (item.orderCustomers.last_name == null ||
                      item.orderCustomers.last_name == "null") ? (
                    <div>{item.orderCustomers.customer_email}</div>
                  ) : (
                    <div className="capitalize">
                      {(item.orderCustomers.first_name != null &&
                        item.orderCustomers.first_name != "null" &&
                        item.orderCustomers.first_name != ""
                        ? item.orderCustomers.first_name + " "
                        : "") +
                        (item.orderCustomers.last_name != null &&
                          item.orderCustomers.last_name != "null" &&
                          item.orderCustomers.last_name != ""
                          ? item.orderCustomers.last_name
                          : "")}
                    </div>
                  )
                ) : (
                  ""
                )}
              </IndexTable.Cell>
        
              <IndexTable.Cell>
              <Badge status="success">{item.middleware_status}</Badge>
              </IndexTable.Cell>
              <IndexTable.Cell>
              {item.failed_reason}
              </IndexTable.Cell>
              <IndexTable.Cell>
                <div>
                  {item.currency == "USD" ? "$" : "₹"}
                  {item.discount !== null &&
                    item.discount !== "" &&
                    item.discount !== "null"
                    ? priceConversion(item.order_amount - item.discount)
                    : priceConversion(item.order_amount)}
                </div>
              </IndexTable.Cell>
              <IndexTable.Cell>
                {dateConversion(item.order_created_at)}
              </IndexTable.Cell>
            </IndexTable.Row>
          )
        );
      }
      return (
        <IndexTable
          resourceName={resourceName}
          itemCount={rowMarkup.length}
          selectedItemsCount={
            this.state.allResourcesSelected
              ? "All"
              : this.state.selectedResources.length
          }
          hasMoreItems={allPage > 1 ? true : false}
          onSelectionChange={this.onSelection}
          loading={this.state.pageLoading}
          bulkActions={bulkActions}
          footerContent={pagination.showing}
          headings={[
            { title: "Shopify Order#" },
            { title: "Order#" },
            { title: "Product Detail" },
            { title: "Customer" },
            { title: "Middleware Status" },
            { title: "Failed Reason" },
            { title: "Amount" },
            { title: "Date" }, 
          ]}
        >
          {rowMarkup}
        </IndexTable>
      );
    };
    render() {
      const filters = []
      const sortOptions = [
        { label: "Date (oldest first)", value: "oldest" },
        { label: "Date (newest first)", value: "newest" },
      ];
      const appliedFilters = [];
      return (
        <div>
          <Page
            fullWidth
            title="Orders"
            primaryAction={
              <Stack>
                <DateRangePicker
                  align="right"
                  primary={this.state.primary}
                  handleApplyFilter={(value) => {
                    this.setState(
                      {
                        startDate: value.startDate,
                        endDate: value.endDate,
                        selectedDate: value.selectedDate,
                        selectedDateText: value.selectedDateText,
                      },
                      () => {
                        this.getAllOrders();
                      }
                    );
                  }}
                />
              </Stack>
            }
          >
            <Layout>
              <Layout.Section>
                <Card>
                  <Card.Section>
                    <div style={{ padding: "16px", display: "flex" }}>
                      <div style={{ flex: 1 }}>
                        <Filters
                          queryPlaceholder="Search"
                          label="Title"
                          filters={filters}
                          queryValue={this.state.queryValue}
                          appliedFilters={appliedFilters}
                          onQueryChange={this.handleFiltersQueryChange}
                          onQueryClear={this.handleQueryValueRemove}
                          // value={this.state.searchField}
                          onClearAll={this.handleFiltersClearAll}
                        />
                      </div>
                      <div style={{ paddingLeft: "0.4rem" }}>
  
                      </div>
                      <div>
                        <Popover
                          fluidContent={true}
                          active={this.state.popactive}
                          activator={<div></div>}
                          onClose={this.togglePopover.bind(this)}
                          preferredAlignment="right"
                          preferredPosition="below"
                          footerContent
                        >
                          <Card>
                            <Card.Section>
                              <ChoiceList
                                title="Sort By"
                                choices={sortOptions}
                                selected={this.state.sortValue}
                                onChange={this.handleChange}
                              />
                            </Card.Section>
                          </Card>
                        </Popover>
                      </div>
                    </div>
                  </Card.Section>
                  {this.orderListMarkup()}
                  <Card.Section>
                    <Stack
                      alignment="center"
                      vertical="middle"
                      distribution="center"
                    >
                      <Pagination
                        plain
                        hasPrevious={pagination.hasPrevious}
                        onPrevious={() => {
                          this.handleChanagePage("Previous");
                        }}
                        hasNext={pagination.hasNext}
                        onNext={() => {
                          this.handleChanagePage("Next");
                        }}
                      />
                    </Stack>
                  </Card.Section>
                </Card>
              </Layout.Section>
            </Layout>
          </Page>
  
          <Modal
            medium
            title={this.state.modalHeader}
            open={this.state.orderDetailModalActive}
            onClose={this.handleOrderDetailModalClose}
          >
            {this.state.appOrderStatus == "Out Of Stock" ? (
              <Modal.Section>
                <div>
                  <Stack>
                    <Stack.Item fill>
                      <Select
                        labelInline
                        label="Select CFA"
                        options={this.state.cfaOption}
                        onChange={this.handleSelectCFA}
                        value={this.state.selectedCFa}
                      />
                    </Stack.Item>
                    <Stack.Item>
                      <Button loading={this.state.cfaButttonLoading} onClick={this.handleSubmitCFA}>Submit</Button>
                    </Stack.Item>
                  </Stack>
                </div>
              </Modal.Section>
            ) : null}
            <Modal.Section>
  
              {Object.values(this.state.cancelOrderData).some(val => val) == true ? (
                <div>
                  <div>
                    <Stack distribution="equalSpacing">
                      <Heading>Order Canceled </Heading>
                      <Link onClick={() => this.handleOrderDetailsEdit('cancel')}>Edit</Link>
                    </Stack>
                    <br></br>
                    <Stack distribution="fillEvenly">
                      {(<p>Order#</p>)}
                      {(<p>WayBill#</p>)}
                      {(<p>SAPOrder#</p>)}
                      {(<p>SAPInvoice#</p>)}
                      {(<p>SAPDelivery#</p>)}
                    </Stack>
                    <Stack distribution="fillEvenly">
                      {this.state.createdOrderData.orderName == null || this.state.createdOrderData.orderName == "" ? (<div></div>) : (<Badge>{this.state.createdOrderData.orderName}</Badge>)}
                      {this.state.createdOrderData.waybillNumber == null || this.state.createdOrderData.waybillNumber == "" ? (<div></div>) : this.state.createdOrderData.waybillNumber.split(",").length == 2 ? (
                        <Stack vertical={true} spacing="tight">
                          <Badge>{this.state.createdOrderData.waybillNumber.split(",")[0]}</Badge>
                          <Badge>{this.state.createdOrderData.waybillNumber.split(",")[1]}</Badge>
                        </Stack>
                      ) : (<Badge>{this.state.createdOrderData.waybillNumber}</Badge>)}
                      {this.state.cancelOrderData.sapOrderNumber == null || this.state.cancelOrderData.sapOrderNumber == "" ? (<div></div>) : (<Badge>{this.state.cancelOrderData.sapOrderNumber}</Badge>)}
                      {this.state.cancelOrderData.sapInvoiceNumber == null || this.state.cancelOrderData.sapInvoiceNumber == "" ? (<div></div>) : (<Badge>{this.state.cancelOrderData.sapInvoiceNumber}</Badge>)}
                      {this.state.cancelOrderData.sapDeliveryNumber == null || this.state.cancelOrderData.sapDeliveryNumber == "" ? (<div></div>) : (<Badge>{this.state.cancelOrderData.sapDeliveryNumber}</Badge>)}
                    </Stack>
                  </div>
                  <br></br>
                </div>
              ) : null}
  
              {Object.values(this.state.customerToCfaData).some(val => val) == true ? (
                <div>
                  <Heading>Order Return</Heading>
                  <br></br>
                    <div>
                      <Stack distribution="equalSpacing">
                        <b>Customer - CFA</b>
                        <Link onClick={() => this.handleOrderDetailsEdit('replaced')}>Edit</Link>
                      </Stack>
                      <br></br>
                      <Stack distribution="fillEvenly">
                        {(<p>Order#</p>)}
                        {(<p>WayBill#</p>)}
                        {(<p>SAPOrder#</p>)}
                        {(<p>SAPInvoice#</p>)}
                        {(<p>SAPDelivery#</p>)}
                      </Stack>
                      <Stack distribution="fillEvenly">
                        {this.state.customerToCfaData.orderName == null || this.state.customerToCfaData.orderName == "" ? (<div></div>) : (<Badge>{this.state.customerToCfaData.orderName}</Badge>)}
                        {this.state.customerToCfaData.waybillNumber == null || this.state.customerToCfaData.waybillNumber == "" ? (<div></div>) : this.state.customerToCfaData.waybillNumber.split(",").length == 2 ? (
                          <Stack vertical={true} spacing="tight">
                            <Badge>{this.state.customerToCfaData.waybillNumber.split(",")[0]}</Badge>
                            <Badge>{this.state.customerToCfaData.waybillNumber.split(",")[1]}</Badge>
                          </Stack>
                        ) : (<Badge>{this.state.customerToCfaData.waybillNumber}</Badge>)}
                        {this.state.customerToCfaData.sapOrderNumber == null || this.state.customerToCfaData.sapOrderNumber == "" ? (<div></div>) : (<Badge>{this.state.customerToCfaData.sapOrderNumber}</Badge>)}
                        {this.state.customerToCfaData.sapInvoiceNumber == null || this.state.customerToCfaData.sapInvoiceNumber == "" ? (<div></div>) : (<Badge>{this.state.customerToCfaData.sapInvoiceNumber}</Badge>)}
                        {this.state.customerToCfaData.sapDeliveryNumber == null || this.state.customerToCfaData.sapDeliveryNumber == "" ? (<div></div>) : (<Badge>{this.state.customerToCfaData.sapDeliveryNumber}</Badge>)}
                      </Stack>
                    </div>
                    <br></br>
                </div>
              ) : null}
  
              {Object.values(this.state.createdOrderData).some(val => val) == true ? (
                <div>
                  <Stack distribution="equalSpacing">
                    <Heading>Order Created</Heading>
                    <Link onClick={() => this.handleOrderDetailsEdit('created')}>Edit</Link>
                  </Stack>
                  <br></br>
                  <Stack distribution="fillEvenly">
                    {(<p>Order#</p>)}
                    {(<p>WayBill#</p>)}
                    {(<p>SAPOrder#</p>)}
                    {(<p>SAPInvoice#</p>)}
                    {(<p>SAPDelivery#</p>)}
                  </Stack>
                  <Stack distribution="fillEvenly" >
                    {this.state.createdOrderData.orderName == null || this.state.createdOrderData.orderName == "" ? (<div></div>) : (<Badge >{this.state.createdOrderData.orderName}</Badge>)}
                    {this.state.createdOrderData.waybillNumber == null || this.state.createdOrderData.waybillNumber == "" ? (<div></div>) : this.state.createdOrderData.waybillNumber.split(",").length == 2 ? (
                      <Stack vertical={true} spacing="tight">
                        <Badge>{this.state.createdOrderData.waybillNumber.split(",")[0]}</Badge>
                        <Badge>{this.state.createdOrderData.waybillNumber.split(",")[1]}</Badge>
                      </Stack>
                    ) : (<Badge>{this.state.createdOrderData.waybillNumber}</Badge>)}
                    {this.state.createdOrderData.sapOrderNumber == null || this.state.createdOrderData.sapOrderNumber == "" ? (<div></div>) : (<Badge>{this.state.createdOrderData.sapOrderNumber}</Badge>)}
                    {this.state.createdOrderData.sapInvoiceNumber == null || this.state.createdOrderData.sapInvoiceNumber == "" ? (<div></div>) : (<Badge>{this.state.createdOrderData.sapInvoiceNumber}</Badge>)}
                    {this.state.createdOrderData.sapDeliveryNumber == null || this.state.createdOrderData.sapDeliveryNumber == "" ? (<div></div>) : (<Badge>{this.state.createdOrderData.sapDeliveryNumber}</Badge>)}
                  </Stack>
                </div>
              ) : (
                <div>
                  <b>Order is Created</b>
                  <br></br>
                </div>)}
            </Modal.Section>
          </Modal>
  
          <Modal
            medium
            title={this.state.modalHeader}
            open={this.state.editModalActive}
            onClose={this.handleOrderDetailsEdit}
            primaryAction={{
              content: "Save",
              onAction: this.handleSaveOrderDetails,
              loading: this.state.editModalIsLoading,
            }}
            secondaryActions={{
              content: "Cancel",
              onAction: this.handleOrderDetailsEdit,
            }}
          >
            <Modal.Section>
              <Stack distribution="fillEvenly">
                <b>WayBill Number</b>
                <TextField
                  value={
                    this.state.editedOrderStatus == "cancel"
                      ? this.state.cancelOrderData.waybillNumber
                      : this.state.editedOrderStatus == "replaced"
                        ? this.state.customerToCfaData.waybillNumber
                        : this.state.createdOrderData.waybillNumber
                  }
                  onChange={(val) =>
                    this.handleOrderDetailsText(val, "waybillNumber")
                  }
                  placeholder="Enter waybill number"
                  autoComplete="off"
                />
              </Stack>
              <br />
              <Stack distribution="fillEvenly">
                <b>SAPInvoice Number</b>
                <TextField
                  value={
                    this.state.editedOrderStatus == "cancel"
                      ? this.state.cancelOrderData.sapInvoiceNumber
                      : this.state.editedOrderStatus == "replaced"
                        ? this.state.customerToCfaData.sapInvoiceNumber
                        : this.state.createdOrderData.sapInvoiceNumber
                  }
                  onChange={(val) =>
                    this.handleOrderDetailsText(val, "sapInvoiceNumber")
                  }
                  placeholder="Enter SAPInvoice number"
                  autoComplete="off"
                />
              </Stack>
              <br />
              <Stack distribution="fillEvenly">
                <b>SAPDelivery Number</b>
                <TextField
                  value={
                    this.state.editedOrderStatus == "cancel"
                      ? this.state.cancelOrderData.sapDeliveryNumber
                      : this.state.editedOrderStatus == "replaced"
                        ? this.state.customerToCfaData.sapDeliveryNumber
                        : this.state.createdOrderData.sapDeliveryNumber
                  }
                  onChange={(val) =>
                    this.handleOrderDetailsText(val, "sapDeliveryNumber")
                  }
                  placeholder="Enter SAPDelivery number"
                  autoComplete="off"
                />
              </Stack>
              <br />
              <Stack distribution="fillEvenly">
                <b>SAPOrder Number</b>
                <TextField
                  value={
                    this.state.editedOrderStatus == "cancel"
                      ? this.state.cancelOrderData.sapOrderNumber
                      : this.state.editedOrderStatus == "replaced"
                        ? this.state.customerToCfaData.sapOrderNumber
                        : this.state.createdOrderData.sapOrderNumber
                  }
                  onChange={(val) =>
                    this.handleOrderDetailsText(val, "sapOrderNumber")
                  }
                  placeholder="Enter SAPOrder number"
                  autoComplete="off"
                />
              </Stack>
              <br />
              <Stack distribution="fillEvenly">
                <b>CFA Number</b>
                <TextField
                  value={this.state.editedOrderStatus == 'cancel' ? this.state.cancelOrderData.cfaNumber :
                    this.state.editedOrderStatus == 'replaced' ? this.state.customerToCfaData.cfaNumber : this.state.createdOrderData.cfaNumber}
                  onChange={(val) => this.handleOrderDetailsText(val, 'cfaNumber')}
                  placeholder="Enter CFA number"
                  autoComplete="off"
                />
              </Stack>
            </Modal.Section>
          </Modal>
  
          <Modal
            medium
            title={this.state.actionsConfirmationHeader}
            open={this.state.actionsConfirmationActive}
            onClose={this.actionsConfirmationClose}
            primaryAction={{
              content: "Yes",
              onAction: this.actionsConfirmationModal,
              loading: this.state.ConfirmationLoadingButton,
            }}
            secondaryActions={{
              content: "No",
              onAction: this.actionsConfirmationClose,
            }}
          >
            <Modal.Section>
              <TextContainer>
                <p>Are you sure you want to do this?</p>
              </TextContainer>
            </Modal.Section>
          </Modal>
  
          <Modal
            medium
            title={this.state.actionsConfirmationHeader}
            open={this.state.actionsConfirmationActive}
            onClose={this.actionsConfirmationClose}
            primaryAction={{
              content: "Yes",
              onAction: this.actionsConfirmationModal,
              loading: this.state.ConfirmationLoadingButton,
            }}
            secondaryActions={{
              content: "No",
              onAction: this.actionsConfirmationClose,
            }}
          >
            <Modal.Section>
              <TextContainer>
                <p>Are you sure you want to export selected orders</p>
              </TextContainer>
            </Modal.Section>
          </Modal>
        </div>
      );
    }
  }
  
  const mapStateToProps = (state) => ({
    orderListAllData: state.order.data,
    loading: state.order.loading,
    error: state.order.error,
    orderResponseData: state.order.orderData,
    orderLoading: state.order.orderLoading,
    cfaListData: state.cfa.cfaData,
    cfaListloading: state.cfa.cfaListloading,
  });
  const mapDispatchToProps = (dispatch) => ({
    getOrdersList: (orderData) => dispatch(getAllFailedOrdersRequest(orderData)),
    pushOrders: (object) => dispatch(pushOrdersRequest(object)),
    submitOrderCFA: (object) => dispatch(submitOrderCFARequest(object)),
    getCFAList: (orderData) => dispatch(getMannualCFAListRequest(orderData)),
    getCFAList: (orderData) => dispatch(getMannualCFAListRequest(orderData)),
    saveOrderDetails: (object) => dispatch(saveOrderDetailsRequest(object)),
    exportFailedOrders: (object) => dispatch(exportOrderFailedRequest(object)),
  });
  
  export default connect(mapStateToProps, mapDispatchToProps)(FailedOrders);
  