import {
    <PERSON>,
    <PERSON>,
    Badge,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    Fi<PERSON>s,
    ChoiceList,
    DataTable,
    SkeletonBodyText,
    Icon,
    Heading,
    Modal,
    TextField,
    Link,
} from "@shopify/polaris";
import React, { Component } from "react";
import { connect } from "react-redux";
import { getAllGstOrdersRequest, actionGstOrderRequest } from "../../redux/gstOrders/gstOrdersActions";
import { checkNullOrUndefined, dateConversion, disambiguateLabel, gstValid, isEmpty } from "../../helpers/appHelpers";
require('./gstOrders.css');
import { debounce } from "lodash";
import { SearchMajor } from '@shopify/polaris-icons';
import BillingModal from "./billingModal";

class GstOrders extends Component {
    constructor(props) {
        super(props);
        this.state = {
            filters: {
                search: "",
                gstValid: [],
            },
            modalActive: false,
            editedGst: '',
            billingModalActive: false,
            actionLoading: false
        }
    }
    componentDidMount() {
        this.getHoldGstOrders()
    }
    getHoldGstOrders = () => {
        let { getGstOrdersList } = this.props;
        getGstOrdersList({
            request: { filter: this.state.filters },
            callback: () => { },
        });
    }

    debounceEvent(...args) {
        this.debounceEvent = debounce(...args);
        return (e) => {
            e.perstist();
            return this.debounceEvent(e);
        };
    }

    callActionOfOrders = async (orderName, type, billingData = null, index = '') => {
        this.setState({ actionLoading: type + index })
        let { callActionOnOrders } = this.props;
        await new Promise((resolve, reject) => {
            callActionOnOrders({
                request: { orderName, type, editedGstNo: this.state.editedGst, billingData },
                callback: () => {
                    if (!this.props.error) {
                        this.setState({ editedGstNo: false, modalActive: false })
                        this.getHoldGstOrders()
                    }
                    this.setState({ actionLoading: false })
                    resolve()
                },
            })

        })
        return;
    }

    handleBillingModal = (val) => {
        this.setState({ billingModalActive: val })
    }

    orderListMarkup = () => {
        const { loading, gstOrderListAllData } = this.props;

        let rowMarkup = [];
        const dataObj = gstOrderListAllData?.data?.data;
        const storeDomain = gstOrderListAllData?.data?.shop?.split(".myshopify.com")[0];
        if (!loading) {
            if (!isEmpty(dataObj)) {
                rowMarkup = dataObj.map(({ shopify_order_name, gstin, is_gst_Valid, order_created_at, billing_details, shopify_order_id }, index) => {
                    let billingData = billing_details && typeof billing_details == 'string' ? JSON.parse(billing_details) : billing_details
                    let gstStatus = is_gst_Valid == '1' ? 'Valid' : is_gst_Valid =='2' ? 'Invalid State' : 'Invalid' 
                    let gstBadgeStatus = is_gst_Valid == '1' ? 'success' : 'attention'
                    return ([
                        <Link removeUnderline url={`https://admin.shopify.com/store/${storeDomain}/orders/${shopify_order_id}`} >{shopify_order_name}</Link>,
                        gstin || '-',
                        dateConversion(order_created_at),
                        checkNullOrUndefined(billingData?.billing_company),
                        <Badge size="small" status={gstBadgeStatus}>{gstStatus}</Badge>,
                        <Button plain onClick={() => {
                            let obj = { ...billingData, shopify_order_id }
                            this.handleBillingModal(obj)
                        }} size="slim">Edit</Button>,
                        <Stack spacing="extraTight">
                            {is_gst_Valid == '1' ? '' : <Button onClick={() => this.handleModalAction(shopify_order_id)} size="slim">Edit GST</Button>}
                            {is_gst_Valid == '1'? <Button loading={this.state.actionLoading == 'Approved' + index} onClick={() => this.callActionOfOrders(shopify_order_id, "Approved", '', index)} size="slim">Approve</Button> : ''}
                            <Button loading={this.state.actionLoading == 'Rejected' + index} onClick={() => this.callActionOfOrders(shopify_order_id, "Rejected", '', index)} size="slim">Reject</Button>
                        </Stack>
                    ])
                })
            } else {
                return (
                    <div className="failed_search_div">
                        <Icon source={SearchMajor} color="base" />
                        <Heading element="h1">No GST orders found</Heading>
                    </div>);
            }
        } else {
            for (let i = 0; i <= 5; i++) {
                rowMarkup.push([
                    <SkeletonBodyText lines={1} />,
                    <SkeletonBodyText lines={1} />,
                    <SkeletonBodyText lines={1} />,
                    <SkeletonBodyText lines={1} />,
                    <SkeletonBodyText lines={1} />,
                ]);
            }
        }

        return (
            <DataTable
                columnContentTypes={[
                    'text',
                    'text',
                    'text',
                    'text',
                    'text',
                    'text',
                ]}
                headings={[
                    'Shopify Order',
                    'GST',
                    'Date',
                    'Company Name',
                    'GST Status',
                    'Billing',
                    'Action',
                ]}
                rows={rowMarkup}
                totalsName={{
                    singular: 'Total net sales',
                    plural: 'Total net sales',
                }}
            />

        );
    };

    handleFilterChange = (value, setter) => {
        let filterObj = { ...this.state.filters }
        filterObj[setter] = value
        this.setState({ filters: { ...filterObj } }, () => {
            setter == 'search' ? this.debounceEvent(() => {
                this.getHoldGstOrders()
            }, 300) : this.getHoldGstOrders()
        })
    };

    handleModalAction = (modalActiveVal) => {
        this.setState({ modalActive: modalActiveVal, editedGst: "" })
    }

    render() {
        const filtersArr = [{
            key: "isGstValid",
            label: "GST Status",
            filter: (
                <ChoiceList
                    choices={gstValid}
                    selected={this.state.filters.gstValid || []}
                    onChange={(val) => this.handleFilterChange(val, "gstValid")}
                />
            ),
            shortcut: true,
        }]

        const appliedFiltersArr = []
        if (!isEmpty(this.state.filters.gstValid)) {
            const key = "isGstValid";
            appliedFiltersArr.push({
                key,
                label: disambiguateLabel(key, this.state.filters.gstValid),
                onRemove: () => this.handleFilterChange([], "gstValid"),
            });
        }

        return (
            <Page fullWidth title="GST Hold Orders">
                <Card sectioned>
                    <Filters
                        queryPlaceholder="Search by order"
                        filters={filtersArr}
                        queryValue={this.state.filters.search}
                        appliedFilters={appliedFiltersArr}
                        onQueryChange={(val) => this.handleFilterChange(val, "search")}
                        onQueryClear={() => this.handleFilterChange('', "search")}
                    />
                    {this.orderListMarkup()}
                </Card>
                <Modal
                    small
                    title={"Edit GST"}
                    open={this.state.modalActive}
                    onClose={() => this.handleModalAction(false)}
                    primaryAction={{
                        content: 'Edit',
                        onAction: () => this.callActionOfOrders(this.state.modalActive, 'Edit'),
                        loading: this.props.modalBtnLoading,
                        disabled: this.state.editedGst == ''
                    }}
                    secondaryActions={{
                        content: 'Cancel',
                        onAction: () => this.handleModalAction(false)
                    }}
                >
                    <Modal.Section>
                        <TextField
                            label="Add GST No."
                            value={this.state.editedGst}
                            onChange={(val) => this.setState({ editedGst: val })}
                            clearButton
                            onClearButtonClick={() => this.setState({ editedGst: "" })}
                            autoComplete="off"
                        />
                    </Modal.Section>
                </Modal>
                {this.state.billingModalActive != false ?
                    <BillingModal modalBtnLoading={this.props.modalBtnLoading} getHoldGstOrders={this.getHoldGstOrders} billingModalActive={this.state.billingModalActive} handleBillingModal={this.handleBillingModal} callActionOfOrders={this.callActionOfOrders} />
                    : ""}
            </Page >
        )
    }
}
const mapStateToProps = (state) => {
    return ({
        gstOrderListAllData: state.gstOrders.data,
        loading: state.gstOrders.loading,
        error: state.gstOrders.error,
        modalBtnLoading: state.gstOrders.modalBtnLoading
    })
};

const mapDispatchToProps = (dispatch) => ({
    getGstOrdersList: (orderData) => dispatch(getAllGstOrdersRequest(orderData)),
    callActionOnOrders: (orderData) => dispatch(actionGstOrderRequest(orderData)),
});

export default connect(mapStateToProps, mapDispatchToProps)(GstOrders);
