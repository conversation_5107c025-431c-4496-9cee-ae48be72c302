stages:
  - pre
  - build_and_package
  - deploy

# Include staging.yml for staging environment (feature/cicd-setup branch)
include:
  - local: ci/staging.yml
    rules:
      - if: '$CI_COMMIT_BRANCH == "feature/extended-warranty"'
  - local: ci/production.yml
    rules:
      - if: '$CI_COMMIT_BRANCH == "production-v1-2025"'

# Debug job to confirm pipeline triggers
debug_test:
  stage: pre
  script:
    - echo "Pipeline is running on branch $CI_COMMIT_BRANCH"
    - ls -l ci/ # Debug: Check if staging.yml and production.yml exist