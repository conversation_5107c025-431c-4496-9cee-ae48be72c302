const dbConnection = require("../models");
const shopConnection = dbConnection.shop;
const cfaStockMapping = dbConnection.cfaStockMapping;
const fulfillmentServiceTable = dbConnection.fulfillmentService;
const childSku = dbConnection.childSku;
const CONFIG = require('../config/config_' + [process.env.NODE_ENV || 'local'] + '.json');
const Fulfillment_Service = require('../shopifyObjects/FulfillmentService');
const { Op } = require("sequelize");


exports.fulfillmentService = async (shop) => {
    try {
        // const { shop } = req.query;
        // if (!req.query.shop) {
        //     return res.status(CONFIG.status.FAILED).send({ err: CONFIG.msg.PARAMETER });
        // }
        const shopResponse = await shopConnection.findOne({
            where: { myshopify_domain: shop }
        });
        if (!shopResponse) {
            return res.status(CONFIG.status.FAILED).send({ err: CONFIG.msg.NO_DATA });
        }
        const token = shopResponse.dataValues.token;
        const call_fulfillment_service = new Fulfillment_Service(shop, token);
        const getFulfillmentService = await call_fulfillment_service.get();
        console.log("getFulfillmentService-->",getFulfillmentService)
        const fulfillmentService = getFulfillmentService.filter(x => x.name === CONFIG.fulfillment_service.name && x.callback_url === CONFIG.fulfillment_service.callback_url);
        if (fulfillmentService.length != 0) {
            console.log("Already present")
            // res.status(CONFIG.status.SUCCESS).send("Already present");
        } else {
            const call_Fulfillment_Service = new Fulfillment_Service(shop, token);
            const createfulfillmentService = await call_Fulfillment_Service.create(CONFIG.fulfillment_service);
            let fulfillmentServiceObj = {
                fulfillment_service_id: createfulfillmentService.fulfillment_service.id,
                service_name: createfulfillmentService.fulfillment_service.name,
                location_id: createfulfillmentService.fulfillment_service.location_id
            }
            await fulfillmentServiceTable.create(fulfillmentServiceObj);
            console.log("fulfilment service created")
            // res.status(CONFIG.status.SUCCESS).send(CONFIG.msg.CREATE);
        }
    } catch (error) {
        console.log('fufilment service error',error)
        // res.status(CONFIG.status.FAILED).send(CONFIG.msg.ERROR);
    }
}

exports.fetchStock = async (req, res) => {
    try {
        console.log("shopify req sku------",req.query.sku)
        let skuValue = req.query.sku, inventoryLevel = {};
        let activeCfa = await dbConnection.cfaPlantLocation.findAll({
            where: {
              is_active: 1
            },
            attributes: ['plant_code']
          });
        let activePlantCodes = activeCfa.map(plan => plan.plant_code);
        if (skuValue === undefined) {
            const totalStock = await cfaStockMapping.findAll({ where: { pincode_group: { [Op.in]: activePlantCodes } },
                attributes: [
                    'sku',
                    [cfaStockMapping.sequelize.fn('sum', cfaStockMapping.sequelize.col('local_stock')), 'total_stock']
                ],
                group: ['sku'],
            });
            for (let i = 0; i < totalStock.length; i++) {
                    inventoryLevel[totalStock[i].dataValues.sku] = parseInt(totalStock[i].dataValues.total_stock);
            }
            res.status(CONFIG.status.SUCCESS).send(JSON.stringify(inventoryLevel));
        } else {
            let skuValues = skuValue.split(",");
            for (let i = 0; i < skuValues.length; i++) {
                let stockValueArray = [];
                    stockValueArray = await cfaStockMapping.findAll({
                        attributes: [
                            'sku',
                            [cfaStockMapping.sequelize.fn('sum', cfaStockMapping.sequelize.col('local_stock')), 'total_stock']
                        ],
                        where: { sku: skuValues[i] ,pincode_group: { [Op.in]: activePlantCodes }}
                    })
                stockValueArray.map((arr) => {
                    if (arr.dataValues.sku == null) {
                        inventoryLevel[skuValues[i]] = 0;
                    } else {
                        inventoryLevel[arr.dataValues.sku] = parseInt(arr.dataValues.total_stock);
                    }
                })
            }
            console.log("stockArray==>",JSON.stringify(inventoryLevel))
            res.status(CONFIG.status.SUCCESS).send(JSON.stringify(inventoryLevel));
        }
    } catch (error) {
        res.status(CONFIG.status.FAILED).send(CONFIG.msg.ERROR);
    }
}