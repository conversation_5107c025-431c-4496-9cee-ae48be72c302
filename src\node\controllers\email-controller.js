const dbConnection = require("../models");
const nodemailer = require('nodemailer');
const CONFIG = require('../config/config_' + [process.env.NODE_ENV || 'local'] + '.json');
const { QueryTypes } = require('sequelize');

exports.sendEmailToSymphonyByApi = async (req, res) => {
    const { orderName, orderStatus } = req.body
    await this.sendEmailToSymphony(orderName, orderStatus)
    res.send({ "message": "success" })
}

exports.sendEmailToCustomerByApi = async (req, res) => {
    const { orderName, invoiceUrl } = req.body
    await this.sendEmailToCustomer(orderName, invoiceUrl)
    res.send({ "message": "success" })
}

exports.sendEmailToSymphony = async (orderName, orderStatus) => {
    return new Promise(async (resolve, reject) => {
        if (orderName && orderStatus) {
            let transporter = nodemailer.createTransport({
                host: CONFIG.email.host,
                port: CONFIG.email.port,
                secure: false, // true for 465, false for other ports
                auth: {
                    user: CONFIG.email.username,
                    pass: CONFIG.email.password
                },
                tls: {
                    rejectUnauthorized: false
                },
            });
            let mailOptions = {
                subject: 'Order status',
                text: `Hello,\nOrder ${orderName} is ${orderStatus} by customer!!`,
                to: CONFIG.email.to,
                from: CONFIG.email.from,
            };
            transporter.sendMail(mailOptions, function (error, info) {
                if (error) {
                    console.log("Email Errorrrr=>", error);
                    resolve()
                } else {
                    console.log("mail send")
                    resolve()
                }
            });
        } else {
            console.log("OrderId/orderStatus Not found")
            resolve()
        }
    })
}

exports.sendEmailToCustomer = async (orderName, invoiceUrl) => {
    return new Promise(async (resolve, reject) => {
        if (orderName && invoiceUrl) {
            let queryStr = `SELECT * FROM order_item_splits INNER JOIN order_customers ON order_item_splits.shopify_order_id = order_customers.order_id WHERE order_item_splits.order_name = '${orderName}';`
            let queryData = await dbConnection.sequelize.query(queryStr, { type: QueryTypes.SELECT })
            if (queryData.length > 0) {
                let customerEmail = queryData[0].customer_email;
                // let customerEmail = "<EMAIL>";
                if (customerEmail != null && customerEmail != 'null' && customerEmail != '') {
                    let transporter = nodemailer.createTransport({
                        host: CONFIG.email.host,
                        port: CONFIG.email.port,
                        secure: false, // true for 465, false for other ports
                        auth: {
                            user: CONFIG.email.username,
                            pass: CONFIG.email.password
                        },
                        tls: {
                            rejectUnauthorized: false
                        },
                    });
                    let cusName = queryData[0].first_name ? queryData[0].first_name : 'Customer'
                    let mailOptions = {
                        subject: 'Symphony-Order Invoice',
                        text: `Dear ${cusName},\nThank you for Shopping!\n\nKindly find attached Invoice for your Order ${orderName}.\n\nBest,\nSymphony Limited`,
                        to: `${customerEmail}`,
                        from: CONFIG.email.from,
                        attachments: [
                            {
                                filename: `Invoice_${orderName}.pdf`,
                                contentType: 'application/pdf',
                                path: `${invoiceUrl}`
                            }]
                    };
                    transporter.sendMail(mailOptions, async function (error, info) {
                        if (error) {
                            console.log("Email Errorrrr=>", error);
                            resolve()
                        } else {
                            console.log('customer mail send')
                            await dbConnection.orderItemSplit.update({
                                is_send_mail: '1'
                            }, {
                                where: {
                                    order_name: orderName,
                                }
                            });
                            resolve()
                        }
                    });
                } else {
                    console.log("invoiceUrl/orderName Not found")
                    resolve()
                }
            } else {
                console.log("data Not found")
                resolve()
            }
        } else {
            console.log("invoiceUrl/orderName Not found")
            resolve()
        }
    })
}

exports.sendExportDataEmail = async (exportFileName, exportFileUrl, email, cc_mail, subject) => {
    return new Promise(async (resolve, reject) => {
        let transporter = nodemailer.createTransport({
            host: CONFIG.email.host,
            port: CONFIG.email.port,
            secure: false, // true for 465, false for other ports
            auth: {
                user: CONFIG.email.username,
                pass: CONFIG.email.password
            },
            tls: {
                rejectUnauthorized: false
            },
        });
        let mailOptions = {
            subject: subject,
            to: email,
            from: CONFIG.email.from,
            attachments: [
                {
                    filename: `${exportFileName}`,
                    contentType: 'application/xlsx',
                    path: `${exportFileUrl}`
                }]
        };
        if (cc_mail) mailOptions.cc = cc_mail

        transporter.sendMail(mailOptions, function (error, info) {
            if (error) {
                console.log("Email Errorrrr=>", error);
                resolve()
            } else {
                console.log("mail send")
                resolve()
            }
        });
    })
}

exports.escalationEmailSend = async (data) => {
    return new Promise(async (resolve, reject) => {
        if (data) {
            let email = data.email_id ? `Customer email : ${data.email_id}\n` : "";
            let feedbackType = data.feedback_type ? `Feedback Type : ${data.feedback_type}\n` : "";
            let feedback = data.feedback ? `Feedback : ${data.feedback}` : "";
            let customerName = data.name ? `Name : ${data.name}\n` : "";
            let transporter = nodemailer.createTransport({
                host: CONFIG.email.host,
                port: CONFIG.email.port,
                secure: false, // true for 465, false for other ports
                auth: {
                    user: CONFIG.email.username,
                    pass: CONFIG.email.password
                },
                tls: {
                    rejectUnauthorized: false
                },
            });
            let mailOptions = {
                subject: `Escalation Information For Complaint No : ${data.complaint_number}`,
                text: `Hello Team,\n\n${customerName}${email}Phone Number : ${data.phone}\nEscalation Type : ${data.escalation_type}\n${feedbackType}${feedback}\n\nThank You`,
                to: CONFIG.email.escalation_to,
                from: CONFIG.email.escalation_from,
            };
            transporter.sendMail(mailOptions, async function (error, info) {
                if (error) {
                    console.log("Email Errorrrr=>", error);
                    resolve({ status: false })
                } else {
                    if (data.feedback_type != null && data.feedback_type != "null" && data.feedback_type != "" && data.email_id != '') {
                        await escalationEmailToCustomer(data.email_id, data.feedback_type);
                    } else if (data.email_id != '') {
                        await thankYouEmailToCustomer(data.email_id)
                    }
                    console.log("mail send")
                    resolve({ status: true })
                }
            });
        } else {
            console.log("complaintId Not found")
            resolve({ status: false })
        }
    })
}

let escalationEmailToCustomer = (email, feedbackType) => {
    return new Promise(async (resolve) => {
        let transporter = nodemailer.createTransport({
            host: CONFIG.email.host,
            port: CONFIG.email.port,
            secure: false, // true for 465, false for other ports
            auth: {
                user: CONFIG.email.username,
                pass: CONFIG.email.password
            },
            tls: {
                rejectUnauthorized: false
            },
        });
        let mailOptions = {
            subject: `Grievance against: ${feedbackType}`,
            text: `Dear Symphony customer,\n\nWe acknowledge receipt of your complaint regarding ${feedbackType}. Our team will shortly contact you for resolution.\n\nBest,\nSymphony Limited`,
            to: email,
            from: CONFIG.email.escalation_from,

        };
        transporter.sendMail(mailOptions, function (error, info) {
            if (error) {
                console.log("Email Errorrrr=>", error);
                resolve()
            } else {
                console.log("mail send")
                resolve()
            }
        });
    })
}
let thankYouEmailToCustomer = (emailId) => {
    return new Promise(async (resolve) => {
        let transporter = nodemailer.createTransport({
            host: CONFIG.email.host,
            port: CONFIG.email.port,
            secure: false,
            auth: {
                user: CONFIG.email.username,
                pass: CONFIG.email.password
            },
            tls: {
                rejectUnauthorized: false
            },
        });
        let mailOptions = {
            subject: 'Symphony',
            text: `Dear Symphony customer,\n\nThank you for providing your valuable feedback.\n\nRegards,\nSymphony Limited`,
            to: emailId,
            from: CONFIG.email.escalation_from,

        };
        transporter.sendMail(mailOptions, function (error) {
            if (error) {
                console.log("thankYouEmailToCustomer Error==>", error);
                resolve();
            } else {
                console.log("thankYouEmailToCustomer mail send");
                resolve();
            }
        });
    })
}