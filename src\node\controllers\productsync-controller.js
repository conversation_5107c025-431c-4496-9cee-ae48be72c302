const dbConnection = require("../models");
const cfaStockMapping = dbConnection.cfaStockMapping;
const { default: ShopifyApi } = require('@shopify/shopify-api');
const CONFIG = require('../config/config_' + [process.env.NODE_ENV || 'local'] + '.json');
const response = require('../helper/appResponse')
const logger = require('../helper/appLoger');
const { default: axios } = require("axios");
exports.syncProductByApi=(req,res)=>{
  this.syncProduct(req.query.shop)
  logger.info("info===>productSync"+req.query.shop)
  res.status(200).send({data:CONFIG.status.SUCCESS});

}
exports.syncProduct = async (shop) => {
  let successMessage = response.responseMessage(CONFIG.status.FAILED, CONFIG.msg.FAILED)
  try {
    const myShopifyDomain = shop;
    if (myShopifyDomain) {
      let shopResponse = await dbConnection.shop.findOne({ where: { myshopify_domain: myShopifyDomain } })
      if (shopResponse) {
        const client = new ShopifyApi.Clients.Rest(myShopifyDomain, shopResponse.token);
        await this.productSync(client, shopResponse.id)
        // await this.productSync(myShopifyDomain, shopResponse.token)
        successMessage.status = CONFIG.status.SUCCESS
        successMessage.message = CONFIG.msg.SUCCESS
      } else {
        successMessage.status = CONFIG.status.FAILED
        successMessage.message = CONFIG.msg.NO_DATA
      }
    } else {
      successMessage.status = CONFIG.status.FAILED
      successMessage.message = CONFIG.msg.PARAMETER
    }
  } catch (err) {
    logger.error("err===>productSync"+err)
    successMessage.status = CONFIG.status.ERROR
    successMessage.message = CONFIG.msg.ERROR
  }
};

exports.productSync = async (shopify, store_id) => {
  try {
    let params = { path: 'products', query: { limit: 250 } };
    do {
      const allProducts = await shopify.get(params);
      var products = allProducts.body.products
      for (let product of products) {

        await shopifyProducts(store_id, product)
        await shopifyProductVariant(store_id, product)
        await shopifyProductImage(store_id, product)
      }
      params = allProducts.pageInfo.nextPage;
    } while (params !== undefined);
  } catch (error) {
    logger.error("productSync===>"+error)
  }
}


let shopifyProducts = async (store_id, product) => {
  let dataObj = {
    product_title: product.title,
    description: product.body_html,
    handle: product.handle,
    published_at: product.published_at,
    store_id: store_id,
    shopify_product_id: product.id,
    product_type: product.product_type,
    tags: product.tags,
    product_created_at: product.created_at,
    product_updated_at: product.updated_at,
  }
  let productResponse = await dbConnection.product.findOne({ where: { shopify_product_id: product.id } })
  if (productResponse != null) {
    await dbConnection.product.update(dataObj, { where: { shopify_product_id: product.id } }).then(response=>{
    }).catch(err=>{
      console.log("catch  err==>",err)
    })
  } else {
    await dbConnection.product.create(dataObj).then(response=>{
    }).catch(err=>{
      console.log("catch  err==>",err)
    })
  }
}

let shopifyProductVariant = async (store_id, product) => {
  for (let variant of product.variants) {
    let productTableData = await dbConnection.product.findOne({ where: { store_id: store_id, shopify_product_id: product.id } })
    let dataObj = {
      sku: variant.sku,
      store_id: store_id,
      grams: variant.grams,
      title: variant.title,
      weight: variant.weight,
      barcode: variant.barcode,
      option_value_1: variant.option1,
      option_value_2: variant.option2,
      option_value_3: variant.option3,
      option_1: variant.option1 ? product.options[0].name : null,
      option_2: variant.option2 ? product.options[1].name : null,
      option_3: variant.option3 ? product.options[2].name : null,
      taxable: variant.taxable,
      position: variant.position,
      product_id: productTableData.id,
      price:variant.price,
      weight_unit: variant.weight_unit,
      compared_at_price: variant.compare_at_price,
      inventory_policy: variant.inventory_policy,
      inventory_item_id: variant.inventory_item_id,
      requires_shipping: variant.requires_shipping,
      inventory_quantity: variant.inventory_quantity,
      fulfillment_service: variant.fulfillment_service,
      inventory_managment: variant.inventory_managment,
      shopify_variant_id: variant.id
    }
    if (variant.sku) {
      await createSkuPincode(variant.sku, store_id)
    }
    let productResponse = await dbConnection.productVariant.findOne({ where: { product_id: dataObj.product_id, shopify_variant_id: variant.id } })
    if (productResponse != null) {
      await dbConnection.productVariant.update(dataObj, { where: { product_id: dataObj.product_id, shopify_variant_id: variant.id } }).then(response=>{
      }).catch(err=>{
        console.log("catch variant err==>",err)
      })
    } else {
      await dbConnection.productVariant.create(dataObj).then(response=>{
      }).catch(err=>{
        console.log("catch variant err==>",err)
      })
    }
  }
}

let createSkuPincode = async (sku, storeId) => {
  let plantCode = await dbConnection.cfaPlantLocation.findAll({ where: { store_id: storeId } })
  for (element of plantCode) {
    let skuObj = {
      store_id: storeId,
      sku: sku,
      pincode_group: element.plant_code
    }
    let skuData=await dbConnection.cfaStockMapping.findOne({where:{sku: sku,pincode_group: element.plant_code}})
    if(skuData){
      await dbConnection.cfaStockMapping.update(skuObj,{where:{sku: sku,pincode_group: element.plant_code}})
    }else{
      await dbConnection.cfaStockMapping.create(skuObj)
    }
    
  }
}

let shopifyProductImage = async (store_id, product) => {
  for (let image of product.images) {
    let productTableData = await dbConnection.product.findOne({ where: { store_id: store_id, shopify_product_id: product.id } })
    let dataObj = {
      store_id: store_id,
      product_id: productTableData.id,
      image_id: image.id,
      image_url: image.src,
    }
    let productResponse = await dbConnection.productImage.findOne({ where: { product_id: dataObj.product_id, image_id: image.id } })
    if (productResponse != null) {
      await dbConnection.productImage.update(dataObj, { where: { product_id: dataObj.product_id, image_id: image.id } }).then(response=>{
      }).catch(err=>{
        console.log("catch image err==>",err)
      })
    } else {
      await dbConnection.productImage.create(dataObj).then(response=>{
      }).catch(err=>{
        console.log("catch image err==>",err)
      })

    }
  }
}