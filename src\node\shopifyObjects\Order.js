const axios = require('axios');

module.exports = class Orders {
  constructor(shop, token, orderId) {
    this.options = {
      headers: {
        'X-Shopify-Access-Token': token,
        'Content-Type': 'application/json',
      },
    };
    this.shop = shop;
    this.orderId = orderId;
    this.path = `https://${shop}/admin/api/2025-01/orders/${orderId}.json`;
  }

  // GET request to retrieve the order details
  get = () => {
    return new Promise(async (resolve, reject) => {
      try {
        const res = await axios.get(this.path, this.options);
        resolve({ data: res.data.order });
      } catch (err) {
        console.log("get tag Error:", err);
        resolve({ status: err.response.status, error: err.response.data });
      }
    });
  }

  // PUT request to update the order (e.g., adding a new tag)
  update = (tags) => {
    return new Promise(async (resolve, reject) => {
      try {
        // First, get the current tags
        const currentOrder = await this.get();
        let currentTags = currentOrder?.data?.tags || '';

        // Append new tags if there are any
        if (currentTags) {
          tags = `${currentTags}, ${tags}`;
        }

        // Create the request body for the PUT request
        const updateData = {
          order: {
            id: this.orderId,
            tags: tags,
          },
        };

        // Perform the PUT request to update the tags
        const res = await axios.put(this.path, updateData, this.options);
        resolve({ data: res.data.order }); // Returning the updated order data
      } catch (err) {
        console.log("update Order (tag add) Error:", err);
        resolve({ status: err.response.status, error: err.response.data });
      }
    });
  }
}
