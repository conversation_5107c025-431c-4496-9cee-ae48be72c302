const axios = require('axios');

module.exports = class Orders {
  constructor(shop, token, orderId) {
    this.options = {
      headers: {
        'X-Shopify-Access-Token': token,
        'Content-Type': 'application/json',
      },
    };
    this.shop = shop;
    this.orderGid = `gid://shopify/Order/${orderId}`;
    this.path = `https://${shop}/admin/api/2025-07/graphql.json`;
  }

  // PUT request to update the order (e.g., adding a new tag)
  update = (tags) => {
    return new Promise(async (resolve, reject) => {
      const mutation = `
        mutation tagsAdd($id: ID!, $tags: [String!]!) {
          tagsAdd(id: $id, tags: $tags) {
            node {
              ... on Order {
                id
                tags
              }
            }
            userErrors {
              field
              message
            }
          }
        }`;
      
      // The tagsAdd mutation expects an array of strings.
      const tagsArray = tags.split(',').map(tag => tag.trim()).filter(tag => tag);

      const variables = {
        id: this.orderGid,
        tags: tagsArray,
      };

      try {
        const res = await axios.post(this.path, { query: mutation, variables }, this.options);

        if (res.data.errors || res.data.data.tagsAdd.userErrors.length > 0) {
          const errors = res.data.errors || res.data.data.tagsAdd.userErrors;
          throw new Error(JSON.stringify(errors));
        }
        resolve({ data: res.data.data.tagsAdd.node });
      } catch (err) {
        console.log("update Order (tag add) Error:", err.message);
        const status = err.response ? err.response.status : 500;
        const error = err.response ? err.response.data : { message: err.message };
        resolve({ status, error });
      }
    });
  }
}
