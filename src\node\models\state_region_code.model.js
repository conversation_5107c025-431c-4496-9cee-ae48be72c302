const Sequelize = require("sequelize");
module.exports = (sequelize) => {
  return sequelize.define("state_region_codes", {
    id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      unique: true
    },
    state: {
      type: Sequelize.STRING,
    },
    new_state: {
      type: Sequelize.STRING
    },
    region_code: {
      type: Sequelize.STRING
    }
  });
};