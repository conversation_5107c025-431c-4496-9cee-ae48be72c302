const Sequelize = require("sequelize");
module.exports = (sequelize) => {
  return sequelize.define("city_state_crm_pincode", {
    id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      unique: true
    },
    store_id: {
      allowNull:false,
      type: Sequelize.INTEGER,
      references: {
        model: 'stores',
        key: 'id'
      },
    },
    city: {
      type: Sequelize.STRING,
    },
    city_code: {
      type: Sequelize.INTEGER,
    },
    state: {
      type: Sequelize.STRING
    },
    state_code: {
      type: Sequelize.INTEGER,
    },
    area : {
      type: Sequelize.STRING
    },
    area_code : {
      type: Sequelize.STRING
    },
    pincode : {
      type: Sequelize.STRING
    }
  },
    {
      indexes: [
        {
          unique: true,
          fields: ['store_id', 'city_code','state_code','area_code']
        }
      ]
    });
};