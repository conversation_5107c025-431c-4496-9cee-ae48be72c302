const dbConnection = require("../../models");
const CONFIG = require('../../config/config_' + [process.env.NODE_ENV || 'local'] + '.json');
const axios = require("axios");
const orderHistoryHelper = require('../../helper/orderHistory')
const { Op } = require('sequelize');
let base64 = require('base-64');
const { getPaymentId } = require('../razorpay-controller');
const FulfillmentOrder = require("../../shopifyObjects/FullfillmentOrder");
const Fulfillment = require("../../shopifyObjects/Fulfillment");

exports.createOrderOnSAPforWarranty = async (orderName, isReverse = false) => {
  try {
    console.log('createOrderOnSAPforWarranty========>', orderName)
    // Fetch order data with necessary relations
    let orderData = await dbConnection.extendedWarrantyOrder.findOne({
      where: { main_order_name: orderName, middleware_status: ["PENDING", "FAILED"] },
      include: [
        { model: dbConnection.orderItemSplit, as: "orderItemSplit", required: true },
        { model: dbConnection.orderCustomer, as: "orderCustomers", required: true },
        { model: dbConnection.orderAddress, as: "orderAddresses", required: true },
      ],
      raw: true,
      nest: true,
    });

    if (!orderData) return;
    const { orderItemSplit } = orderData;

    if (orderData?.checkout_id == null) {
      let shopResponse = await dbConnection.shop.findOne({ where: { id: orderItemSplit?.store_id }, raw: true })
      const payId = await getPaymentId(shopResponse.myshopify_domain, shopResponse.token, orderData.shopify_order_id)
      if (payId.status) {
        let orderObj = {}
        if (payId.payId) {
          orderData.checkout_id = payId.payId
          orderObj.checkout_id = payId.payId
        }
        if (payId?.gateway == 'SNAPMINT') {
          orderData.gateway = 'SNAPMINT'
          orderObj.gateway = 'SNAPMINT'
        }
        if (Object.keys(orderObj).length > 0) {
          await dbConnection.extendedWarrantyOrder.update(orderObj, { where: { order_name: orderData.order_name } })
        }
      }
    }

    let sapParam = {
      orderData,
      rate: 0,
    };

    // Handle GST and update Customer Master
    if (orderItemSplit?.gstin && orderItemSplit.gstin != null && orderItemSplit.gstin != "null" && orderItemSplit?.is_gst_Valid === '1') {
      const gstRes = await pushToCustomerMaster(orderData);
      if (gstRes?.status) {
        sapParam.CustomerNum = gstRes.data.CustomerNum;
        sapParam.Gstin = orderItemSplit?.gstin;
      }
    }

    // Prepare and push SAP request
    const reqSapData = await this.getSapReqBody(sapParam);
    const sapData = await this.callOnSap(reqSapData.data);

    // Insert into order history
    await orderHistoryHelper.insertOrderHistory({
      shopify_order_id: orderData.shopify_order_id,
      shopify_order_name: orderData.shopify_order_name,
      order_name: orderData.order_name,
      platform_type: isReverse ? "SAP-extended-order-Reverse" : "SAP-extended-order",
      request_data: JSON.stringify(reqSapData.data),
      response_status_code: sapData.status_code,
      response_data: JSON.stringify(sapData.data || sapData),
    });

    // Prepare SAP log object
    const sapObj = {
      store_id: orderItemSplit?.store_id,
      order_id: orderData.shopify_order_id,
      line_item_id: orderData.line_item_id,
      order_name: orderData.order_name,
    };
    if (isReverse) sapObj.is_cancel = "1";

    // Define SAP log search options
    const sapOptions = { order_name: orderData.order_name };
    if (isReverse) {
      sapOptions[Op.or] = [
        { is_cancel: { [Op.or]: '1' } },
        { is_return: { [Op.or]: '1' } },
      ];
    }

    const sapDataRes = await dbConnection.sapLog.findOne({ where: sapOptions });

    // Update or create SAP log and order status based on SAP response
    const failureMessage = sapData?.data?.MESSAGE?.[0]?.MESSAGE || "No response from SAP";
    const updateData = sapData.status
      ? {
        sap_order_number: sapData.data.SAP_SO_NUM,
        sap_status: "pushed",
        middleware_status: isReverse ? 'REVERSE ORDER PROCESSING' : "PROCESSING",
      }
      : {
        sap_status: "failed",
        middleware_status: 'FAILED',
        failed_reason: failureMessage,
      };

    sapObj.response_message = failureMessage;
    if (sapData.status) {
      Object.assign(sapObj, {
        sap_order_number: sapData.data.SAP_SO_NUM,
        shopify_order_name: sapData.data.SHOPIFY_NUM,
      });
    }

    if (sapDataRes) {
      await dbConnection.sapLog.update(sapObj, { where: { order_name: orderData.order_name } });
    } else {
      await dbConnection.sapLog.create(sapObj);
    }

    await dbConnection.extendedWarrantyOrder.update(updateData, { where: { order_name: orderData.order_name } });
  } catch (error) {
    console.error("Error in createOrderOnSAP:", error);
  }
};

exports.callOnSap = (data) => {
  return new Promise(async (resolve, reject) => {
    try {
      const options = {
        headers: {
          "Accept": "application/json",
          "Authorization": `Basic ${base64.encode(CONFIG.sap.userId + ":" + CONFIG.sap.pass)}`
        }
      };
      const sapRes = await axios.post(CONFIG.sap.SO_URL, data, options)
      if (sapRes.data) {
        if (sapRes.data.OUTPUT.SAP_SO_NUM != "") {
          resolve({ status: true, status_code: sapRes.status, data: sapRes.data.OUTPUT })
        } else {
          resolve({ status: false, status_code: sapRes.status, data: sapRes.data.OUTPUT })
        }
      } else {
        resolve({ status: false, status_code: sapRes.status, data: sapRes })
      }
    } catch (err) {
      resolve({ status: false, status_code: err?.response?.status, data: { MESSAGE: [err?.response?.statusText] } })
    }
  })
}

exports.getSapReqBody = (sapParam) => {
  return new Promise(async (resolve, reject) => {
    try {
      let { orderData, reason, billNo, CustomerNum, Gstin, rate } = sapParam

      const { orderCustomers: orderCustomer, orderAddresses: orderAddress } = orderData;

      let orderCreatedAt = orderData?.order_created_at.split('-')
      let orderDate = `${orderCreatedAt[0]}${orderCreatedAt[1]}${orderCreatedAt[2].split(" ")[0]}`
      let totalPrice = 0, basicPrice = 0, taxPrice = 0, discount = 0
      if (orderData.order_amount) {
        totalPrice = parseFloat(orderData.order_amount)
        discount = orderData.discount != null && orderData.discount != "null" ? parseFloat(orderData.discount) : 0
        discount += rate
        discount = parseFloat(discount / 1.18).toFixed(2)
        basicPrice = parseFloat(totalPrice / 1.18).toFixed(2)
        taxPrice = totalPrice - basicPrice

      }
      let paymentMethod = orderData.gateway

      //helper function
      const fetchRegionCode = async (province) => {
        if (!province) return "";
        const state = await dbConnection.stateRegionCode.findOne({
          where: {
            [Op.or]: [{ new_state: province }, { state: province }],
          },
        });
        return state?.dataValues?.region_code || "";
      };
      const cleanAddress = (field) => field && field !== "null" ? field : "";
      const formatName = (name) => name && name !== "null" && name.length > 35 ? name.split(" ")[0] : name || null;

      const shippingFirstName = formatName(orderAddress.first_name);
      const billingFirstName = formatName(orderAddress.billing_first_name);

      const shippingRegionCode = await fetchRegionCode(orderAddress.province);
      const billingRegionCode = await fetchRegionCode(orderAddress.billing_province);

      let billingName1 = cleanAddress(orderAddress.billing_last_name);

      let gstfirstName = ''
      if (Gstin) {
        let fullGstName = orderAddress.billing_company.split(/\s+(.+)/);
        gstfirstName = fullGstName[0]
        billingName1 = fullGstName[1] || ''
      }

      const streetAddress = this.getStreetAddress(orderAddress)
      const ewProductMapping = CONFIG.extendedwarranty.ew_product || {};
      const ewProduct = ewProductMapping[orderData?.sku] || null;
      
      const body = {
        "ORDER_HEADER_IN": {
          "SHOPIFY_NUM": orderData.shopify_order_name,
          "DOC_TYPE": "ZSER",
          "DOC_DATE": orderDate,
          "REF_1": orderData.order_name,
          "ORD_REASON": "Y01",
          "BILL_NUM": ""
        },
        "ORDER_ITEMS_IN": {
          "ITM_NUMBER": "10",
          "MATERIAL": ewProduct,
          "BILL_DATE": orderDate,
          "PLANT": "10A1",
          "STORE_LOC": "1007",
          "PURCH_NO_C": paymentMethod == "COD" ? orderData.order_name : orderData.checkout_id, //query
          "PURCH_NO_S": orderData.order_name ? orderData.order_name : "", // secondary order number
          "POITM_NO_S": "",
          "SALQTYNUM": "1",
          "ITEM_CATEG": "ZTSI",
          "PRICE": `${orderData.order_amount}`,
          "BASIC_AMOUNT": `${basicPrice - discount}`, //  query
          "DISCOUNT_AMOUNT": "0.00",//`${discount}`,
          "TAX_PERCENT": '18',
          "TAX_AMOUNT": taxPrice != "" ? `${parseFloat(taxPrice).toFixed(2)}` : "",
          "TOTAL_AMOUNT": `${totalPrice - discount}`,
          "PAYMENT_METHODS": paymentMethod // orderData.financial_status == "paid" ? "Prepaid" : "COD",
        },
        "ORDER_PARTNERS": {
          "NAME": shippingFirstName,
          "NAME_2": cleanAddress(orderAddress?.last_name),
          "STREET": "",
          "COUNTRY": cleanAddress(orderAddress?.country_code),
          "POSTL_CODE": cleanAddress(orderAddress?.zip_code),
          "CITY": cleanAddress(orderAddress.city),
          "DISTRICT": "",
          "REGION": shippingRegionCode,
          "TELEPHONE": cleanAddress(orderAddress.phone)?.replaceAll(' ', ""),
          "LANGU": "EN",
          "ADDRESS": cleanAddress(orderAddress.address1)
        },
        "BP_PARTNERADDRESSES": { // query solved
          "CUST_NUM": CustomerNum ? CustomerNum : "",
          "GSTIN": Gstin ? Gstin.toUpperCase() : "",
          "NAME": Gstin ? gstfirstName : billingFirstName,
          "NAME_2": billingName1,
          "CITY": cleanAddress(orderAddress?.billing_city),
          "POSTL_COD1": cleanAddress(orderAddress?.billing_zip_code),
          "STREET": streetAddress.billingStreet,
          "STREET_NO": "",
          "HOUSE_NO": "",
          "STR_SUPPL1": streetAddress.billingStreet1,
          "STR_SUPPL2": streetAddress.billingStreet2,
          "STR_SUPPL3": streetAddress.billingStreet3,
          "COUNTRY": cleanAddress(orderAddress?.billing_country_code),
          "LANGU": "EN",
          "REGION": billingRegionCode,
          "TEL1_NUMBR": cleanAddress(orderAddress?.billing_phone)?.replaceAll(' ', ""),
          "E_MAIL": cleanAddress(orderCustomer?.customer_email)
        },
        "SH_PARTNERADDRESSES": { // query solved
          "NAME": shippingFirstName,
          "NAME_2": cleanAddress(orderAddress?.last_name),
          "CITY": cleanAddress(orderAddress?.city),
          "POSTL_COD1": cleanAddress(orderAddress?.zip_code),
          "STREET": streetAddress.shippingStreet,
          "STREET_NO": "",
          "HOUSE_NO": "",
          "STR_SUPPL1": streetAddress.shippingStreet1,
          "STR_SUPPL2": streetAddress.shippingStreet2,
          "STR_SUPPL3": streetAddress.shippingStreet3,
          "COUNTRY": cleanAddress(orderAddress?.country_code),
          "LANGU": "EN",
          "REGION": shippingRegionCode,
          "TEL1_NUMBR": cleanAddress(orderAddress?.phone)?.replaceAll(' ', ""),
          "E_MAIL": cleanAddress(orderCustomer?.customer_email)
        },
        "ORDER_SCHEDULES_IN": {
          "ITM_NUMBER": "10",
          "SCHED_LINE": "0001",
          "REQ_DATE": orderDate,
          "REQ_QTY": "1"
        },
        "ORDER_CONDITIONS_IN": {
          "ITM_NUMBER": "10",
          "COND_TYPE": "ZSER",
          "COND_VALUE": `${orderData.order_amount}`,
          "CURRENCY": "INR"
        }
      }
      resolve({ status: true, data: body })
    } catch (err) {
      console.log('Error getSapReqBody for Extened Warranty=====>', err)
    }
  })
}
exports.getStreetAddress = (orderAddress) => {
  try {

    // Helper function to split address into parts of 40 characters
    const splitAddress = (address) => {
      const parts = [];
      while (address.length > 0) {
        parts.push(address.slice(0, 40));
        address = address.slice(40);
      }
      // Ensure the result always has 4 parts
      while (parts.length < 4) {
        parts.push("");
      }
      return parts;
    };

    const shippingParts = orderAddress?.address1 && orderAddress.address1 !== "null" ? splitAddress(orderAddress.address1) : ["", "", "", ""];
    const billingParts = orderAddress?.billing_address1 && orderAddress.billing_address1 !== "null" ? splitAddress(orderAddress.billing_address1) : ["", "", "", ""];

    return {
      shippingStreet: shippingParts[0],
      shippingStreet1: shippingParts[1],
      shippingStreet2: shippingParts[2],
      shippingStreet3: shippingParts[3],
      billingStreet: billingParts[0],
      billingStreet1: billingParts[1],
      billingStreet2: billingParts[2],
      billingStreet3: billingParts[3],
    };
  } catch (err) {
    console.error("Error getStreetAddress for Extened Warranty=====>", err);
  }
};

const pushToCustomerMaster = async (orderData) => {
  try {
    const reqData = await getCustomerMasterRequest(orderData);

    // If the request data is valid, proceed with the request
    if (!reqData.status) return { status: false };

    const options = {
      headers: {
        "Accept": "application/json",
        "Authorization": `Basic ${base64.encode(CONFIG.sap.userId + ":" + CONFIG.sap.pass)}`
      }
    };

    // For testing purposes, resolve early
    // return { status: true, data: { "CustomerNum": "001", "Gstin": "24ASDFRER", "Message": "Customer has been created" } }
    const customerData = await axios.post(CONFIG.sap.CUS_URL, reqData.data, options);
    const helperObj = {
      shopify_order_id: orderData?.shopify_order_id,
      shopify_order_name: orderData?.shopify_order_name,
      order_name: orderData?.order_name,
      platform_type: "SAP-PushToCustomerMaster-Extended",
      request_data: JSON.stringify(reqData.data),
      response_status_code: customerData.status,
      response_data: JSON.stringify(customerData.data || customerData)
    };

    await orderHistoryHelper.insertOrderHistory(helperObj);

    // Check if customer data exists
    return customerData.data.length > 0
      ? { status: true, data: customerData.data[0] }
      : { status: false };
  } catch (error) {
    console.error("Error pushing to Customer Master for Extened Warranty:", error);
    return { status: false };
  }
};

const getCustomerMasterRequest = async (orderData) => {
  try {
    const data = [];
    const orderCustomer = orderData?.orderCustomers || null;
    const orderAddress = orderData?.orderAddresses || null;

    if (!orderAddress) {
      return { status: false };
    }

    let shippingRegionCode = '';
    let streetAddress = this.getStreetAddress(orderAddress);

    const province = orderAddress.billing_province || "";
    if (province) {
      const state = await dbConnection.stateRegionCode.findOne({
        where: {
          [Op.or]: [{ new_state: province }, { state: province }],
        },
      });

      shippingRegionCode = state?.dataValues?.region_code || "";
    }

    const body = {
      "NAME1": orderAddress.billing_company || "",
      "NAME2": orderAddress.billing_company || "",
      "NAME3": orderAddress.billing_company || "",
      "NAME4": orderAddress.billing_company || "",
      "SEARCH_TERM1": orderAddress.billing_company || "",
      "SEARCH_TERM2": "",
      "STREET": streetAddress.shippingStreet,
      "STREET2": streetAddress.shippingStreet1,
      "STREET3": streetAddress.shippingStreet2,
      "STREET4": streetAddress.shippingStreet3,
      "STREET5": "",
      "CITY": orderAddress.billing_city || "",
      "PINCODE": orderAddress.billing_zip_code || "",
      "REGION": shippingRegionCode,
      "COUNTRY": orderAddress.billing_country_code || orderAddress.country_code || "",
      "TELEPHONE": "",
      "MOBILE": orderAddress.phone?.replaceAll(' ', '') || "",
      "EMAIL": orderCustomer?.customer_email || "",
      "GSTIN": orderData?.orderItemSplit?.gstin || "",
      "CONTACT_PERSON": "",
      "CONTACT_MOBILE": "",
      "CONTACT_EMAIL": "",
      "PAYMENT_TERM": "0001"
    };

    data.push(body);

    return { status: true, data: data };
  } catch (error) {
    console.error("Error getting Customer Master request for Extened Warranty:", error);
    return { status: false };
  }
};



exports.processWarrantyPurchase = async (ordersData) => {
  const API_URL = CONFIG.extendedwarranty.url
  const AUTH_KEY = CONFIG.extendedwarranty.authkey
  const ewProductMapping = CONFIG.extendedwarranty.ew_product || {};

  for (const order of ordersData) {
    // Dynamically determine the ew_product based on the SKU
    const ewProduct = ewProductMapping[order?.sku] || null;

    const payload = {
      authKey: AUTH_KEY,
      name: order?.orderAddresses?.name,
      mobile: order?.orderAddresses?.phone && order?.orderCustomers?.phone != "null" || "",
      email: order?.orderCustomers?.customer_email && order?.orderCustomers?.customer_email != "null" ? order?.orderCustomers?.customer_email : "",
      address: order?.orderAddresses?.address1,
      city: order?.orderAddresses?.city,
      state: order?.orderAddresses?.province,
      pincode: order?.orderAddresses?.zip_code,
      ew_product: ewProduct, // Dynamically set ew_product
      product: order?.orderItemSplit?.sku,
      product_range: order?.orderItemSplit?.product_title,
      invoice_date: new Date(order?.sap_invoice_date).toISOString().split('T')[0],
      invoice_no: order?.sap_billing_number,
      ew_purchase_date: new Date(order?.order_created_at).toISOString().split('T')[0],
      serial_no: order?.orderItemSplit?.product_serial_number,
      order_no: order?.order_name,
    };

    try {
      // Call the external API
      const response = await axios.post(API_URL, payload, {
        headers: {
          "Content-Type": "application/json"
        },
      });

      // Log response
      await orderHistoryHelper.insertOrderHistory({
        shopify_order_id: order?.shopify_order_id,
        shopify_order_name: order?.shopify_order_name,
        order_name: order?.order_name,
        platform_type: "warranty-purchase",
        request_data: JSON.stringify(payload),
        response_status_code: response.status,
        response_data: JSON.stringify(response.data),
      });

      // Update order if API call succeeds
      if (response.data.code === 0) {
        await dbConnection.extendedWarrantyOrder.update(
          { warranty_certificate_url: response.data.data.crt_link, middleware_status: "WARRANTY REGISTERED" },
          { where: { id: order?.id, order_name: order?.order_name } }
        );
      }
    } catch (apiError) {
      if (apiError.response.data.code === 1) {
        const messages = apiError.response.data.message.flat();
        const messagesString = messages.join(' | ');
        await dbConnection.extendedWarrantyOrder.update(
          { failed_reason: messagesString, middleware_status: "FAILED" },
          { where: { id: order?.id, order_name: order?.order_name } }
        );
      }
      // Log API failure
      await orderHistoryHelper.insertOrderHistory({
        shopify_order_id: order?.shopify_order_id,
        shopify_order_name: order?.shopify_order_name,
        order_name: order?.order_name,
        platform_type: "warranty-purchase-error",
        request_data: JSON.stringify(payload),
        response_status_code: apiError.response?.status || 500,
        response_data: JSON.stringify(apiError.response?.data || { error: apiError.message }),
      });
    }
  }
  return
};

exports.getSapInvoiceNumber = async (orderData) => {
  try {
    // If no order data or SAP order number is missing, return early
    if (!orderData?.sap_order_number) return;

    let apiUrl
    if (process.env.NODE_ENV === "production") {
      apiUrl = `https://symerpprd.symphonylimited.com:8443/sap/opu/odata/sap/ZSD_SERVICE_ORDER_SRV/ServiceInvoiceSet(ServiceOrderNo='${orderData.sap_order_number}')`;
    } else {
      apiUrl = `https://symerpqa.symphony.com:8001/sap/opu/odata/sap/ZSD_SERVICE_ORDER_SRV/ServiceInvoiceSet(ServiceOrderNo='${orderData.sap_order_number}')`;
    }

    const options = {
      headers: {
        "Accept": "application/json",
        "Authorization": `Basic ${base64.encode(CONFIG.sap.userId + ":" + CONFIG.sap.pass)}`
      },
    };

    // Make the API call to fetch the invoice number
    const response = await axios.get(apiUrl, options);

    // Log response for auditing purposes
    await orderHistoryHelper.insertOrderHistory({
      shopify_order_id: orderData?.shopify_order_id,
      shopify_order_name: orderData?.shopify_order_name,
      order_name: orderData?.order_name,
      platform_type: "warranty-SAP-invoice-number",
      response_status_code: response.status,
      response_data: JSON.stringify(response.data),
    });

    // Check if the response contains the expected data
    if (response?.data?.d) {
      const { ServiceInvoiceNo } = response.data.d;

      // Validate if ServiceInvoiceNo is not empty or null
      if (ServiceInvoiceNo) {
        const date = new Date().toISOString();
        // Update SAP log and order details with the invoice number
        await dbConnection.sapLog.update(
          { sap_billing_number: ServiceInvoiceNo },
          { where: { order_name: orderData.order_name, sap_order_number: orderData.sap_order_number } }
        );
        await dbConnection.extendedWarrantyOrder.update(
          { sap_status: "invoiced", sap_invoice_date: date, middleware_status: "INVOICED", sap_billing_number: ServiceInvoiceNo },
          { where: { order_name: orderData.order_name, sap_order_number: orderData.sap_order_number } }
        );
      } else {
        await dbConnection.extendedWarrantyOrder.update(
          { middleware_status: "PROCESSING", failed_reason: "No invoice number found" },
          { where: { order_name: orderData.order_name, sap_order_number: orderData.sap_order_number, sap_status: "pushed" } }
        );
      }
    }
  } catch (error) {
    console.error(`Error getting SAP Invoice Number for order ${orderData.order_name}:`, error.message);

    // Update the order status to "FAILED" in case of an error
    await dbConnection.extendedWarrantyOrder.update(
      { failed_reason: error?.message || null, middleware_status: "FAILED" },
      { where: { order_name: orderData.order_name } }
    );

    // Log API failure
    await orderHistoryHelper.insertOrderHistory({
      shopify_order_id: orderData?.shopify_order_id,
      shopify_order_name: orderData?.shopify_order_name,
      order_name: orderData?.order_name,
      platform_type: "warranty-SAP-invoice-number-error",
      response_status_code: 500,
      response_data: JSON.stringify({ error: error?.message || null }),
    });

    return { status: false, error: error.message };
  }
};


exports.cancelWarrantyOrder = async (mainOrderId) => {
  try {
    // Find the warranty order using the main order ID
    const warrantyOrder = await dbConnection.extendedWarrantyOrder.findOne({
      where: { main_order_name: mainOrderId },
    });

    // If no warranty order is found, return early
    if (!warrantyOrder) {
      console.log(`No warranty order found for main_order_name: ${mainOrderId}`);
      return { status: false, message: "Warranty order not found" };
    }

    // Update the warranty order statuses
    await dbConnection.extendedWarrantyOrder.update(
      {
        sap_status: "cancelled",
        middleware_status: "CANCELLED",
      },
      { where: { main_order_name: mainOrderId } }
    );

    console.log(`Warranty order with main_order_id: ${mainOrderId} has been canceled.`);
    return { status: true, message: "Warranty order canceled successfully" };
  } catch (error) {
    console.error(`Error canceling warranty order for main_order_id: ${mainOrderId}`, error.message);
    return { status: false, error: error.message };
  }
};

exports.createWarrantyFulfillment = async () => {
  try {
    const orderDatas = await dbConnection.extendedWarrantyOrder.findAll({
      where: { middleware_status: ["INVOICED", "WARRANTY REGISTERED"], fulfillment_status: ["failed", "pending"] },
      include: [
        {
          model: dbConnection.orderItemSplit,
          as: "orderItemSplit",
          where: { fulfillment_status: "fulfilled" },
          required: true,
        },
      ],
      raw: true,
      nest: true,
    });

    if (!orderDatas.length) {
      console.log(`No warranty orders found`);
      return { status: false, message: "No warranty orders found" };
    }

    for (const order of orderDatas) {
      const shopResponse = await dbConnection.shop.findOne({ where: { id: order.orderItemSplit.store_id } });
      if (!shopResponse) {
        console.error(`Shop not found for store_id: ${order.orderItemSplit.store_id}`);
        continue; // Skip this order and proceed to the next
      }

      const fulfillmentOrder = new FulfillmentOrder(shopResponse.myshopify_domain, shopResponse.token);
      const { status, data } = await fulfillmentOrder.get(order.shopify_order_id);

      if (!status || !data.length) {
        console.error(`No fulfillment order found for order ID: ${order.shopify_order_id}`);
        continue; // Skip this order and proceed to the next
      }

      for (const resData of data) {
        const fulfillmentOrderData = resData.line_items.find(({ line_item_id }) => line_item_id === Number(order.line_item_id));

        if (!fulfillmentOrderData) {
          console.error(`No matching line item found for order ID: ${order.shopify_order_id}, line_item_id: ${order.line_item_id}`);
          continue; // Skip this line item and proceed to the next
        }

        const objFulfillment = new Fulfillment(shopResponse.myshopify_domain, shopResponse.token);
        const responseData = await objFulfillment.warrantyShopifyFulfillment(
          fulfillmentOrderData.fulfillment_order_id,
          fulfillmentOrderData.id
        );

        if (!responseData || responseData.errors || responseData.data?.fulfillmentCreate?.userErrors?.length) {
          console.error(
            `Error creating fulfillment for order ID: ${order.shopify_order_id}, line_item_id: ${order.line_item_id}`,
            responseData.errors || responseData.data?.fulfillmentCreate?.userErrors
          );
          continue; // Skip this line item and proceed to the next
        }

        console.log(`Fulfillment created successfully for order ID: ${order.shopify_order_id}, line_item_id: ${order.line_item_id}`);
        await dbConnection.extendedWarrantyOrder.update(
          { fulfillment_status: "fulfilled" },
          { where: { id: order.id } }
        );
      }
    }

    return { status: true, message: "Warranty fulfillments processed successfully" };
  } catch (error) {
    console.error("Error in createWarrantyFulfillment:", error.message);
    return { status: false, error: error.message };
  }
};