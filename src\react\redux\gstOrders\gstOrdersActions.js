import { gstOrdersTypes } from './gstOrdersTypes';

export const getAllGstOrdersRequest = (data) => ({
    type: gstOrdersTypes.GET_GST_ORDERS_REQUEST,
    payload: data
})

export const getAllGstOrdersSuccess = (data) => ({
    type: gstOrdersTypes.GET_GST_ORDERS_SUCCESS,
    payload: data,
});

export const actionGstOrderRequest = (data) => ({
    type: gstOrdersTypes.ACTION_GST_ORDER_REQUEST,
    payload: data
})

export const actionGstOrderSuccess = (data) => ({
    type: gstOrdersTypes.ACTION_GST_ORDER__SUCCESS,
    payload: data,
});

export const actionGstOrderError = (data) => ({
    type: gstOrdersTypes.ACTION_GST_ORDER__ERROR,
    payload: data,
});