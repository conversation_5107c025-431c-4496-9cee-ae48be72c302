.font-link {
    font-family: '<PERSON><PERSON><PERSON>ll', cursive;
}

.card-style {
    height: 1000px;
    background-color: yellowgreen;
    width: 600px;
}

@font-face {
    font-family: "Poppins";
    src: url("../fonts/Roboto-Black.ttf") format("truetype");
    font-weight: 900;
    font-style: normal;
}

@font-face {
    font-family: "Poppins";
    src: url("../fonts/Roboto-BlackItalic.ttf") format("truetype");
    font-weight: 900;
    font-style: italic;
}

@font-face {
    font-family: "Poppins";
    src: url("../fonts/Roboto-Bold.ttf") format("truetype");
    font-weight: 700;
    font-style: normal;
}

@font-face {
    font-family: "Poppins";
    src: url("../fonts/Roboto-BoldItalic.ttf") format("truetype");
    font-weight: 700;
    font-style: italic;
}

@font-face {
    font-family: "Poppins";
    src: url("../fonts/Roboto-Italic.ttf") format("truetype");
    font-weight: 900;
    font-style: italic;
}

@font-face {
    font-family: "Poppins";
    src: url("../fonts/Roboto-Light.ttf") format("truetype");
    font-weight: 300;
    font-style: normal;
}

@font-face {
    font-family: "Poppins";
    src: url("../fonts/Roboto-LightItalic.ttf") format("truetype");
    font-weight: 300;
    font-style: italic;
}

@font-face {
    font-family: "Poppins";
    src: url("../fonts/Roboto-Medium.ttf") format("truetype");
    font-weight: 500;
    font-style: normal;
}

@font-face {
    font-family: "Poppins";
    src: url("../fonts/Roboto-MediumItalic.ttf") format("truetype");
    font-weight: 500;
    font-style: italic;
}

@font-face {
    font-family: "Poppins";
    src: url("../fonts/Roboto-Regular.ttf") format("truetype");
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: "Poppins";
    src: url("../fonts/Roboto-Thin.ttf") format("truetype");
    font-weight: 100;
    font-style: normal;
}

@font-face {
    font-family: "Poppins";
    src: url("../fonts/Roboto-ThinItalic.ttf") format("truetype");
    font-weight: 100;
    font-style: italic;
}

body {
    margin: 25px;
    font-size: 16px;
    background-color: #f2f3f5;
    font-weight: 400;
    font-family: Poppins;
    font-style: normal;
}

.tracking-detail {
    padding-top: 2.5rem;
    padding-bottom: 2.5rem;
}

.css-fjzh6-TimelineCardTitle {
    font-size: 1rem;
}

.tracking-detail .tracking-detail-card-wrapper:not(:last-child) {
    margin-bottom: 2rem;
}

.tracking-detail .title {
    font-size: 18px;
    margin: 0px 0px 20px 0px;
}

.tracking-detail .card {
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.tracking-detail .card-content-wrapper .card-description {
    display: block;
}

/* .tracking-detail .card-content-wrapper .show-less.card-description {
    display: none;
} */

.MuiCardContent-root .css-5qnbdr-TimelineMainWrapper {
    width: auto;
    padding: 0px;
}

.css-1iv48dl-TimelineVerticalWrapper {
    padding: 0px;
}

.css-swzoo7-TimelineItemContentWrapper .card-title p {
    margin: 0px;
}

.table-heading {
    padding-right: 20px;
}

.table-row-size-style {
    font-size: 12px;
}

.show-more .css-1eg5jlz-ShowMore .emk90bu3 {
    position: relative;
    right: 295px;
}

.show-more .css-1eg5jlz-ShowMore .emk90bu3 {
    display: block;
}

.timeline-card-content .css-sf0pmz-TimelineItemContentWrapper .emk90bu10 {
    line-height: 2.5em;
}

.tracking-order-name-list {
    text-align: center;
    margin: auto;
    padding: 10px;
    font-size: medium;
    color: #878787;
}

.order-name-track {
    text-align: center;
    font-size: x-large;
    font-weight: bold;
}

.not-found-card {
    display: flex;
    justify-content: center;
    align-items: center;
}

.not-found-card-style {
    height: 150px;
    width: 200px;
    display: flex;
    justify-content: center;
    align-items: center;
}