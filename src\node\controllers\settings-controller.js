const dbConnection = require("../models");
const { Op, QueryTypes } = require('sequelize');
const CONFIG = require('../config/config_' + [process.env.NODE_ENV || 'local'] + '.json');
const response = require('../helper/appResponse');
const xlsxx = require('xlsx')
const xlsx = require('json-as-xlsx')
const fs = require('fs');

exports.upsertCoverMasterSku = async (req, res) => {
    let successMessage = response.responseMessage(CONFIG.status.FAILED, CONFIG.msg.FAILED)
    try {
        let bodyData = req.body.data;
        if (bodyData) {
            for (let value of bodyData) {
                let masterSkuObj = {
                    master_sku: value.master_sku,
                    child_sku: value.child_sku,
                }

                let childSkuObj = {
                    child_sku: value.child_sku,
                    cfa: value.cfa
                }
                let tableData = await dbConnection.coverMasterSku.findOne({ where: { master_sku: value.master_sku } })
                if (tableData) {
                    await dbConnection.coverMasterSku.update(masterSkuObj, { where: { master_sku: value.master_sku } })
                    let childTableData = await dbConnection.childSku.findOne({ where: { child_sku: value.child_sku } })
                    if (childTableData) {
                        await dbConnection.childSku.update(childSkuObj, { where: { child_sku: value.child_sku } })
                    } else {
                        await dbConnection.childSku.create(childSkuObj)
                    }
                    successMessage.status = CONFIG.status.SUCCESS
                    successMessage.message = CONFIG.msg.UPDATE
                } else {
                    await dbConnection.coverMasterSku.create(masterSkuObj)
                    await dbConnection.childSku.create(childSkuObj)
                    successMessage.status = CONFIG.status.SUCCESS
                    successMessage.message = CONFIG.msg.CREATE
                }
            }

        } else {
            successMessage.status = CONFIG.status.FAILED
            successMessage.message = CONFIG.msg.NO_DATA
        }
    }
    catch (e) {
        console.log("ee",e);
        successMessage.status = CONFIG.status.ERROR
        successMessage.message = CONFIG.msg.ERROR
    }
    res.status(200).send(successMessage)
}


exports.upsertChildSku = async (req, res) => {
    let successMessage = response.responseMessage(CONFIG.status.FAILED, CONFIG.msg.FAILED)
    try {
        let bodyData = req.body.data
        if (bodyData) {
            for (let value of bodyData) {
                let childSkuObj = {
                    child_sku: value.sku,
                    cfa: value.cfa,
                    is_cloud: "1"
                }
                let tableData = await dbConnection.childSku.findOne({ where: { child_sku: value.sku } })
                if (tableData) {

                    await dbConnection.childSku.update(childSkuObj, { where: { child_sku: value.sku } })
                    successMessage.status = CONFIG.status.SUCCESS
                    successMessage.message = CONFIG.msg.UPDATE
                } else {
                    await dbConnection.childSku.create(childSkuObj)
                    successMessage.status = CONFIG.status.SUCCESS
                    successMessage.message = CONFIG.msg.CREATE
                }
            }
        } else {
            successMessage.status = CONFIG.status.FAILED
            successMessage.message = CONFIG.msg.NO_DATA
        }
    } catch (e) {
        console.log("ERR", e);
        successMessage.status = CONFIG.status.ERROR
        successMessage.message = CONFIG.msg.ERROR
    }
    res.status(200).send(successMessage)
}


exports.getCoverSku = async (req, res) => {
    let successMessage = response.responseMessage(CONFIG.status.FAILED, CONFIG.msg.FAILED)
    try {
        let dbData = await dbConnection.sequelize.query("SELECT cover_master_skus.id,master_sku,cover_master_skus.child_sku,cfa FROM cover_master_skus,child_skus WHERE cover_master_skus.child_sku = child_skus.child_sku", { type: QueryTypes.SELECT })
        if (dbData) {
            successMessage.status = CONFIG.status.SUCCESS
            successMessage.message = CONFIG.msg.SUCCESS
            successMessage.data = dbData
        } else {
            successMessage.status = CONFIG.status.FAILED
            successMessage.message = CONFIG.msg.NO_DATA
        }
    } catch (e) {
        console.log("dd",e)
        successMessage.status = CONFIG.status.ERROR
        successMessage.message = CONFIG.msg.ERROR
    }
    res.status(200).send(successMessage)
}

exports.getChildSku = async (req, res) => {
    let successMessage = response.responseMessage(CONFIG.status.FAILED, CONFIG.msg.FAILED)
    try {
        let dbData = await dbConnection.childSku.findAll({ where: {is_cloud: {[Op.not] : "2"}} })
        if (dbData) {
            successMessage.status = CONFIG.status.SUCCESS
            successMessage.message = CONFIG.msg.SUCCESS
            successMessage.data = dbData
        } else {
            successMessage.status = CONFIG.status.FAILED
            successMessage.message = CONFIG.msg.NO_DATA
        }
    } catch (e) {
        console.log("Error", e);
        successMessage.status = CONFIG.status.ERROR
        successMessage.message = CONFIG.msg.ERROR
    }
    res.status(200).send(successMessage)
}

exports.deleteCoverMasterSku = async (req, res) => {
    let successMessage = response.responseMessage(CONFIG.status.FAILED, CONFIG.msg.FAILED)
    try {
        let id = req.body.id;
        let masterSku = req.body.master_sku;
        let tableData = await dbConnection.coverMasterSku.findOne({ where: { id: id, master_sku: masterSku } })
        if (tableData) {
            await dbConnection.coverMasterSku.destroy({ where: { id: id, master_sku: masterSku } })
            await dbConnection.childSku.destroy({ where: { child_sku: tableData.child_sku } })
            successMessage.status = CONFIG.status.SUCCESS
            successMessage.message = CONFIG.msg.DELETE
        } else {
            successMessage.status = CONFIG.status.FAILED
            successMessage.message = CONFIG.msg.NO_DATA
        }
    } catch (e) {
        console.log("ss",e);
        successMessage.status = CONFIG.status.ERROR
        successMessage.message = CONFIG.msg.ERROR
    }
    res.status(200).send(successMessage)

}

exports.deleteChildSku = async (req, res) => {
    let successMessage = response.responseMessage(CONFIG.status.FAILED, CONFIG.msg.FAILED)
    try {
        let id = req.body.id;
        let childSku = req.body.child_sku;
        let tableData = await dbConnection.childSku.findOne({ where: { id: id, child_sku: childSku } })
        if (tableData) {
            await dbConnection.childSku.destroy({ where: { id: id, child_sku: childSku } })
            successMessage.status = CONFIG.status.SUCCESS
            successMessage.message = CONFIG.msg.DELETE
        } else {
            successMessage.status = CONFIG.status.FAILED
            successMessage.message = CONFIG.msg.NO_DATA
        }
    } catch (e) {
        successMessage.status = CONFIG.status.ERROR
        successMessage.message = CONFIG.msg.ERROR

    }
    res.status(200).send(successMessage)

}

exports.importCoverMasterFile = async (req, res) => {
    let successMessage = response.responseMessage(CONFIG.status.SUCCESS, CONFIG.msg.SUCCESS)
    try {
        if (req.file.path) {
            fileRead(req.file.path)
        } else {
            successMessage.status = CONFIG.status.FAILED
            successMessage.message = CONFIG.msg.FAILED
        }
    } catch (e) {
        console.log("err", e);
        successMessage.status = CONFIG.status.ERROR
        successMessage.message = CONFIG.msg.ERROR
    }
    res.status(200).send(successMessage)


}

exports.exportCoverMasterFile = async (req, res) => {
    let successMessage = response.responseMessage(CONFIG.status.FAILED, CONFIG.msg.FAILED)
    let buffer
    try {
        let dbData = await dbConnection.sequelize.query("SELECT master_sku,cover_master_skus.child_sku,cfa FROM cover_master_skus,child_skus WHERE cover_master_skus.child_sku = child_skus.child_sku", { type: QueryTypes.SELECT })
        const mainsettings = {
            writeOptions: {
                type: 'buffer',
                bookType: 'xlsx'
            }
        }
        const data = [
            {
                sheet: 'Cover Master Data',
                columns: [
                    { label: 'Master SKU', value: row => (row.master_sku ? row.master_sku : "") },
                    { label: 'Child  SKU', value: row => (row.child_sku ? row.child_sku : "") },
                    { label: 'CFA', value: row => (row.cfa ? row.cfa : "") },
                ],
                content: dbData
            }
        ]
        buffer = xlsx(data, mainsettings)
        successMessage.status = 200
        res.writeHead(200, {
            'Content-Type': 'application/octet-stream',
            'Content-disposition': 'attachment; filename=CoverMaster_Data.xlsx'
        })
    } catch (e) {
        successMessage.status = CONFIG.status.ERROR
        successMessage.message = CONFIG.msg.ERROR
    }
    if (successMessage.status == 200) res.end(buffer)
    else res.status(200).send(successMessage)

}


exports.importChildSkuFile = async (req, res) => {
    let successMessage = response.responseMessage(CONFIG.status.SUCCESS, CONFIG.msg.SUCCESS)
    try {
        if (req.file.path) {
            res.status(200).send(successMessage)
            const file = xlsxx.readFile(req.file.path)
            
            let data = []
            const sheets = file.SheetNames
            for (let i = 0; i < sheets.length; i++) {
                const fileUpload = xlsxx.utils.sheet_to_json(
                    file.Sheets[file.SheetNames[i]], { header: ["sku", "cfa" , "cloud"], skipHeader: true })
                fileUpload.forEach((res) => {
                    data.push(res)
                })
            }
            data.splice(0, 1)
            for (let value of data) {
                console.log("value----",value);
                let childSkuObj = {
                    child_sku: value.sku,
                    cfa: value.cfa,
                    is_cloud: value.cloud ? "1" : "0"
                }
                if(value.cfa){
                let tableData = await dbConnection.childSku.findOne({ where: { child_sku: value.sku } })
                if (tableData) {
                    await dbConnection.childSku.update(childSkuObj, { where: { child_sku: value.sku } })
                } else {
                    await dbConnection.childSku.create(childSkuObj)
                }
            }
        }
        } else {
            successMessage.status = CONFIG.status.FAILED
            successMessage.message = CONFIG.msg.FAILED
        }
    } catch (e) {
        console.log("ERROR", e);
        successMessage.status = CONFIG.status.ERROR
        successMessage.message = CONFIG.msg.ERROR
    }
}



exports.exportChildSkuFile = async (req, res) => {
    let successMessage = response.responseMessage(CONFIG.status.FAILED, CONFIG.msg.FAILED)
    let buffer
    try {
        let tableData = await dbConnection.childSku.findAll({ where: {is_cloud: {[Op.not] : "2"}}})
        
        const mainsettings = {
            writeOptions: {
                type: 'buffer',
                bookType: 'xlsx'
            }
        }
        const data = [
            {
                sheet: 'Child Sku Data',
                columns: [
                    { label: 'SKU', value: row => (row.child_sku ? row.child_sku : "") },
                    { label: 'CFA', value: row => (row.cfa ? row.cfa : "") },
                    { label: 'Installation Required', value: row=> (row.is_cloud == "1" ? "true" : "false")}
                ],
                content: tableData
            }
        ]
        buffer = xlsx(data, mainsettings)
        successMessage.status = 200

        res.writeHead(200, {
            'Content-Type': 'application/octet-stream',
            'Content-disposition': 'attachment; filename=ChildSKUCloud_data.xlsx'
        })
    } catch (e) {
        successMessage.status = CONFIG.status.ERROR
        successMessage.message = CONFIG.msg.ERROR
    }
    if (successMessage.status == 200) res.end(buffer)
    else res.status(200).send(successMessage)
}


let fileRead = async (filePath) => {
    try {
        const file = xlsxx.readFile(filePath)
        let data = []
        const sheets = file.SheetNames
        for (let i = 0; i < sheets.length; i++) {
            const fileUpload = xlsxx.utils.sheet_to_json(
                file.Sheets[file.SheetNames[i]], { header: ["master_sku", "child_sku", "cfa"], skipHeader: true })
            fileUpload.forEach((res) => {
                data.push(res)
            })
        }
        data.splice(0, 1)

        for (let value of data) {
            let masterSkuObj = {
                master_sku: value.master_sku,
                child_sku: value.child_sku,
            }
            let childSkuObj = {
                child_sku: value.child_sku,
                cfa: value.cfa
            }
            if(value.child_sku){
            let tableData = await dbConnection.coverMasterSku.findOne({ where: { master_sku: value.master_sku } })
            if (tableData) {
                if (tableData.child_sku !== value.child_sku) {
                    await dbConnection.coverMasterSku.update(masterSkuObj, { where: { master_sku: tableData.master_sku } })
                    await dbConnection.childSku.update(childSkuObj, { where: { child_sku: tableData.child_sku } })
                } else {
                    let childTableData = await dbConnection.coverMasterSku.findOne({ where: { child_sku: value.child_sku } })
                    if (childTableData) {
                        await dbConnection.childSku.update(childSkuObj, { where: { child_sku: value.child_sku } })
                    } else {
                        await dbConnection.coverMasterSku.update(masterSkuObj, { where: { master_sku: value.master_sku } })
                        await dbConnection.childSku.create(childSkuObj)
                    }
                }
            } else {
                let tableData = await dbConnection.coverMasterSku.findOne({ where: { child_sku: value.child_sku } })
                if (tableData) {
                    await dbConnection.coverMasterSku.update(masterSkuObj, { where: { child_sku: value.child_sku } })
                    await dbConnection.childSku.update(childSkuObj, { where: { child_sku: value.child_sku } })
                } else {
                    await dbConnection.coverMasterSku.create(masterSkuObj)
                    await dbConnection.childSku.create(childSkuObj)
                }
            }
        }
    }
        // fs.unlinkSync(filePath)
    } catch (e) {
        console.log("Error", e);
    }
}

exports.updateCreditNote = async (req, res) => {
    try {
        if (req.file.path) {
            const file = xlsxx.readFile(req.file.path)
            let data = []
            const sheets = file.SheetNames
            console.log("sheets==>>>",sheets)
            for (let i = 0; i < sheets.length; i++) {
                const fileUpload = xlsxx.utils.sheet_to_json(
                    file.Sheets[file.SheetNames[i]], { columns: ["sap_delivery_number", "sap_order_number", "Credit note"], skipHeader: true })
                fileUpload.forEach((res) => {
                    data.push(res)
                })
            }
            console.log("data==>>>",data)
            // data.splice(0, 1)
            for (let value of data) {
                console.log("testt")
                let res1 = await dbConnection.orderItemSplit.update({ sap_billing_number: value["Credit note"] }, { where: { sap_delivery_number: value.sap_delivery_number, sap_order_number: value.sap_order_number } });
                console.log("res1",res1)
                let res2 = await dbConnection.sapLog.update({ sap_billing_number: value["Credit note"] }, { where: { sap_delivery_number: value.sap_delivery_number, sap_order_number: value.sap_order_number } });
                console.log("res2",res2)

            }
            res.status(200).send("DONE")

        } else {
            res.status(500).send("ERROR")
        }
    } catch (error) {
        console.log("updateCreditNote---", error);
        res.status(500).send("ERROR")
    }
}
exports.updateSODelivery = async (req, res) => {
    try {
        if (req.file.path) {
            const file = xlsxx.readFile(req.file.path)
            let data = []
            const sheets = file.SheetNames
            for (let i = 0; i < sheets.length; i++) {
                const fileUpload = xlsxx.utils.sheet_to_json(
                    file.Sheets[file.SheetNames[i]], { columns: ["sap_delivery_number", "SAP SO", "Delivery", "sap_order_number"], skipHeader: true })
                fileUpload.forEach((res) => {
                    data.push(res)
                })
            }
            // data.splice(0, 1)
            for (let value of data) {
                let res1 = await dbConnection.orderItemSplit.update({ sap_delivery_number: value.Delivery, sap_order_number: value["SAP SO"] }, { where: { sap_delivery_number: value.sap_delivery_number, sap_order_number: value.sap_order_number } });
                let res2 = await dbConnection.sapLog.update({ sap_delivery_number: value.Delivery, sap_order_number: value["SAP SO"] }, { where: { sap_delivery_number: value.sap_delivery_number, sap_order_number: value.sap_order_number } });
            }
            res.status(200).send("DONE")

        } else {
            res.status(500).send("ERROR")
        }
    } catch (error) {
        console.log("updateSODelivery---", error);
        res.status(500).send("ERROR")
    }
}