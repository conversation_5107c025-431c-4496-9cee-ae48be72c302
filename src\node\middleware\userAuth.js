const CONFIG = require('../config/config_' + [process.env.NODE_ENV || 'local'] + '.json');
const jwt = require("jsonwebtoken");

exports.generateAccessToken = (username) => {
  return jwt.sign({ username }, CONFIG.jwt.token, { expiresIn: '6h' });
}

exports.createtoken = (username) => {
  const token = this.generateAccessToken(username);
  return token
}

exports.authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization']
  const token = authHeader && authHeader.split(' ')[1]
  if (token == null) {
    return res.status(401).send({ message: 'JWT is invalid.', statusCode: 401 })
  }
  jwt.verify(token, CONFIG.jwt.token, (err, user) => {
    if (err) {
      return res.status(401).json({ message: "token expired", statusCode: 401 })
    }
    req.query.shop = user.username
    next()
  })
}

exports.authenticateWebhook = (req, res, next) => {
  let header = req.headers.authorization || '';       // get the auth header
  let token = header.split(/\s+/).pop() || '';        // and the encoded auth token
  let auth = Buffer.from(token, 'base64').toString(); // convert from base64
  let parts = auth.split(/:/);                        // split on colon
  let username = parts.shift();                       // username is first
  let password = parts.join(':');
  if (username === CONFIG.webhookAuth.userName && password === CONFIG.webhookAuth.password) {
    // console.log("Inside webhook if condition====>")
    next()
  } else {
    console.log("Insde webhook authentication====>")
    return res.status(401).send({ message: 'JWT is invalid.', statusCode: 401 })
  }

}

exports.authenticateLimeChat = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];

    if (!authHeader || !authHeader.startsWith('Basic ')) {
      return res.status(401).json({ error: "Invalid authorization" });
    }

    const base64Credentials = authHeader.split(' ')[1];
    const credentials = Buffer.from(base64Credentials, 'base64').toString('utf8');
    const [username, password] = credentials.split(':');

    if (username === CONFIG.limechat.username && password === CONFIG.limechat.password) {
      return next();
    }

    return res.status(401).json({ error: "Invalid credentials" });
  } catch (error) {
    console.error("authenticateLimeChat Middleware Error:", error.message);
    return res.status(500).json({ error: "Internal Server Error" });
  }
};

exports.authenticateZippeeWebhook = (req, res, next) => {
  const username = req.headers['username'];
  const password = req.headers['password'];

  if (!username || !password) {
    return res.status(401).send({ 
      message: 'Authentication credentials missing', 
      statusCode: 401 
    });
  }

  try {
    // Create Basic Auth token
    const credentials = `${username}:${password}`;
    const basicToken = Buffer.from(credentials).toString('base64');

    // Add Basic Auth token to headers for downstream use
    req.headers.authorization = `Basic ${basicToken}`;

    // Verify credentials
    if (username === CONFIG.webhookAuth.userName && 
        password === CONFIG.webhookAuth.password) {
      next();
    } else {
      console.log("Authentication failed");
      return res.status(401).send({ 
        message: 'Invalid credentials', 
        statusCode: 401 
      });
    }
  } catch (error) {
    console.error("Authentication error:", error);
    return res.status(401).send({ 
      message: 'Authentication failed', 
      statusCode: 401 
    });
  }
}