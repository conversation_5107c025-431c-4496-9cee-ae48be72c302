const { Op, Sequelize } = require("sequelize");
const dbConnection = require("../../models");


exports.checkRefundStatus = async (req, res) => {
  try {
    const { phone, sortOrder = "DESC" } = req.query;

    // Validate phone number
    if (!phone || !/^\d{10}$/.test(phone)) {
      return res.status(400).json({ success: false, message: "A valid 10-digit phone number is required." });
    }

    // Fetch orders based on refund/cancellation criteria
    const orders = await dbConnection.orderItemSplit.findAll({
      attributes: [
        "order_name", "middleware_status",
        [Sequelize.col("orderRefund.refund_status"), "refund_status"],
      ],
      include: [
        {
          model: dbConnection.orderCustomer,
          as: "orderCustomers",
          attributes: [],
          where: { phone_number: { [Op.like]: `%${phone}%` } },
          required: true,
        },
        {
          model: dbConnection.order_refund,
          as: "orderRefund",
          attributes: [],
        },
      ],
      where: {
        [Op.or]: [{ is_cancel: "1" }, { is_return: "1" }],
      },
      order: [["id", sortOrder.toUpperCase()]],
      raw: true,
    });

    return res.status(200).json({
      success: orders.length > 0,
      message: orders.length ? "Retrun/Cancel Order fetched successfully." : "No records found.",
      data: orders,
    });
  } catch (error) {
    console.error("Error in checkRefundStatus:", error);
    return res.status(500).json({ success: false, message: "Internal server error." });
  }
};

exports.trackRefundProgress = async (req, res) => {
  try {
    const { orderName } = req.body;

    // Validate orderName
    if (!orderName) {
      return res.status(400).json({ success: false, message: "'orderName' is required." });
    }

    // Fetch order details
    const order = await dbConnection.orderItemSplit.findOne({
      attributes: [
        "order_name",
        "middleware_status",
        [Sequelize.col("orderRefund.refund_status"), "refund_status"],
        [Sequelize.col("orderRefund.payment_id"), "payment_id"],
        [Sequelize.col("orderRefund.amount"), "amount"],
        [Sequelize.col("orderRefund.refund_id"), "refund_id"],
        [Sequelize.col("orderRefund.refund_status"), "refund_status"],
        [Sequelize.col("orderRefund.refund_date"), "refund_date"],
      ],
      include: [
        {
          model: dbConnection.order_refund,
          as: "orderRefund",
          attributes: [],
        },
      ],
      where: { order_name: orderName },
      raw: true,
    });

    if (!order) {
      return res.status(404).json({ success: false, message: "Order not found." });
    }

    return res.status(200).json({ success: true, message: "Order details fetched successfully.", data: order });
  } catch (error) {
    console.error("Error in trackRefundProgress:", error);
    return res.status(500).json({ success: false, message: "Internal server error." });
  }
};