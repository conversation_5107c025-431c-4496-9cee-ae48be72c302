const db = require("./models");
const webhookController = require("./controllers/webhook-controller");

// Synchronize Sequelize models with the database
db.sequelize.sync().then(()=>{console.log("Connected---------")}).catch(err=>{console.log("errrr-----",err)})
exports.handler = async (event) => {
    try {
        // Extracting authorization token from headers
        let authRes = event.headers.Authorization || event.headers.authorization;
        // Parsing data from the event body
        const encodedString = event.detail.payload;
        const decodedString = Buffer.from(encodedString, 'base64').toString('utf-8');
        const header = authRes || '';
        const token = header.split(/\s+/).pop() || '';
        const auth = Buffer.from(token, 'base64').toString();
        const parts = auth.split(/:/);
        const username = parts.shift();
        const password = parts.join(':');

        // Checking credentials
        if (username === process.env.userName && password === process.env.password) {

            // Handling webhook data using the controller
            const response = await webhookController.webhook(JSON.parse(decodedString));

            return {
                statusCode: 200,
                body: JSON.stringify(response),
            };
        } else {
            console.log("Inside webhook authentication ===>");
            return {
                statusCode: 401,
                body: JSON.stringify({ message: 'JWT is invalid.', statusCode: 401 }),
            };
        }
    } catch (err) {
        console.error("Error:", err);
        return {
            statusCode: 500,
            body: JSON.stringify({ message: 'Internal Server Error', statusCode: 500 }),
        };
    }
};