const multer  = require('multer');
const fs = require('fs')
if (!fs.existsSync(__basedir + '/uploads')){
    fs.mkdirSync(__basedir + '/uploads');
}
const storage = multer.diskStorage({
	destination: (req, file, cb) => {
	   cb(null, __basedir + '/uploads')
	},
	filename: (req, file, cb) => {
	   cb(null, file.fieldname + "-" + Date.now() + "-" + file.originalname)
	}
});
const upload = multer({storage: storage});
module.exports = upload;
