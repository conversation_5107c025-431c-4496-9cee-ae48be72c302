const Sequelize = require("sequelize");
const sequelize = new Sequelize(process.env.db_name, process.env.db_user, process.env.db_password, {
  host: process.env.db_host,
  dialect: process.env.dialect,
  operatorsAliases: 0,
  logging: false,
  pool: {
    max: 5,
    min: 0,
    acquire: 30000,
    idle:10000
  },
  dialectOption: {
    useUTC: true
  },
  timezone: "+00:00"
});
//DB Connection
const db = { sequelize: sequelize };

//All Models Include
db.shop = require('./shop.model')(sequelize)
db.orderItemSplit = require('./order_item_split.model')(sequelize)
db.orderCustomer = require('./order_customer.model')(sequelize)
db.orderAddress = require('./order_address.model')(sequelize)
db.fulfillmentItem = require('./fulfillment_item.model')(sequelize)
db.delhiveryLog = require('./delhivery_log.model')(sequelize)
db.orderCancellation = require('./order_cancellation.model')(sequelize)
db.childSku = require('./child_sku.model')(sequelize)

db.orderCustomer.belongsTo(db.orderItemSplit, { as: 'orderCustomers', foreignKey: "order_id", sourceKey: "shopify_order_id" })
db.orderItemSplit.hasOne(db.orderCustomer, { as: 'orderCustomers', foreignKey: "order_id", sourceKey: "shopify_order_id" })

db.orderAddress.belongsTo(db.orderItemSplit, { as: 'orderAddresses', foreignKey: "order_id", sourceKey: "shopify_order_id" })
db.orderItemSplit.hasOne(db.orderAddress, { as: 'orderAddresses', foreignKey: "order_id", sourceKey: "shopify_order_id" })

db.fulfillmentItem.belongsTo(db.orderItemSplit, { as: 'fulfillmentItem', foreignKey: "order_id", sourceKey: "id" })
db.orderItemSplit.hasMany(db.fulfillmentItem, { as: 'fulfillmentItem', foreignKey: "order_id", sourceKey: "id" })

db.delhiveryLog.belongsTo(db.orderItemSplit, { as: 'delhiveryLog', foreignKey: "order_name", sourceKey: "order_name" })
db.orderItemSplit.hasMany(db.delhiveryLog, { as: 'delhiveryLog', foreignKey: "order_name", sourceKey: "order_name" })

//Exports
module.exports = db;
