const Sequelize = require("sequelize");
module.exports = (sequelize) => {
    return sequelize.define("cfa_pincode_mapping", {
        id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            unique: true
        },
        store_id: {
            allowNull: false,
            type: Sequelize.INTEGER,
            references: {
                model: 'stores',
                key: 'id'
            },
        }, pincode: {
            type: Sequelize.STRING,
            primaryKey: true
        },
        city: {
            type: Sequelize.STRING
        },
        state: {
            type: Sequelize.STRING
        },
        region: {
            type: Sequelize.STRING
        },
        country: {
            type: Sequelize.STRING
        },
        status: {
            type: Sequelize.STRING
        },
        cfa_type: {
            type: Sequelize.STRING
        },
        estimated_delivery_time: {
            type: Sequelize.STRING
        },
        cod_enabled: {
            type: Sequelize.STRING
        },
        shipping_partner: {
            type: Sequelize.STRING
        },
        pincode_group: {
            type: Sequelize.STRING
        },
    },
        {
            indexes: [
                {
                    unique: true,
                    fields: ['pincode', 'cfa_type']
                }
            ]
        
    });
};