const dbConnection = require("../../models");
const CONFIG = require(`../../config/config_${process.env.NODE_ENV || "local"}.json`);
const { Sequelize, Op } = require("sequelize");
const axios = require('axios');
const orderHistoryHelper = require('../../helper/orderHistory')


exports.trackTicketStatus = async (req, res) => {
  try {
    const { phone, sortOrder = "DESC" } = req.query;

    // Validate phone number
    if (!phone || !/^\d{10}$/.test(phone)) {
      return res.status(400).json({ success: false, message: "A valid 10-digit phone number is required." });
    }

    // Fetch orders based on the provided condition
    const orders = await dbConnection.orderItemSplit.findAll({
      attributes: ["order_name", "crm_ticket_status", "crm_ticket_number", "call_registration_date", "call_closed_date"],
      include: [
        {
          model: dbConnection.orderCustomer,
          as: "orderCustomers",
          attributes: [],
          where: { phone_number: { [Op.like]: `%${phone}%` } },
          required: true,
        },
      ],
      where: {
        // crm_ticket_status: ["open", "pending"],
        middleware_status: "DELIVERED",
        // crm_ticket_number: { [Op.ne]: null },
      },
      order: [["id", sortOrder.toUpperCase()]],
      raw: true,
    });

    // Initialize array with existing orders
    let allOrders = [...orders];

    // Fetch service request logs for each order and push into the array
    await Promise.all(
      orders.map(async (order) => {
        const serviceRequestLog = await dbConnection.serviceRequestLog.findOne({ where: { order_name: order.order_name, call_id: { [Op.ne]: null } }, raw: true });

        if (serviceRequestLog) {
          allOrders.push({
            order_name: order.order_name,
            crm_ticket_status: serviceRequestLog?.call_status,
            call_registration_date: serviceRequestLog?.call_registration_date,
            call_closed_date: serviceRequestLog?.call_closed_date,
            crm_ticket_number: serviceRequestLog?.call_id?.replace(/,$/, ""),
          });
        }
      })
    );

    // Filter out records with null or empty crm_ticket_number
    allOrders = allOrders.filter(order => order.crm_ticket_number);

    return res.status(200).json({
      success: allOrders.length > 0,
      message: allOrders.length ? "Ticket Status fetched successfully." : "No records found.",
      data: allOrders,
    });
  } catch (error) {
    console.error("Error in trackTicketStatus:", error);
    return res.status(500).json({ success: false, message: "Internal server error." });
  }
};

exports.createTicket = async (req, res) => {
  const { orderName, phoneNumber, modelType, camplainReason } = req.body;

  const validationError = validateTicketRequest(orderName, phoneNumber, modelType, camplainReason);
  if (validationError) {
    return res.status(400).json({ success: false, message: validationError });
  }

  const options = {
    headers: {
      'Content-Type': 'application/json'
    }
  }

  try {
    const orderDetails = await dbConnection.orderItemSplit.findOne({
      include: [
        { model: dbConnection.orderCustomer, as: "orderCustomers", required: true },
        { model: dbConnection.orderAddress, as: "orderAddresses", required: true },
      ],
      where: { order_name: orderName },
      raw: true,
      nest: true,
    });

    if (!orderDetails) {
      return res.status(404).json({ success: false, message: "Order not found" });
    }

    const cityStateData = await dbConnection.cityStatePin.findOne({
      where: { pincode: orderDetails?.orderAddresses?.zip_code }, raw: true,
    });

    const city_code = cityStateData?.city_code || "";
    const state_code = cityStateData?.state_code || "";

    const tableData = (city_code && state_code)
      ? await dbConnection.cityStatePin.findOne({ where: { city_code, state_code }, raw: true })
      : null;

    const logReqData = {
      serviceType: "submitRegistrationDetails",
      submitRegistrationDetails: {
        companyCode: CONFIG.crm.companyCode,
        name: orderDetails?.orderAddresses?.name || "",
        mobileNumber: getLastTenDigits(phoneNumber),
        loginType: CONFIG.crm.loginType,
        address: orderDetails?.orderAddresses?.address1 || "",
        state: state_code,
        city: city_code,
        area: tableData?.area || "",
        pin: orderDetails?.orderAddresses?.zip_code || "",
        emailAddress: orderDetails?.orderCustomers?.customer_email?.trim() || "",
        customerType: CONFIG.crm.customerType,
        alternateMobileNo: "",
        profilePhoto: "",
        fileSize: "",
        fileType: "",
        landmark: "",
        preferredLanguage: "",
        customerPriorityType: ""
      },
    };

    const logDataRes = await axios.post(CONFIG.crm.log_url, logReqData, options);
    //add log in database
    let helperObj = {
      shopify_order_id: orderDetails.shopify_order_id,
      shopify_order_name: orderDetails.shopify_order_name,
      order_name: orderDetails.order_name,
      platform_type: "LimeChat-Service-Request-first",
      request_data: JSON.stringify(logReqData),
      response_status_code: logDataRes.status,
      response_data: logDataRes.data ? JSON.stringify(logDataRes.data) : 'warranty error'
    }
    await orderHistoryHelper.insertOrderHistory(helperObj)

    if (logDataRes?.data?.wSState !== 0) {
      const customerCode = logDataRes?.data?.userProfile?.customerCode || null;

      const warrantyLogReqData = {
        serviceType: "submitCallRegistration",
        userId: CONFIG.crm.userId,
        token: CONFIG.crm.token,
        submitCallRegistration: {
          distributorCode: CONFIG.crm.distributorCode,
          dealerCode: CONFIG.crm.dealerCode,
          customerType: CONFIG.crm.customerType,
          mobileNumber: customerCode ? customerCode : getLastTenDigits(phoneNumber),
          customerName: orderDetails?.orderAddresses?.name || "",
          address: orderDetails?.orderAddresses?.address1 || "",
          area: tableData?.area || "",
          cityCode: city_code,
          stateCode: state_code,
          pinCode: orderDetails?.orderAddresses?.zip_code || "",
          brandCode: CONFIG.crm.brandCode,
          productCategoryCode: CONFIG.crm.productCategoryCode,
          productCode: CONFIG.crm.productCode,
          model: modelType || "",
          productSerialNumber: orderDetails?.product_serial_number || "",
          techRemarks: camplainReason || "",
          serviceTypeCode: 1,
          dateOfPurchase: ""
        },
        version: CONFIG.crm.version,
      };

      const Response = await axios.post(CONFIG.crm.warranty_url, warrantyLogReqData, options);
      //add log in database
      let helperObj = {
        shopify_order_id: orderDetails.shopify_order_id,
        shopify_order_name: orderDetails.shopify_order_name,
        order_name: orderDetails.order_name,
        platform_type: "LimeChat-Service-Request",
        request_data: JSON.stringify(warrantyLogReqData),
        response_status_code: Response.status,
        response_data: Response.data ? JSON.stringify(Response.data) : 'warranty error'
      }
      await orderHistoryHelper.insertOrderHistory(helperObj)

      if (Response?.data) {
        const { responseId, callId, messageDescription } = Response.data;
        await dbConnection.serviceRequestLog.create({
          order_name: orderDetails.order_name,
          request_id: Math.floor(Math.random() * 100) + 1,
          customer_code: customerCode,
          response_id: responseId || null,
          call_id: callId || null,
          message_description: messageDescription,
        });

        return res.status(200).json({ success: true, message: "Ticket created successfully", data: { callId } });
      } else {
        return res.status(400).json({ success: false, message: "Failed to register call, please contact support team" });
      }
    }
  } catch (error) {
    console.error("Error creating ticket:", error.message, error.stack);
    return res.status(400).json({ success: false, message: "Failed to register call, please contact support team" });
  }
};

// Validation function
const validateTicketRequest = (orderName, phoneNumber, modelType, camplainReason) => {
  if (!orderName) return "Order Name is required";
  if (!phoneNumber) return "Phone Number is required";
  if (!/^\d{10,15}$/.test(phoneNumber)) return "Invalid Phone Number format";
  if (!modelType) return "Model Type is required";
  if (!camplainReason) return "Complaint Reason is required";
  return null; // No validation errors
};

function getLastTenDigits(str) {
  return str.toString().replace(/\D/g, '').slice(-10);
}