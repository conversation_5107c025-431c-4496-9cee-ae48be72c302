const Sequelize = require("sequelize");
module.exports = (sequelize) => {
  return sequelize.define("product_image", {
    id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      unique: true
    },
    store_id: {
      allowNull:false,
      type: Sequelize.INTEGER,
      references: {
        model: 'stores',
        key: 'id'
      },
    },
    product_id: {
      allowNull:false,
      type: Sequelize.INTEGER,
      references: {
        model: 'products',
        key: 'id'
      },
    },
    image_id: {
      type: Sequelize.STRING
    },
    image_url: {
      type: Sequelize.STRING(500),
    },
  },
    {
      indexes: [
        {
          unique: true,
          fields: ['store_id', 'image_id']
        }
      ]
    });
};