import { settingsTypes } from './settingsTypes';

export const getCloudSkuListRequest = (data) => ({
    type: settingsTypes.GET_CLOUD_SKU_LIST_REQUEST,
    payload: data
});

export const getCloudSkuListSuccess = (data) => ({
    type: settingsTypes.GET_CLOUD_SKU_LIST_SUCCESS,
    payload: data,
});

export const getCoverSkuListRequest = (data) => ({
    type: settingsTypes.GET_COVER_SKU_LIST_REQUEST,
    payload: data
});

export const getCoverSkuListSuccess = (data) => ({
    type: settingsTypes.GET_COVER_SKU_LIST_SUCCESS,
    payload: data,
});

export const saveCloudSkuFileRequest = (data) => ({
    type: settingsTypes.SAVE_CLOUD_SKU_FILE_REQUEST,
    payload: data,
});

export const saveCloudSkuFileSuccess = (data) => ({
    type: settingsTypes.SAVE_CLOUD_SKU_FILE_SUCCESS,
    payload: data
});

export const saveCloudSkuFileError = (data) => ({
    type: settingsTypes.SAVE_CLOUD_SKU_FILE_ERROR,
    payload: data
});

export const saveCoverSkuFileRequest = (data) => ({
    type: settingsTypes.SAVE_COVER_SKU_FILE_REQUEST,
    payload: data
});

export const saveCoverSkuFileSuccess = (data) => ({
    type: settingsTypes.SAVE_COVER_SKU_FILE_SUCCESS,
    payload: data
});

export const saveCoverSkuFileError = (data) => ({
    type: settingsTypes.SAVE_COVER_SKU_FILE_ERROR,
    payload: data
});

export const coverExportRequest = (data) => ({
    type: settingsTypes.COVER_SKU_EXPORT_REQUEST,
    payload: data
});

export const coverExportSuccess = (data) => ({
    type: settingsTypes.COVER_SKU_EXPORT_SUCCESS,
    payload: data
});

export const coverExportError = (data) => ({
    type: settingsTypes.COVER_SKU_EXPORT_ERROR,
    payload: data
});

export const cloudExportRequest = (data) => ({
    type: settingsTypes.CLOUD_SKU_EXPORT_REQUEST,
    payload: data
});

export const cloudExportSuccess = (data) => ({
    type: settingsTypes.CLOUD_SKU_EXPORT_SUCCESS,
    payload: data
});

export const cloudExportError = (data) => ({
    type: settingsTypes.CLOUD_SKU_EXPORT_ERROR,
    payload: data
});

export const deleteCoverRequest = (data) => ({
    type: settingsTypes.DELETE_COVER_SKU_REQUEST,
    payload: data
});

export const deleteCoverSuccess = (data) => ({
    type: settingsTypes.DELETE_COVER_SKU_SUCCESS,
    payload: data
});

export const deleteCoverError = (data) => ({
    type: settingsTypes.DELETE_COVER_SKU_ERROR,
    payload: data
});

export const deleteCloudRequest = (data) => ({
    type: settingsTypes.DELETE_CLOUD_SKU_REQUEST,
    payload: data
});

export const deleteCloudSuccess = (data) => ({
    type: settingsTypes.DELETE_CLOUD_SKU_SUCCESS,
    payload: data
});

export const deleteCloudError = (data) => ({
    type: settingsTypes.DELETE_CLOUD_SKU_ERROR,
    payload: data
});