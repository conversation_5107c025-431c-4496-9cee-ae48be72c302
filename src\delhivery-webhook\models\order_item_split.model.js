const Sequelize = require("sequelize");
module.exports = (sequelize) => {
    return sequelize.define("order_item_split", {
        id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            unique: true
        },
        store_id: {
            type: Sequelize.INTEGER,
            allowNull: false,
            references: {
                model: 'stores',
                key: 'id'
            }
        },
        shopify_order_id: {
            allowNull: false,
            type: Sequelize.STRING
        },
        shopify_customer_id: {
            allowNull: true,
            type: Sequelize.STRING,
        },
        shopify_product_id: {
            type: Sequelize.STRING
        },
        shopify_variant_id: {
            type: Sequelize.STRING
        },
        checkout_id: {
            type: Sequelize.STRING
        },
        gateway: {
            allowNull: true,
            type: Sequelize.STRING
        },
        shipment_status: {
            type: Sequelize.STRING
        },
        line_item_id: {
            allowNull: false,
            type: Sequelize.STRING,
        },
        order_number: {
            allowNull: false,
            type: Sequelize.STRING
        },
        shopify_order_name: {
            allowNull: false,
            type: Sequelize.STRING
        },
        order_name: {
            allowNull: true,
            type: Sequelize.STRING,
            primaryKey: true,
            unique: true
        },
        checkout_id: {
            type: Sequelize.STRING,
            defaultValue: null
        },
        sap_order_number: {
            type: Sequelize.STRING,
            defaultValue: null
        },
        sap_delivery_number: {
            type: Sequelize.STRING,
            defaultValue: null
        },
        sap_billing_number: {
            type: Sequelize.STRING,
            defaultValue: null
        },
        tax_percentage: {
            type: Sequelize.STRING,
            defaultValue: null
        },
        waybill_number: {
            type: Sequelize.STRING,
            // primaryKey: true,
            // unique: true
        },
        invoice_url: {
            type: Sequelize.STRING(500)
        },
        order_status: {
            allowNull: false,
            type: Sequelize.DataTypes.ENUM('Pending', 'Out Of Stock', 'PincodeNotAvailable', 'Completed', 'Cancel', 'Refund', 'Returned', 'Replacement'),
            defaultValue: 'Pending'
        },
        sap_status: {
            type: Sequelize.DataTypes.ENUM('Pending', 'Failed', 'Pushed', 'Invoiced', 'Cancel', 'Replacement'),
            defaultValue: 'Pending'

        },
        delhivery_status: {
            type: Sequelize.DataTypes.ENUM('Pending', 'Failed', 'Pushed', 'Cancel', 'Replacement'),
            defaultValue: 'Pending'
        },
        sap_invoice_date: {
            type: Sequelize.STRING
        },
        order_cancel_date: {
            type: Sequelize.STRING
        },
        financial_status: {
            type: Sequelize.DataTypes.ENUM('paid', 'unpaid', 'pending', 'authorized', 'partially_paid', 'refunded', 'partially_refunded', 'voided'),
            defaultValue: 'unpaid'
        },
        fulfillment_status: {
            type: Sequelize.DataTypes.ENUM('fulfilled', 'failed', 'pending'),
            defaultValue: 'pending'
        },
        plant_code: {
            type: Sequelize.STRING,
            defaultValue: null
        },
        order_created_at: {
            type: Sequelize.STRING
        },
        order_amount: {
            type: Sequelize.DOUBLE(11, 2),
            defaultValue: '0.00'
        },
        product_title: {
            type: Sequelize.STRING,
            defaultValue: null
        },
        sku: {

            type: Sequelize.STRING(100),
        },
        gstin: {
            type: Sequelize.STRING,
            defaultValue: null
        },
        is_cancel: {
            type: Sequelize.ENUM("0", "1"),
            defaultValue: "0"
        },
        is_return: {
            type: Sequelize.ENUM("0", "1"),
            defaultValue: "0"
        },
        is_send_mail: {
            type: Sequelize.ENUM("0", "1"),
            defaultValue: "0"
        },
        is_pickup: {
            type: Sequelize.ENUM("0", "1"),
            defaultValue: "0"
        },
        is_replaceable: {
            type: Sequelize.ENUM("0", "1"),
            defaultValue: "0"
        },
        is_gst_Valid: {
            type: Sequelize.ENUM("0", "1", "2"),
            defaultValue: "1",
            allowNull: false,
            comment: '(0=invalid, 1=valid, 2=state not match)'
        },
        is_order_hold: {
            type: Sequelize.ENUM("0", "1"),
            defaultValue: "0",
            allowNull: false
        },
        delivered_at: {
            type: Sequelize.DATE
        },
        tax: {
            type: Sequelize.STRING,
            defaultValue: null
        },
        tax_percentage: {
            type: Sequelize.STRING,
            defaultValue: null
        },
        discount: {
            type: Sequelize.STRING,
            defaultValue: null
        },
        quantity: {
            allowNull: false,
            type: Sequelize.INTEGER
        },
        product_serial_number: {
            type: Sequelize.STRING,
            defaultValue: null
        },
        replacement_reason: {
            type: Sequelize.STRING,
            defaultValue: null
        },
        crm_ticket_number: {
            type: Sequelize.STRING,
            defaultValue: null
        },
        crm_ticket_status: {
            type: Sequelize.STRING,
            defaultValue: null
        },
        warranty_code: {
            type: Sequelize.STRING,
            defaultValue: null
        },
        return_applied_status: {
            type: Sequelize.DataTypes.ENUM('0', '1', '2'),
            defaultValue: null,
            comment: '(0=requested, 1=approved, 2=rejected)'
        },
        settlement_id: {
            type: Sequelize.STRING,
            defaultValue: null
        },
        razorpay_transfer_id: {
            type: Sequelize.STRING,
            defaultValue: null
        },
        middleware_status: {
            type: Sequelize.DataTypes.ENUM('OLD ORDERS','PENDING', 'PROCESSING', 'CANCELLED', 'RTO IN TRANSIT', 'READY TO SHIP', 'IN TRANSIT', 'OUT OF STOCK', 'RTO RETURNED TO WAREHOUSE','RETURNED TO WAREHOUSE','REFUND PENDING','REFUNDED','RETURN REQUESTED','RETURN PICKUP CANCELLED BY CUSTOMER','RETURN IN TRANSIT','RETURN APPROVED','LOST IN TRANSIT','GST ORDER PENDING','FAILED','DELIVERED'),
            defaultValue: 'PENDING'
        },
        return_order_name: {
            type: Sequelize.STRING,
            defaultValue: null
        },
        return_request_date: {
            type: Sequelize.STRING,
            defaultValue: null
        },
        failed_reason: {
            type: Sequelize.STRING,
            defaultValue: null
        },
        gift_card_value: {
            type: Sequelize.DOUBLE(11, 5),
            defaultValue: 0.00
        },
        return_approved_date: {
            type: Sequelize.STRING,
            defaultValue: null
        },
    },
        {
            indexes: [
                {
                    unique: true,
                    fields: ['shopify_order_id', 'order_name', 'line_item_id']
                },
                {
                    fields: ['middleware_status']
                }
            ]
        });
};