import { put, takeLatest } from "redux-saga/effects";
import Api from "../../apis/Api";
import { openToast } from "../toast/toastActions";
import {
    getCfaListSuccess, getMannualCFAListSuccess, saveCfaFileSuccess, saveCfaFileError,
    pincodeExportError, pincodeExportSuccess, exportCfaStockSuccess, exportCfaStockError,
    getManageCfaListSuccess, saveManageCfaFileSuccess, saveManageCfaFileError,
    exportAllCfaSuccess,
    exportAllCfaError
} from "./cfaActions";
import { cfaTypes, toastObject } from "./cfaTypes";

let toast = toastObject;

function* getCfaList(data) {

    try {
        const response = yield Api.postAsync(
            Api.getCfaList,
            data.payload
        )
        yield put(getCfaListSuccess(response));
        data.payload.callback();
    } catch (e) {
        data.payload.callback();
    }
}
function* getManageCfaList(data) {
    try {
        const response = yield Api.postAsync(
            Api.getManageCfa,
            data.payload
        )
        yield put(getManageCfaListSuccess(response));
        data.payload.callback();
    } catch (e) {
        data.payload.callback();
    }
}

function* getManualCFAList(data) {

    try {
        const response = yield Api.postAsync(
            Api.getCFAList,
            data.payload
        )
        yield put(getMannualCFAListSuccess(response));
        data.payload.callback();
    } catch (e) {
        data.payload.callback();
    }
}

function* saveCfaList(data) {
    let message = "Oppss...There is something wrong.";
    let isError = false;
    try {
        isError = false;
        message = "Data Saved";
        const response = yield Api.postAsync(
            Api.saveCfaFile,
            data.payload.formData
        )
        yield put(saveCfaFileSuccess(response));
    } catch (e) {
        isError = true;
        if (typeof e.response.data.message !== 'undefined' && e.response.data.message !== null) {
            message = e.response.data.message;
        }
        yield put(saveCfaFileError(e.response));
    } finally {
        toast = { ...toast, message: message, isActive: true, isError: isError }
        yield put(openToast(toast));
        data.payload.callback();
    }
}

function* saveManageCfaList(data) {
    let message = "Oppss...There is something wrong.";
    let isError = false;
    try {
        isError = false;
        message = "Data Saved";
        const response = yield Api.postAsync(
            Api.saveManageCfaFile,
            data.payload.formData
        )
        yield put(saveManageCfaFileSuccess(response));
    } catch (e) {
        isError = true;
        if (typeof e.response.data.message !== 'undefined' && e.response.data.message !== null) {
            message = e.response.data.message;
        }
        yield put(saveManageCfaFileError(e.response));
    } finally {
        toast = { ...toast, message: message, isActive: true, isError: isError }
        yield put(openToast(toast));
        data.payload.callback();
    }
}

function* pincodeExport(data) {
    let message = "Oppss...There is something wrong.";
    let isError = false;
    try {
        isError = false;
        message = "File has been exported successfully";
        const response = yield Api.postAsync(
            Api.exportPincode,
            data.payload
        )
        yield put(pincodeExportSuccess(response));
    } catch (e) {
        isError = true;
        message = "Oppss...There is something wrong.";
        yield put(pincodeExportError(e.response));
    } finally {
        toast = { ...toast, message: message, isActive: true, isError: isError }
        yield put(openToast(toast));
        data.payload.callback();
    }
}

function* exportAllCfa(data) {
    let message = "Oppss...There is something wrong.";
    let isError = false;
    try {
        isError = false;
        message = "File has been exported successfully";
        const response = yield Api.postAsync(
            Api.exportCfaList,
            data.payload
        )
        yield put(exportAllCfaSuccess(response));
    } catch (e) {
        isError = true;
        message = "Oppss...There is something wrong.";
        yield put(exportAllCfaError(e.response));
    } finally {
        toast = { ...toast, message: message, isActive: true, isError: isError }
        yield put(openToast(toast));
        data.payload.callback();
    }
}

function* exportCFAstock(data) {
    let message = "Oppss...There is something wrong.";
    let isError = false;
    try {
        isError = false;
        message = "Exported data send to your mail";
        const response = yield Api.postAsync(
            Api.exportCFAstock,
            data.payload.objdata
        )
        yield put(exportCfaStockSuccess(response));
    } catch (e) {
        isError = true;
        message = "Oppss...There is something wrong.";
        yield put(exportCfaStockError(e.response));
    } finally {
        toast = { ...toast, message: message, isActive: true, isError: isError }
        yield put(openToast(toast));
        data.payload.callback();
    }
}
export default function* ordersSaga() {
    yield takeLatest(cfaTypes.GET_CFA_LIST_REQUEST, getCfaList);
    yield takeLatest(cfaTypes.SEND_CFA_FILE_REQUEST, saveCfaList);
    yield takeLatest(cfaTypes.GET_MANNUAL_CFA_LIST_REQUEST, getManualCFAList);
    yield takeLatest(cfaTypes.PINCODE_EXPORT_REQUEST, pincodeExport);
    yield takeLatest(cfaTypes.CFASTOCK_EXPORT_REQUEST, exportCFAstock);
    yield takeLatest(cfaTypes.GET_MANAGE_CFA_LIST_REQUEST, getManageCfaList);
    yield takeLatest(cfaTypes.SEND_MANAGE_CFA_FILE_REQUEST, saveManageCfaList);
    yield takeLatest(cfaTypes.EXPORT_CFA_REQUEST, exportAllCfa);
}