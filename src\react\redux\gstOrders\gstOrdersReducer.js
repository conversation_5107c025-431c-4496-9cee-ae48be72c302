import { gstOrdersTypes } from "./gstOrdersTypes";

const initialState = {
    loading: true,
    data: [],
    error: false,
    modalBtnLoading: false
}

export default function gstOrdersReducer(state = initialState, action) {
    switch (action.type) {
        case gstOrdersTypes.GET_GST_ORDERS_REQUEST:
            return { ...state, loading: true };

        case gstOrdersTypes.GET_GST_ORDERS_SUCCESS:
            return { ...state, data: action.payload, loading: false };

        case gstOrdersTypes.ACTION_GST_ORDER_REQUEST:
            return { ...state, modalBtnLoading: true };

        case gstOrdersTypes.ACTION_GST_ORDER__SUCCESS:
            return { ...state, modalBtnLoading: false, error: false };

        case gstOrdersTypes.ACTION_GST_ORDER__ERROR:
            return { ...state, error: true, modalBtnLoading: false };

        default:
            return state;
    }
}