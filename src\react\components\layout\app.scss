//Tab
.Polaris-Tabs__Wrapper{
    border-bottom: .1rem solid var(--p-border-subdued) !important;
    padding: 0 3.2rem;
    .Polaris-Tabs{
        .Polaris-Tabs__Tab{
            .Polaris-Tabs__Title{
                color: rgb(32,34,35);
                padding: 0.8rem 1.3rem;
            }
            &:before,&:after {
                background-color:none !important;
            }
        }
        .Polaris-Tabs__Tab:hover{
            .Polaris-Tabs__Title::before {
                background-color: var(--p-action-primary);
            }
        }
        .Polaris-Tabs__Tab--selected{
            .Polaris-Tabs__Title{
                color: var(--p-action-primary) !important;
            }
        }
    }
}
.Polaris-Frame__TopBar{
    div{
        box-shadow: none !important;
    }
}

//product-slider
.image-wrapper{
  h2{
    margin-bottom: 10px;
  }
    .image-div{
      width: 250px;
      .img-div{
        height: 290px;
        .product-image{
          width: 250px;
          height: 270px;
      }
      }
      .product-title {
            text-align: center;
            padding-bottom: 10px;
            text-transform: capitalize;
            span {
                font-size: 16px;
                font-weight: 500;
            }
          }
    }
}
.image-wrapper {
    .slick-list {
      .slick-slide {
        margin-bottom: 0px;
        padding-right: 15px;
        padding-left: 26px;
      }
    }
    .slick-next {
      right: -20px;
      z-index: 1;
      @media (max-width: 767px) {
        right: -5px;
      }
    }
    // .product-slider:after {
    //   content: "";
    //   clear: both;
    //   display: table;
    // }
    .slick-prev {
      left: -20px;
      z-index: 1;
      @media (max-width: 767px) {
        left: -5px;
      }
      &::before {
        content: "";
      }
    }
    .slick-next {
      &::before {
        content: "";
      }
    }
    .image-wrap{
      margin-left: 50px;
    }
  }

  // Introducation tour
// Tour Section
.tour_section{
  .tour_item{
      display: flex;
      align-items: center;
      div:first-child {
          margin-right: 10px;
      }
      div:nth-child(2) {
          flex: 1;
      }
  }
  .Polaris-ProgressBar__Indicator{
      background-color: var(--p-action-primary);
  }
}
//Tour Helper
.tour_helper_box {
  max-width: 400px;
  .Polaris-Button--primary{
      background: #008060;
      color: white;
      opacity: 1;
  }
}
.tour_helper_mask rect {
  fill: #212b36 e6;
}
.tour_helper_mask svg {
  vertical-align: top;
}
.tour_helper_mask {
  opacity: 0.8;
}