import { shopConfig } from "./settings";
import axiosInstance from "./axiosInstance";

export let AuthUsers = null;

const getStoreDetails = async () => {
    return await axiosInstance.get('/priceplan/check');
}

export const setAuthorization = async (callback) => {
    const response = await getStoreDetails();
    if (response) {
        const store = response.data;
        if(store.statusCode==200){
            localStorage.setItem('is_paid', 1);
        }else{
            localStorage.setItem('is_paid', 0);
        }
        AuthUsers = { shopDomain: store.shopDomain, name: store.name,isPaid:store.isPaid,isLimitExpired:store.isLimitExpired}
        // AuthUsers = { shopDomain: store.shopDomain, name: store.name,isPaid:store.isPaid,isLimitExpired:store.isLimitExpired}
    }
    callback(AuthUsers);
}

// export const redirectLogin = async () => {
//     window.location.href = `https://${shopConfig.shopOrigin}/admin/apps`;
// }