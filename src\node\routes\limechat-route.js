module.exports = (app) => {
  const router = require("express").Router();
  const orderController = require("../controllers/limeChat/orderController");
  const returnRefundController = require("../controllers/limeChat/returnRefundController");
  const crmController = require("../controllers/limeChat/crmController");

  // 📦 Order Management
  router.get("/orders", orderController.getAllOrders); // Fetch all orders
  router.post("/order/details", orderController.getOrderDetails); // Fetch specific order details

  // 🧾 Invoice Management
  router.post("/invoice/details", orderController.getInvoiceDetails); // Get invoice based on order number

  // ❌ Order Cancellation
  router.post("/order/cancel-eligibility", orderController.checkCancelEligibility); // Check if order can be canceled
  router.post("/order/cancel", orderController.orderCancel); // Check if order can be canceled

  // 💰 Refund Management
  router.get("/refund/status", returnRefundController.checkRefundStatus); // Check refund status
  router.post("/refund/track", returnRefundController.trackRefundProgress); // Track refund progress

  // 🔄 Return Order Tracking
  router.get("/return/orders", orderController.getReturnOrders); // Get return orders
  router.post("/return/order/details", orderController.getOrderDetails); // Fetch return order details

  // 🎫 CRM Ticketing System
  router.get("/crm/ticket/status", crmController.trackTicketStatus); // Track CRM ticket
  router.post("/crm/ticket/create", crmController.createTicket); // Escalate CRM ticket

  // API Routes
  app.use("/limechat/", router);
};
