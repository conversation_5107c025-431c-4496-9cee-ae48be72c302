const Sequelize = require("sequelize");
module.exports = (sequelize) => {
  return sequelize.define("cfa_stocks", {
    id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      unique: true
    },
    store_id: {
      allowNull:false,
      type: Sequelize.INTEGER,
      references: {
        model: 'stores',
        key: 'id'
      },
    },
    cfa_id: {
      type: Sequelize.INTEGER,
      references: {
        model: 'cfa_locations',
        key: 'id'
      },
    },
    cfa_type_id: {
        type: Sequelize.INTEGER,
        references: {
          model: 'cfa_types',
          key: 'id'
        },
      },
    sku: {
      type: Sequelize.STRING
    },
    stocks:{
        type: Sequelize.INTEGER
    }
  },
    {
      indexes: [
        {
          unique: true,
          fields: ['store_id', 'cfa_id','cfa_type_id','sku']
        }
      ]
    });
};