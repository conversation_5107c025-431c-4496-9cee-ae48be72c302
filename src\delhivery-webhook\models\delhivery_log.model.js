const Sequelize = require("sequelize");
module.exports = (sequelize) => {
  return sequelize.define("delhivery_log", {
    id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      unique: true
    },
    store_id: {
      allowNull:false,
      type: Sequelize.INTEGER,
      references: {
        model: 'stores',
        key: 'id'
      },
    },
    order_id: {
      allowNull:false,
        type: Sequelize.INTEGER,
        references: {
          model: 'orders',
          key: 'id'
        },
      },
      status: {
      type: Sequelize.STRING
    },
    reference_number: {
      type: Sequelize.STRING,
    },
    waybill_number: {
      allowNull:false,
      type: Sequelize.STRING,
    },
    response_message: {
      type: Sequelize.STRING(500),
    },
    packing_slip_url:{
      type: Sequelize.STRING(500)
    },
    order_id:{
      type:Sequelize.STRING
    },
    line_item_id:{
      type:Sequelize.STRING
    },
    order_name:{
      type:Sequelize.STRING
    },
    return_order_name:{
      type:Sequelize.STRING
    },
    cfa_name:{
      type:Sequelize.STRING,
      defaultValue:null
    },
    is_return:{
      type:Sequelize.ENUM("0","1"),
      defaultValue:"0"
    },
    pickup_date: {
      type: Sequelize.STRING,
      defaultValue: null
    },
    rto_delivered_date: {
      type: Sequelize.STRING,
      defaultValue: null
    },
    is_cancel:{
      type:Sequelize.ENUM("0","1"),
      defaultValue:"0"
    }
  },
    {
      indexes: [
        {
          name: 'unique_index',
          unique: true,
          fields: ['store_id','waybill_number','order_name','is_cancel','is_return','return_order_name']
        }
      ]
    });
};