const { default: ShopifyApi } = require('@shopify/shopify-api');
const axios = require('axios');


module.exports = class FulfillmentOrder {
    constructor(shop, token) {
        this.client = new ShopifyApi.Clients.Rest(shop, token)
        this.options = {
            headers: {
                'X-Shopify-Access-Token': token,
                'Content-Type': 'application/json'
            }
        }
        this.shop = shop
        this.path = `https://${shop}/admin/api/2023-07`
    }

    get = (order_id) => {
        return new Promise(async (resolve, reject) => {
            let path = this.path + `/orders/${order_id}/fulfillment_orders.json`;
            await axios.get(path, this.options)
                .then(res => {
                    resolve({ status: true, data: res.data.fulfillment_orders })
                })
                .catch(err => {
                    // console.log('Error get fulfillOrder', err)
                    resolve({ status: false, data: err?.response?.status })
                })
        })
    }
}