const { default: ShopifyApi } = require("@shopify/shopify-api");
const axios = require("axios");

module.exports = class FulfillmentOrder {
  constructor(shop, token) {
    this.client = new ShopifyApi.Clients.Graphql(shop, token);
    this.options = {
      headers: {
        "X-Shopify-Access-Token": token,
        "Content-Type": "application/json",
      },
    };
    this.shop = shop;
    this.path = `https://${shop}/admin/api/2025-07/graphql.json`;
  }

  get = async (order_id) => {
    try {
      const path = this.path;
      const GID = `gid://shopify/Order/${order_id}`;
      const query = `
        query GetFulfillmentOrders($orderGID: ID!) {
          order(id: $orderGID) {
            fulfillmentOrders(first: 10) {
              edges {
                node {
                  id
                  status
                  assignedLocation {
                    location {
                      id
                    }
                  }
                }
              }
            }
            lineItems(first: 10) {
              edges {
                node {
                  id
                }
              }
            }
          }
        }
      `;
      const variables = { orderGID: GID };
      const res = await axios.post(
        path,
        { query, variables },
        this.options
      );
      // Convert GraphQL response to REST-like format for compatibility
      const orderData = res.data?.data?.order;
      // Build a map of all order line items for easy lookup, with numeric IDs
      const allLineItems = orderData?.lineItems?.edges?.map(e => ({
        line_item_id: e.node.id ? e.node.id.split('/').pop() : null
      })) || [];

      // For each fulfillment order, attach the line_items array as expected by the controller, with numeric IDs
      const fulfillmentOrders =
        orderData?.fulfillmentOrders?.edges?.map(edge => {
          const node = edge.node;
          return {
            id: node.id ? node.id.split('/').pop() : null,
            status: node.status?.toLowerCase(),
            assigned_location_id: node.assignedLocation?.location ? node.assignedLocation.location.id.split('/').pop() : null,
            line_items: allLineItems // Attach all line items to each fulfillment order for compatibility
          };
        }) || [];

      return { status: true, data: fulfillmentOrders };
    } catch (err) {
      // console.log('Error get fulfillOrder', err)
      return { status: false, data: err?.response?.status || err?.message };
    }
  };
};
