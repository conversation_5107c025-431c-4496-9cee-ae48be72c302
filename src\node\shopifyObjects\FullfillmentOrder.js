const { default: ShopifyApi } = require("@shopify/shopify-api");
const axios = require("axios");

module.exports = class FulfillmentOrder {
  constructor(shop, token) {
    this.client = new ShopifyApi.Clients.Graphql(shop, token);
    this.options = {
      headers: {
        "X-Shopify-Access-Token": token,
        "Content-Type": "application/json",
      },
    };
    this.shop = shop;
    this.path = `https://${shop}/admin/api/2025-07/graphql.json`;
  }

  get = async (order_id) => {
    try {
      const path = this.path;
      const GID = `gid://shopify/Order/${order_id}`;
      const query = `
        query GetFulfillmentOrders($orderGID: ID!) {
          order(id: $orderGID) {
            fulfillmentOrders(first: 10) {
              edges {
                node {
                  id
                  status
                  assignedLocation {
                    location {
                      id
                    }
                  }
                  lineItems(first: 10) {
                    edges {
                      node {
                        id
                        lineItem {
                          id
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      `;
      const variables = { orderGID: GID };
      const res = await axios.post(path, { query, variables }, this.options);

      const orderData = res.data?.data?.order;
      
      const fulfillmentOrders = orderData?.fulfillmentOrders?.edges?.map(edge => {
        const node = edge.node;
        const line_items = node.lineItems?.edges?.map(li => ({
          fulfillment_order_id: node.id?.split("/").pop() || null,
          id: li.node.id?.split("/").pop() || null,
          line_item_id: li.node.lineItem?.id?.split("/").pop() || null
        })) || [];

        return {
          id: node.id ? node.id.split("/").pop() : null,
          status: node.status?.toLowerCase(),
          assigned_location_id: node.assignedLocation?.location?.id?.split("/").pop() || null,
          line_items
        };
      }) || [];

      return { status: true, data: fulfillmentOrders };
    } catch (err) {
      console.log("FulfillmentOrder get===>", err);
      return { status: false, data: err?.response?.status || err?.message };
    }
  };
};
