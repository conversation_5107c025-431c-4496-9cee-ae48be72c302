module.exports = app => {
  const cfaController = require("../controllers/cfa-controller.js");
  const router = require("express").Router();
  const upload = require('../helper/uploadFile')


  router.get("/get", cfaController.getCfaData)
  router.get("/all", cfaController.getAllCfa)
  router.post("/update/:id", cfaController.editCfaData)
  router.post("/create", cfaController.createCfaData)
  router.delete("/delete/:id", cfaController.deleteCfaData)
  router.post("/import", upload.single('file'), cfaController.importCfaData)
  router.post("/import-emailData", upload.single('file'), cfaController.importCfaPlantEmail)
  router.post("/import-invoiceData", upload.single('file'), cfaController.importInvoiceData)
  router.post("/pincode/import", upload.single('file'), cfaController.importCityStateData)
  router.get("/pincode/export", cfaController.exportServiceablePincodeDummyFile)

  //CFA Plant Location
  router.get("/list", cfaController.getAllCfaData) //get all cfa list
  router.post("/location/import", upload.single('file'), cfaController.importCfaPlantLocation)
  router.get("/location/export", cfaController.exportCfaPlantLocation)

  //CFA Stock 
  router.post("/stock/import", upload.single('file'), cfaController.importCfaStockMapping)
  router.get("/stock/export", cfaController.cfaStockExport)

  //API Routes
  app.use('/api/cfa', router);
};