const Sequelize = require("sequelize");
module.exports = (sequelize) => {
  return sequelize.define("cfa_plant_locations", {
    id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      unique: true
    },
    store_id: {
      allowNull: false,
      type: Sequelize.INTEGER,
      references: {
        model: 'stores',
        key: 'id'
      },
    },
    plant_code: {
      type: Sequelize.STRING,
    },
    plant_name: {
      type: Sequelize.STRING,
    },
    search: {
      type: Sequelize.STRING
    },
    pincode: {
      type: Sequelize.STRING
    },
    city: {
      type: Sequelize.STRING
    },
    state: {
      type: Sequelize.STRING
    },
    state_code: {
      type: Sequelize.STRING
    },
    country: {
      type: Sequelize.STRING,
    },
    address: {
      type: Sequelize.STRING(500)
    },
    phone: {
      type: Sequelize.STRING
    },
    email: {
      type: Sequelize.STRING(500),
    },
    pincode: {
      type: Sequelize.STRING,
    },
    is_active: {
      type: Sequelize.BOOLEAN,
      defaultValue: true,
    },
    order_limit: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    total_order: {
      type: Sequelize.INTEGER,
      defaultValue: 0,
    }
  },
    {
      indexes: [
        {
          unique: true,
          fields: ['store_id', 'plant_code']
        }
      ]
    });
};