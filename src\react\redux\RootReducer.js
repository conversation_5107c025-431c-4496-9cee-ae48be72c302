import { combineReducers } from 'redux';
import { persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import toastReducer from './toast/toastReducer';
import gstOrdersReducer from './gstOrders/gstOrdersReducer';
import orderReducer from './orders/ordersReducer';
import cfaReducer from './cfa/cfaReducer';
import settingsReducer from './settings/settingsReducer';


const presistConfig = {
	key: 'root',
	storage,
	whitelist: [],
};

const rootReducer = combineReducers({
	toast: toastReducer,
	gstOrders: gstOrdersReducer,
	order: orderReducer,
	cfa: cfaReducer,
	settings: settingsReducer
});


export default persistReducer(presistConfig, rootReducer);
