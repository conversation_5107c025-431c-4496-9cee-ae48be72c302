const Sequelize = require("sequelize");
module.exports = (sequelize) => {
  return sequelize.define("order_item", {
    id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      unique: true
    },
    store_id: {
      type: Sequelize.INTEGER,
      allowNull:false,
      references: {
        model: 'stores',
        key: 'id'
      },
    },
    order_id: {
      type: Sequelize.INTEGER,
      allowNull:false,
      references: {
        model: 'order_item_splits',
        key: 'id'
      },
    },
    line_item_id: {
      allowNull:false,
      type: Sequelize.STRING
    },
    product_id: {
      allowNull:true,
      type: Sequelize.STRING,
    },
    variant_id: {
      allowNull:true,
      type: Sequelize.STRING,
    },
    product_title: {
      type: Sequelize.STRING
    },
    variant_title: {
      type: Sequelize.STRING,
    },
    quantity: {
      allowNull:false,
      type: Sequelize.INTEGER
    },
    price: {
      allowNull:false,
      type: Sequelize.DOUBLE(11, 2),
      defaultValue: '0.00'
    },
    sku: {
      type: Sequelize.STRING
    },
    item_discount_amount: {
      type: Sequelize.DOUBLE(11, 2),
      defaultValue: '0.00'
    },
    is_pushed_delhivery: {
      type: Sequelize.DataTypes.ENUM('Yes', 'No'),
      defaultValue: 'No'
    },
    is_pushed_sap: {
      type: Sequelize.DataTypes.ENUM('Yes', 'No'),
      defaultValue: 'No'
    }
  },
    {
      indexes: [
        {
          unique: true,
          fields: ['store_id','order_id','line_item_id']
        }
      ]
    });
};