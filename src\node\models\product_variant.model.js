const Sequelize = require("sequelize");
module.exports = (sequelize) => {
  return sequelize.define("product_variant", {
    id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      unique: true
    },
    store_id: {
      allowNull:false,
      type: Sequelize.INTEGER,
      references: {
        model: 'stores',
        key: 'id'
      },
    },
    sku: {
      allowNull:false,
      type: Sequelize.STRING(100),
    },
    grams: {
      allowNull:false,
      type: Sequelize.DOUBLE
    },
    price: {
      allowNull:false,
      type: Sequelize.DOUBLE(50, 2),
      defaultValue: '0.00'
    },
    title: {
      type: Sequelize.STRING,
    },
    weight: {
      type: Sequelize.DOUBLE,
    },
    barcode: {
      type: Sequelize.STRING,
    },
    option_1: {
      type: Sequelize.STRING
    },
    option_value_1: {
      type: Sequelize.STRING,
    },
    option_2: {
      type: Sequelize.STRING
    },
    option_value_2: {
      type: Sequelize.STRING
    },
    option_3: {
      type: Sequelize.STRING
    },
    option_value_3: {
      type: Sequelize.STRING
    },
    taxable: {
      type: Sequelize.STRING
    },
    position: {
      type: Sequelize.STRING
    },
    product_id: {
      type: Sequelize.INTEGER,
      references: {
        model: 'products',
        key: 'id'
      },
    },
    weight_unit: {
      type: Sequelize.STRING
    },
    compared_at_price: {
      allowNull:true,
      type: Sequelize.DOUBLE(50, 2),
      defaultValue: '0.00'
    },
    inventory_policy: {
      type: Sequelize.STRING
    },
    inventory_item_id: {
      type: Sequelize.STRING
    },
    requires_shipping: {
      type: Sequelize.STRING
    },
    inventory_quantity: {
      type: Sequelize.INTEGER
    },
    fulfillment_service: {
      type: Sequelize.STRING
    },
    inventory_managment: {
      type: Sequelize.STRING
    },
    shopify_variant_id: {
      type: Sequelize.STRING
    },
  },
    {
      indexes: [
        {
          unique: true,
          fields: ['store_id', 'shopify_variant_id']
        }
      ]
    });
};