const dbConnection = require("../../models");
const CONFIG = require('../../config/config_' + [process.env.NODE_ENV || 'local'] + '.json');
let buff = `${CONFIG.sap.userId}:${CONFIG.sap.pass}`
let accessToken = new Buffer(buff)
accessToken = accessToken.toString('base64')
const { Op, Sequelize } = require('sequelize');
const helper = require('../../helper/helper');
const moment = require('moment');
const xlsx = require('json-as-xlsx')
const emailController = require('.././email-controller');
const path = require('path');
const fs = require('fs');
const s3 = require("../../helper/awsClient");
const { processWarrantyPurchase } = require("./extendedWarrantyService-controller");


exports.getAllOrder = async (req, res) => {
  try {
    // Destructure query parameters
    const { shop, page, per_page, search, middleware_status, start_date, end_date, sort } = req.query;

    // Default options and sorting order
    let options = {};
    let sortOrder = sort === 'oldest' ? 'ASC' : 'DESC';

    // Search filter
    if (search) {
      options[Op.or] = [
        { order_name: { [Op.like]: `%${search}%` } },
        { shopify_order_name: { [Op.like]: `%${search}%` } },
        { sap_order_number: { [Op.like]: `%${search}%` } },
        { sku: { [Op.like]: `%${search}%` } }
      ];
    }

    // SAP status filter
    if (middleware_status) {
      options.middleware_status = { [Op.like]: `%${middleware_status}%` };
    }

    // Get shop details
    const shopResponse = await dbConnection.shop.findOne({ where: { myshopify_domain: shop } });

    // Date filter if provided
    if (start_date && end_date && shopResponse) {
      const { start, end } = helper.getStartEndDate(start_date, end_date, shopResponse.iana_timezone);
      options.order_created_at = {
        [Op.lte]: end,
        [Op.gte]: start
      };
    }

    // Pagination setup
    const { limit, offset } = helper.getPagination(page, per_page);

    // Fetch Warranty orders from DB
    const excludeArray = ["order_item_split_id", "shopify_order_id", "shopify_product_id", "shopify_variant_id", "line_item_id", "gateway", "createdAt", "updatedAt"];
    const data = await dbConnection.extendedWarrantyOrder.findAndCountAll({
      attributes: { exclude: excludeArray },
      where: options,
      limit,
      offset,
      order: [['order_created_at', sortOrder]],
      include: [
        { model: dbConnection.sapLog, attributes: { exclude: ['store_id', 'line_item_id', 'return_order_name', 'plant_code', 'return_created_date', 'cn_created_date', 'is_clear', 'createdAt', 'updatedAt'] }, as: "sapLog" },
        { model: dbConnection.orderCustomer, attributes: { exclude: ['createdAt', 'updatedAt'] }, as: 'orderCustomers' },
        { model: dbConnection.orderAddress, attributes: { exclude: ['createdAt', 'updatedAt'] }, as: 'orderAddresses' }
      ]
    });

    // Prepare response
    const response = helper.getPagingData(data, page, limit);
    res.status(CONFIG.status.SUCCESS).send(response);
  } catch (error) {
    console.error('Error fetching orders:', error);
    res.status(CONFIG.status.FAILED).send(CONFIG.msg.ERROR);
  }
};

exports.exportWarrantyOrders = async (req, res) => {
  try {
    res.status(CONFIG.status.SUCCESS).send(CONFIG.msg.SUCCESS);

    let options = {};
    const { search, start_date, end_date, middleware_status } = req.body.filters;
    const { shop } = req.query;

    if (req.body.isAllResource) {
      if (search) {
        options = {
          [Op.or]: [
            { order_name: { [Op.like]: `%${search}%` } },
            { shopify_order_name: { [Op.like]: `%${search}%` } },
            { sap_order_number: { [Op.like]: `%${search}%` } },
            { sku: { [Op.like]: `%${search}%` } },
          ],
        };
      }

      // Filter by SAP status if provided
      if (middleware_status) options.middleware_status = { [Op.like]: `%${middleware_status}%` };

      let shopResponse = await dbConnection.shop.findOne({ where: { myshopify_domain: shop } })
      if (start_date && end_date && shopResponse) {
        const { start, end } = helper.getStartEndDate(start_date, end_date, shopResponse.iana_timezone);
        options.order_created_at = { [Op.lte]: end, [Op.gte]: start };
      }
    } else {
      options = { order_name: req.body.order_ids };
    }

    const orderDatas = await dbConnection.extendedWarrantyOrder.findAll({
      where: options,
      include: [
        { model: dbConnection.sapLog, as: "sapLog" },
        { model: dbConnection.orderCustomer, as: 'orderCustomers' }
      ],
    });

    const parsedOrderData = JSON.parse(JSON.stringify(orderDatas));

    const finalData = parsedOrderData.map(data => {
      const sapLogArray = data.sapLog || [];
      const sapForwardData = sapLogArray.filter(item => item.is_return === '1' || item.is_cancel === '1');
      const sapReverseData = sapLogArray.filter(item => item.is_return === '0' && item.is_cancel === '0');

      return {
        shopifyOrder: data.shopify_order_name,
        Order: data.order_name,
        customerName: `${data.orderCustomers?.first_name || ''} ${data.orderCustomers?.last_name || ''}`,
        sapStatus: data.sap_status,
        middlewareStatus: data.middleware_status,
        amount: data.order_amount,
        orderCreatedDate: data.order_created_at ? moment.utc(data.order_created_at).tz('Asia/Kolkata').format('L') : null,
        mainOrderNumber: data.main_order_name,
        warrantyCertificateUrl: data.warranty_certificate_url,
        gateway: data.gateway,
        sapDeliveryNumber: sapForwardData[0]?.sap_delivery_number || null,
        sapOrderNumber: sapForwardData[0]?.sap_order_number || null,
        sapBillingNumber: sapForwardData[0]?.sap_billing_number || null,
        cancelSapDeliveryNumber: sapReverseData[0]?.sap_delivery_number || null,
        cancelSapOrderNumber: sapReverseData[0]?.sap_order_number || null,
        cancelSapBillingNumber: sapReverseData[0]?.sap_billing_number || null,
      };
    });

    const sheetData = [
      {
        sheet: 'Warranty Order Data',
        columns: [
          { label: 'Shopify Order Id', value: 'shopifyOrder' },
          { label: 'Order Id', value: 'Order' },
          { label: 'Customer Name', value: 'customerName' },
          { label: 'SAP Status', value: 'sapStatus' },
          { label: 'Middleware Status', value: 'middlewareStatus' },
          { label: 'Amount', value: 'amount' },
          { label: 'Order Created Date', value: 'orderCreatedDate' },
          { label: 'Main Order Number', value: 'mainOrderNumber' },
          { label: 'Warranty Certificate Url', value: 'warrantyCertificateUrl' },
          { label: 'Sap Delivery Number', value: 'sapDeliveryNumber' },
          { label: 'Sap Order Number', value: 'sapOrderNumber' },
          { label: 'Sap Billing Number', value: 'sapBillingNumber' },
          { label: 'Gateway', value: 'gateway' },
          { label: 'Cancel SAP Delivery Number', value: 'cancelSapDeliveryNumber' },
          { label: 'Cancel SAP Billing Number', value: 'cancelSapBillingNumber' },
          { label: 'Cancel SAP Order Number', value: 'cancelSapOrderNumber' },
        ],
        content: finalData,
      },
    ];

    // Write data to XLSX file
    const buffer = xlsx(sheetData, { writeOptions: { type: 'buffer', bookType: 'xlsx' } });
    const fileName = 'warrantyOrderData.xlsx';
    const filePath = path.join(__dirname, '../../uploads', fileName);

    fs.writeFileSync(filePath, buffer);

    const fileContent = fs.readFileSync(filePath);
    const fileData = {
      Bucket: CONFIG.aws.bucketName,
      Key: `exportWarrantyOrderDataFile/${fileName}`,
      Body: fileContent,
      ACL: 'public-read',
      ContentType: 'application/xlsx',
    };

    // Upload file to S3
    const awsData = await s3.upload(fileData).promise();
    fs.unlinkSync(filePath);  // Clean up the file after upload

    if (awsData) {
      console.log('Export File URL:', awsData.Location);
      await emailController.sendExportDataEmail(fileName, awsData.Location, CONFIG.email.exportto, null, 'Exported Warranty Order Data');
    }
  } catch (error) {
    console.error('Error exporting warranty orders:', error);
    res.status(CONFIG.status.ERROR).send(CONFIG.msg.ERROR);
  }
};

exports.warrantyPurchase = async (req, res) => {
  try {
    const { isAllResource, order_ids } = req.body;

    // Build query options
    const options = isAllResource
      ? { middleware_status: ["INVOICED", "FAILED"], sap_status: "invoiced" }
      : { order_name: order_ids, middleware_status: ["INVOICED", "FAILED"], sap_status: "invoiced" };

    // Fetch orders
    const ordersData = await dbConnection.extendedWarrantyOrder.findAll({
      where: options,
      include: [
        { model: dbConnection.orderItemSplit, attributes: ["product_serial_number", "sku", "product_title"], as: "orderItemSplit" },
        { model: dbConnection.orderCustomer, attributes: { exclude: ["createdAt", "updatedAt"] }, as: "orderCustomers" },
        { model: dbConnection.orderAddress, attributes: { exclude: ["createdAt", "updatedAt"] }, as: "orderAddresses" },
      ],
      raw: true,
      nest: true,
    });

    if (!ordersData.length) return res.status(200).json({ statusCode: 200, message: "No orders found." });

    // Process orders using the helper function
    processWarrantyPurchase(ordersData);

    res.status(200).json({ statusCode: 200, message: "Orders processed successfully." });
  } catch (error) {
    console.error("Error in warrantyPurchase API:", error);
    res.status(500).json({ statusCode: 500, message: "Internal server error." });
  }
};

exports.getCustomerOrderForWarranty = async (req, res) => {
  try {
    const { phone, email, sortOrder = 'ASC' } = req.query;

    // Validate required parameters
    if (!phone && !email) {
      return res.status(400).json({ success: false, message: "Either phone or email parameter is required." });
    }

    // Validate phone number
    if (phone && (!/^\d{10}$/.test(phone))) {
      return res.status(400).json({ success: false, message: "Phone number must be a valid 10-digit numeric value." });
    }

    // Validate email address
    if (email && (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email))) {
      return res.status(400).json({ success: false, message: "Email address is invalid." });
    }


    // Calculate date range (10 months to 1 year)
    const today = moment();
    const startDate = today.clone().subtract(1, 'year').toDate();
    const endDate = today.clone().subtract(10, 'months').toDate();

    // Normalize input phone
    const cleanedPhone = phone ? phone.replace(/^(\+91)?/, '') : null;

    const whereConditions = [];
    if (email) {
      whereConditions.push({ customer_email: email });
    }
    if (cleanedPhone) {
      whereConditions.push(
        Sequelize.where(
          Sequelize.fn('REPLACE', Sequelize.col('phone_number'), '+91', ''),
          cleanedPhone
        )
      );
    }

    const OrderData = await dbConnection.orderItemSplit.findAll({
      attributes: [
        'id', 'shopify_product_id', 'shopify_order_id', 'shopify_variant_id', 'order_amount', 'product_title', 'sku',
        'order_created_at', 'order_name', 'discount', 'product_serial_number', 'delivered_at', 'shopify_order_name',
        'financial_status', 'shipment_status', 'quantity', 'order_status',
        [Sequelize.col('product.handle'), 'handle'],
        [Sequelize.col('product.id'), 'product_id'],
      ],
      where: {
        order_created_at: {
          [Op.between]: [startDate, endDate],
          [Op.gt]: new Date('2025-01-01') // Only after 1 Jan 2025
        },
        middleware_status: 'DELIVERED',
        is_extended_warranty: '0',
      },
      include: [
        {
          model: dbConnection.orderCustomer,
          as: 'orderCustomers',
          attributes: [],
          where: {
            [Op.or]: whereConditions
          },
          required: true,
        },
        { model: dbConnection.product, as: 'product', attributes: [], required: true },
      ],
      order: [['id', sortOrder.toUpperCase()]],
      raw: true,
    });

    const orderWithImages = await Promise.all(OrderData.map(async order => {
      if (order.product_id) {
        const images = await dbConnection.productImage.findOne({ where: { product_id: order.product_id }, raw: true });
        order.product_image_url = images ? images.image_url : null;
      }

      if (order.product_serial_number) {
        order.product_serial_number = order.product_serial_number.slice(0, -4);
      }

      return order;
    }));

    if (orderWithImages.length) {
      return res.status(CONFIG.status.SUCCESS).json({ success: true, message: 'Order fetched successfully.', data: orderWithImages });
    }
    return res.status(CONFIG.status.SUCCESS).json({ success: false, message: 'No records found.' });

  } catch (error) {
    console.error("Error in getCustomerOrderForWarranty:", error);
    return res.status(500).json({ message: "Internal server error." });
  }
};

exports.verifySerialNumber = async (req, res) => {
  try {
    const { serialNumbers } = req.body;

    if (!Array.isArray(serialNumbers) || serialNumbers.length === 0) {
      return res.status(400).send({ success: false, message: 'Invalid input, serialNumbers array is required.' });
    }

    const validEntries = [];
    const invalidEntries = [];

    for (let i = 0; i < serialNumbers.length; i++) {
      const { order_name, product_serial_number } = serialNumbers[i];

      if (!order_name || !product_serial_number) {
        invalidEntries.push({ order_name, product_serial_number, error: 'Missing required fields' });
        continue; s
      }

      const orders = await dbConnection.orderItemSplit.findOne({ where: { order_name, product_serial_number } });
      if (orders) {
        validEntries.push({ order_name, product_serial_number, status: 'valid' });
      } else {
        invalidEntries.push({ order_name, product_serial_number, error: 'invalid' });
      }
    }

    if (invalidEntries.length > 0) {
      return res.status(404).json({ success: false, message: 'Some serial numbers are invalid', validEntries, invalidEntries });
    }

    return res.status(200).json({ success: true, message: 'All serial numbers verified successfully', validEntries });
  } catch (error) {
    console.error('Error verifying serial numbers:', error);
    res.status(500).send({ success: false, message: 'An error occurred while verifying serial numbers.' });
  }
};