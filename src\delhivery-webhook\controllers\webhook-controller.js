const dbConnection = require("../models");
const axios = require('axios')
//const BASE_URL = process.env.NODE_ENV == 'production' ? "https://symphonyapp.lucentinnovation.com" : process.env.NODE_ENV == 'local' ? "http://localhost:5000" : "https://appsymphony.lucentinnovation.com"
const BASE_URL = "https://appsymphony.lucentinnovation.com/"

const axiosConfig = {
  headers: {
    shop: "symphony.myshopify.com",
  },
};

const DELHIVERY_SHIPPING_STATUS = {
  'In Transit': 'in_transit',
  'Dispatched': 'out_for_delivery',
  'Delivered': 'delivered'
};

const callCloudApi = async (orderName) => makeApiRequest('/delhivery-webhook/call-cloud-api', { orderName });
const returnSaleOrderSap = async (orderData) => makeApiRequest('/delhivery-webhook/returnsaleordersap', { orderData });
const cancelOrder = async (orderData) => makeApiRequest('/delhivery-webhook/cancelorder', { orderData });
const updateFulfillmentEvent = async (paramObj) => makeApiRequest('/delhivery-webhook/updatefulfillmentevent', { paramObj });

exports.webhook = async (data) => {
  try {
    const status = data.Shipment.Status.Status;
    const statusType = data.Shipment.Status.StatusType;
    const orderName = data.Shipment.ReferenceNo.split("-R")[0];
    const shipmentStaus = getStatusValue(DELHIVERY_SHIPPING_STATUS, status);
    let orderData = await this.getorderSplitDataById(orderName);
    if (!orderData) {
      return { status: false, message: "orderData is not found" };
    }

    let orderObj = {
      shipment_status: status,
    };
   
    const previousMiddlewareStatus = orderData.data.middleware_status
    let fulfillmentevent = false
    if (status == "Delivered" && statusType == "DL") {
      orderObj.middleware_status = "DELIVERED";
      orderObj.is_replaceable = "1";
      orderObj.delivered_at = new Date().toISOString();
      fulfillmentevent = true
      await dbConnection.orderItemSplit.update(orderObj, { where: { order_name: orderName }}); //need to check
      let cloudData = await dbConnection.childSku.findOne({ where: { child_sku: orderData.data.sku, is_cloud: "1" }});
      if (cloudData) {
        callCloudApi(orderName);
      }
    }
    
    if (status == "Dispatched" && statusType == "UD") {
      fulfillmentevent = true
    }
    
    if (status == "In Transit" && statusType == "UD") {
      fulfillmentevent = true
      orderObj.is_pickup = "1";
      orderObj.middleware_status = "IN TRANSIT";
      let pickupDate = new Date().toISOString();
      if(previousMiddlewareStatus != "IN TRANSIT"){
        await dbConnection.delhiveryLog.update({ pickup_date: pickupDate },{ where: { order_name: orderName, is_cancel: "0", is_return: "0" } });
      }
    }

    if ((status == "DTO" || status == "dto") && statusType == "DL") {
      orderObj.middleware_status = "RETURNED TO WAREHOUSE";
      let dbRes = await dbConnection.orderCancellation.create({
        order_name: orderData.data.order_name,
        status_type: "order_replacement_webhook",
        return_order_name: orderData.data.return_order_name,
        middleware_status: "RETURNED TO WAREHOUSE",
      });
      if (dbRes.dataValues.id) {
        returnSaleOrderSap(orderData.data);
      }
    }

    if ((status == "RTO" || status == "rto") && statusType == "DL" && orderData.shipment_status != "delivered") {
      let dbRes = await dbConnection.orderCancellation.create({ order_name: orderData.data.order_name, status_type: "order_cancel_rto",
        return_order_name: orderData.data.return_order_name,
        middleware_status: 'CANCELLED RTO ORDERS',
      });
      if (dbRes.dataValues.id) {
        orderObj.middleware_status= 'RTO RETURNED TO WAREHOUSE'
        cancelOrder(orderData.data);
      }
    }

    if (status == "Canceled" && statusType == "CN") {
      orderObj.middleware_status = "RETURN PICKUP CANCELLED BY CUSTOMER";
      orderObj.order_status = "Completed";
      orderObj.return_applied_status = null;
      orderObj.is_return = "0";
    }

    if (status == "In Transit" && statusType == "PU") {
      orderObj.middleware_status = "RETURN IN TRANSIT";
      let pickupDate = new Date().toISOString()
      await dbConnection.delhiveryLog.update({ pickup_date: pickupDate }, { where: { order_name: orderName, is_return: "1" } })
    }

    if (status == "In Transit" && statusType == "RT") {
      orderObj.middleware_status = "RTO IN TRANSIT";
    }

    await dbConnection.orderItemSplit.update(orderObj, {where: { order_name: orderName }});
    let checkMiddlewareStatus = await this.checkMiddlewareStatus(previousMiddlewareStatus,orderObj.middleware_status);
    if(fulfillmentevent && checkMiddlewareStatus){
      let getFulfillData = await dbConnection.fulfillmentItem.findOne({where: { order_id: orderData.data.id }});
      if (getFulfillData) {
        let shopResponse = await dbConnection.shop.findOne({where: { id: orderData.data.store_id }});
        const paramObj = {
          orderId: orderData.data.shopify_order_id,
          fulfillmentId: getFulfillData.dataValues.fulfillment_id,
          shopResponse: shopResponse,
          shipmentStatus: shipmentStaus,
        };
        const updateEvent = await updateFulfillmentEvent(paramObj);
        return { status: true, message: "Success" };
      } else {
        return { status: false, message: "tracking number is not present" };
      }
    }
    return { status: true, message: "Success" };
  } catch (err) {
    console.log("error==>", err);
    return { status: false, message: err?.message };
  }
};

exports.checkMiddlewareStatus = async (oldmiddlewareStatus, newmiddlewareStatus) => oldmiddlewareStatus !== newmiddlewareStatus;
const getStatusValue = (statusObject, statusKey) => statusObject?.[statusKey] || null;

exports.getorderSplitDataById = (orderName) => {
  return new Promise(async (resolve, reject) => {
      try{
          let getOrderDataByName = await dbConnection.orderItemSplit.findOne({
              where: { order_name: orderName }
          })
          if (getOrderDataByName) {
              let orderCustomer = await dbConnection.orderCustomer.findOne({ where: { order_id: getOrderDataByName.dataValues.shopify_order_id } })
              let orderAddress = await dbConnection.orderAddress.findOne({ where: { order_id: getOrderDataByName.dataValues.shopify_order_id } })
              let orderData = getOrderDataByName.dataValues
              let orderCustomerArr = []
              let orderAddressArr = []
              orderCustomer ? orderCustomerArr.push(orderCustomer.dataValues) : []
              orderAddress ? orderAddressArr.push(orderAddress.dataValues) : []
              orderData.orderCustomers = orderCustomerArr
              orderData.orderAddresses = orderAddressArr
              resolve({ status: true, data: orderData })
          } else {
              resolve({ status: false })
          }
      } catch (err) {
          console.log('Error getorderSplitDataById=====>', err)
      }
  })
}

const makeApiRequest = async (endpoint, data) => {
  try {
    let response = await axios.post(`${BASE_URL}${endpoint}`, data, axiosConfig);
    return response;
  } catch (error) {
    console.log(`Error ${endpoint}`, error);
  }
};