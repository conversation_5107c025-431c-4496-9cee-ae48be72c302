const { default: ShopifyApi, DataType, ApiVersion } = require('@shopify/shopify-api');
const axios = require('axios')

module.exports = class FulfillmentService {
    constructor(shop, token) {
        this.options = {
            headers: {
                'X-Shopify-Access-Token': token,
                'Content-Type': 'application/json'
            }
        }
        this.shop = shop
        this.path = `https://${shop}/admin/api/2021-10/fulfillment_services.json`
    }
    get = () => {
        return new Promise(async (resolve, reject) => {
            axios.get(this.path, this.options)
                .then(res => resolve(res.data.fulfillment_services))
                .catch(err => reject(err.response.data.errors))
        })
    }
    create = (data) => {
        return new Promise(async (resolve, reject) => {
            let bodyObj = {
                "fulfillment_service": data
            }
            await axios.post(this.path, bodyObj, this.options)
                .then((res) => resolve(res.data))
                .catch((err) => resolve(err.response.data.errors));
        })
    }
}