const {
  default: ShopifyApi,
  DataType,
  ApiVersion,
} = require("@shopify/shopify-api");
const axios = require("axios");

module.exports = class FulfillmentService {
  constructor(shop, token) {
    this.options = {
      headers: {
        "X-Shopify-Access-Token": token,
        "Content-Type": "application/json",
      },
    };
    this.shop = shop;
    this.path = `https://${shop}/admin/api/2025-07/graphql.json`;
  }
  get = async () => {
    try {
      const path = this.path;
      const query = `
                query GetFulfillmentServices {
                    shop {
                        fulfillmentServices {
                            callbackUrl
                            serviceName
                            id
                        }
                    }
                }
            `;
      const res = await axios.post(path, { query }, this.options);
      // Convert GraphQL response to array of objects compatible with controller's filter logic
      const services =
        res.data?.data?.shop?.fulfillmentServices?.map((node) => ({
          name: node.serviceName,
          callback_url: node.callbackUrl,
          id: node.id,
        })) || [];
      return services;
    } catch (err) {
      console.log("FulfillmentService get===>", err);
      return [];
    }
  };
  create = async (data) => {
    try {
      const path = this.path;
      const mutation = `
  mutation fulfillmentServiceCreate(
    $name: String!, 
    $callbackUrl: URL!, 
    $requiresShippingMethod: Boolean, 
    $trackingSupport: Boolean, 
    $inventoryManagement: Boolean
  ) {
    fulfillmentServiceCreate(
      name: $name, 
      callbackUrl: $callbackUrl, 
      requiresShippingMethod: $requiresShippingMethod, 
      trackingSupport: $trackingSupport, 
      inventoryManagement: $inventoryManagement
    ) {
      fulfillmentService {
        id
        serviceName
        callbackUrl
        location {
          id
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;
      // Map REST-style keys to GraphQL input keys
      const input = {
        name: data.name,
        callbackUrl: data.callback_url,
        inventoryManagement: data.inventory_management,
        trackingSupport: data.tracking_support,
        requiresShippingMethod: data.requires_shipping_method,
        // fulfillmentOrdersOptIn: data.fulfillment_orders_opt_in, deprecated
        format: data.format,
      };
      const res = await axios.post(
        path,
        { query: mutation, variables: { input } },
        this.options
      );
      const result = res.data?.data?.fulfillmentServiceCreate;
      if (result?.userErrors && result.userErrors.length > 0) {
        return { errors: result.userErrors };
      }
      // Map GraphQL response to controller's expected keys
      const fs = result.fulfillmentService;
      return {
        fulfillment_service: {
          id: fs.id,
          name: fs.serviceName,
          location_id: fs.location?.id || null,
        },
      };
    } catch (err) {
      console.log("FulfillmentService create===>", err);
      return { errors: [err?.response?.data || err?.message] };
    }
  };
};
