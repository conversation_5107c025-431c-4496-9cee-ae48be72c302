import React from 'react';
import ReactDOM from 'react-dom';

import App from './components/layout/App';

window.simply = window.simply || {};
window.cn = function (o) { return "undefined" == typeof o || null == o || "" == o.toString().trim() };
window.cb = function (o) { if (o == 'true') { return true } else { return false } };


ReactDOM.render(
  // <React.StrictMode>
    <App />
  // </React.StrictMode>
  ,
  document.getElementById('root')
);
