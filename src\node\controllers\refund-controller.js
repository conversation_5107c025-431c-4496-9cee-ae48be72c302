const dbConnection = require("../models")
const razorpayController = require('./razorpay-controller');

exports.checkingForRefund = async (orderData) => {
  try {
    let totalPrice = parseFloat(orderData.order_amount)
    let discount = orderData.discount != null && orderData.discount != "null" && orderData.discount != "" ? parseFloat(orderData.discount) : 0
    let refundObj = {
      store_id: orderData.store_id,
      order_name: orderData.order_name,
      amount: totalPrice - discount,
      refund_status: 'pending'
    };
    if (orderData.sap_status !== 'Invoiced') {
      if (orderData.sap_status !== 'Pushed') {
        let shopResponse = await dbConnection.shop.findOne({ where: { id: orderData.store_id } });
        let payId = shopResponse ? await razorpayController.getPaymentId(shopResponse.dataValues.myshopify_domain, shopResponse.dataValues.token, orderData.shopify_order_id) : { status: false };
        let createRefund = await dbConnection.order_refund.create(refundObj);
        if (payId.status && createRefund) {
          await dbConnection.order_refund.update({ payment_id: payId.payId }, { where: { id: createRefund.dataValues.id } });
        }
      } else {
        refundObj.payment_id = orderData.checkout_id;
        dbConnection.order_refund.create(refundObj).then().catch((error) => {
          console.log("orderRefund table", error);
        })
      }
    } else {
      refundObj.payment_id = orderData.checkout_id;
      refundObj.is_refund = '1';
      refundObj.refund_status = 'CN-pending';
      await dbConnection.order_refund.create(refundObj);
    }
  } catch (err) {
    console.log("refund error", err)
  }
  return
}

exports.createCNOrderRefund = async (orderName) => {
  orderRefundData = await dbConnection.order_refund.update({ is_refund: '0', refund_status: 'pending' }, { where: { order_name: orderName, is_refund: '1', refund_status: 'CN-pending' } });
}