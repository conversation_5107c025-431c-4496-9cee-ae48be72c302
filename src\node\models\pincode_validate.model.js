const Sequelize = require("sequelize");
module.exports = (sequelize) => {
    return sequelize.define("pincode_validates", {
        id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            unique: true
        },
        pincode: {
            type: Sequelize.INTEGER,
        },
        sku: {
            type: Sequelize.STRING
        },
        response_message: {
            type: Sequelize.STRING(500),
        },
    });
};
