const delhiveryController = require("./delhivery-controller");
const refundController = require("./refund-controller");
const orderSyncController = require("./ordersync-controller");
const dbConnection = require("../models");

const getStatusValue = (statusObject, statusKey) => statusObject?.[statusKey] || null;
const ZIPPEE_SHIPPING_STATUS = {
  'PICKEDUPNOTIFICATION': 'in_transit',
  'DELIVEREDNOTIFICATION': 'delivered'
};
exports.checkMiddlewareStatus = async (oldmiddlewareStatus, newmiddlewareStatus) => oldmiddlewareStatus !== newmiddlewareStatus;

exports.zippeeWebhook = async (req, res) => {
  try {
    const data = req.body;
    console.log("data==>", data);
    if(!data) return{status: false, message: "data is required."};
    else{
      res.status(200).send({ status: true, message: "Success" })
    }
    const orderName = data?.orderNo;
    console.log("orderName==>", orderName);
    if(!orderName) return{status: false, message: "orderName is required."};
    const statusType = data?.notificationType || null;
    console.log("statusType==>", statusType);
    const shipmentStatus = getStatusValue(ZIPPEE_SHIPPING_STATUS, statusType);
    let orderData = await delhiveryController.getorderSplitDataById(orderName);
    if (!orderData) {
      return { status: false, message: "orderData is not found" };
    }

    let orderObj = {
      shipment_status: shipmentStatus,
    };

    const previousMiddlewareStatus = orderData.data.middleware_status
    let fulfillmentevent = false

    //when order is picked up
    if (statusType == "PICKEDUPNOTIFICATION") {
      console.log("inside picked up notification")
      orderObj.middleware_status = "IN TRANSIT";
      orderObj.is_replaceable = "0";
      const pickupDate = new Date().toISOString();
      fulfillmentevent = true
      if (previousMiddlewareStatus != "IN TRANSIT") {
        const data = await dbConnection.delhiveryLog.update({ pickup_date: pickupDate }, { where: { order_name: orderName, is_cancel: "0", is_return: "0" } });
      }
    }

    //when order is delivered
    if (statusType == "DELIVEREDNOTIFICATION") {
      console.log("inside delivered notification")
      orderObj.middleware_status = "DELIVERED";
      orderObj.is_replaceable = "0";
      orderObj.delivered_at = new Date().toISOString();
      fulfillmentevent = true
      await dbConnection.orderItemSplit.update(orderObj, { where: { order_name: orderName } });

      let orderDataName = await orderSyncController.getorderSplitDataByName(orderName)
            orderDataName = JSON.parse(JSON.stringify(orderDataName.data))

       let cloudData = await dbConnection.childSku.findOne({ where: { child_sku: orderDataName.sku, is_cloud: "1" }});
            if (cloudData) {
              orderSyncController.callCloudApi(orderDataName);
            }
    }

    //when order is not delivered or cancelled by the customer (RTO)
    if (statusType == "NOTDELIVEREDNOTIFICATION" || statusType == "CANCELLEDNOTIFICATION") {
      console.log("inside not delivered or cancelled notification")
      let dbRes = await dbConnection.orderCancellation.create({
        order_name: orderData.data.order_name, status_type: "order_cancel_rto",
        return_order_name: orderData.data.return_order_name,
        middleware_status: 'CANCELLED RTO ORDERS',
      });
      if (dbRes.dataValues.id) {
        orderObj.middleware_status = 'RTO RETURNED TO WAREHOUSE' 
        await delhiveryController.cancelOrder(orderName, true);
        await refundController.checkingForRefund(orderData.data);
      }

    }

    await dbConnection.orderItemSplit.update(orderObj, { where: { order_name: orderName } });
    let checkMiddlewareStatus = await this.checkMiddlewareStatus(previousMiddlewareStatus, orderObj.middleware_status);

    if (fulfillmentevent && checkMiddlewareStatus) {
      let getFulfillData = await dbConnection.fulfillmentItem.findOne({ where: { order_id: orderData.data.id } });
      if (getFulfillData) {
        console.log("getFulfillData==>", getFulfillData.dataValues.fulfillment_id);
        let shopResponse = await dbConnection.shop.findOne({ where: { id: orderData.data.store_id } });
        const paramObj = {
          orderId: orderData.data.shopify_order_id,
          fulfillmentId: getFulfillData.dataValues.fulfillment_id,
          shopResponse: shopResponse,
          shipmentStatus: shipmentStatus,
        };
        console.log("paramObj==>", paramObj);
        const updateEvent = await delhiveryController.updateFulfillmentEvent(paramObj);
        console.log("updateEvent==>", updateEvent);
        return { status: true, message: "Success" };
      } else {
        return { status: false, message: "tracking number is not present" };
      }
    }
    return { status: true, message: "Success" };
  } catch (err) {
    console.log("error in zippee webhook", err);
    res.status(500).send({ status: false, message: err?.message })
  }
}