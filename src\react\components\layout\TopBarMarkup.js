import React, { Component } from 'react';
import { Tabs } from "@shopify/polaris";
import findIndex from 'lodash/findIndex';
import { withRouter } from 'react-router-dom';
import './app.scss';
const tabsList = [
    {
        id: 'order_list',
        content: 'Orders',
        accessibilityLabel: 'Orders',
        panelID: 'orders',
        route: '/app/orders'
    },
    {
        id: 'gstOrders',
        content: 'GST Orders',
        route: '/app/gst_orders'
    },
    {
        id: 'return_orders',
        content: 'Return Orders',
        route: '/app/returns'
    },
    {
        id: 'failed_orders',
        content: 'Failed Orders',
        route: '/app/failed'
    },
    {
        id: 'warranty_orders',
        content: 'Warranty Orders',
        route: '/app/warranty'
    },
    {
        id: 'manage_cfa',
        content: 'Manage CFA',
        route: '/app/manage_cfa'
    },
    {
        id: 'cfa_details',
        content: 'CFA Stock',
        route: '/app/cfaDetails'
    }, {
        id: 'settings',
        content: 'Settings',
        route: '/app/settings'
    },
    {
        id: 'logout',
        content: 'Logout',
        accessibilityLabel: 'logout',
        panelID: 'logout',
        route: '/shoplogin'
    }
];


class TopBarMarkup extends Component {
    constructor(props) {
        super(props);
        this.state = {
            selected: 0
        }
    }
    componentDidMount() {
        const selected = findIndex(tabsList, { 'route': this.props.history.location.pathname });
        if (selected >= 0) {
            this.setState({ selected: selected });
        }
    }

    componentWillReceiveProps() {
        const selected = findIndex(tabsList, { 'route': this.props.history.location.pathname });
        if (selected >= 0) {
            this.setState({ selected: selected });
        }
    }

    handleTabChange = (selectedTabIndex) => {
        this.setState({ selected: selectedTabIndex })
        if (tabsList[selectedTabIndex].route == '/shoplogin') {
            localStorage.removeItem('token')
            window.location.href = "/shoplogin";
        }
        this.props.history.push(`${tabsList[selectedTabIndex].route}`);
    }

    render() {
        return (
            <div className="topbar-shopify" style={{ background: "#f6f6f7", boxShadow: "1px 1px 5px 3px #dddd" }}>
                <Tabs tabs={tabsList} selected={this.state.selected} onSelect={this.handleTabChange} />
            </div>
        );
    }

}
export default withRouter(TopBarMarkup);