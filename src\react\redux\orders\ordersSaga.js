import { put, takeLatest } from "redux-saga/effects";
import Api from "../../apis/Api";
import { openToast } from "../toast/toastActions";
import { saveRefundFileSuccess,getAllFailedOrdersSuccess,getAllWarrantyOrdersSuccess,handleSubmitServiceError, handleSubmitServiceSuccess,submitOrderCFAError, submitOrderCFASuccess, pushOrdersSuccess, pushOrdersError, cancelOrdersSuccess, replacementOrdersError, replacementOrdersSuccess, orderReturnError, orderReturnSuccess, cancelOrdersError, createExportOrdersSuccess, syncOrderSuccess, syncOrderError, createExportOrdersError, getOrdersListSuccess, createRefundOrdersSuccess, saveOrderDetailsSuccess, saveOrderDetailsError, returnOrderRequestSuccess, returnOrderRequestError, exportOrderReturnError, exportOrderReturnSuccess, exportOrderFailedError, exportOrderFailedSuccess, saveRefundFileError, exportWarrantyOrdersSuccess, exportWarrantyOrdersError, pushForWarrantyError, pushForWarrantySuccess } from "./ordersActions";
import { orderTypes, toastObject } from "./orderTypes";

let toast = toastObject;


function* getOrdersList(data) {

    try {
        const response = yield Api.postAsync(
            Api.getAllOrders,
            data.payload
        )
        yield put(getAllFailedOrdersSuccess(response));
        data.payload.callback();
    } catch (e) {
        data.payload.callback();
    }
}
function* getFailedOrders(data) {
    try {
        const response = yield Api.postAsync(
            Api.getFailedAllOrders,
            data.payload
        )
        yield put(getAllFailedOrdersSuccess(response));
        data.payload.callback();
    } catch (e) {
        console.log("err--",e)
        data.payload.callback();
    }
}
function* getWarrantyOrders(data) {
    try {
        const response = yield Api.postAsync(
            Api.getWarrantyAllOrders,
            data.payload
        )
        yield put(getAllWarrantyOrdersSuccess(response));
        data.payload.callback();
    } catch (e) {
        console.log("err--",e)
        data.payload.callback();
    }
}
function* getReturnOrdersList(data) {

    try {
        const response = yield Api.postAsync(
            Api.getAllReturnOrders,
            data.payload
        )
        yield put(getOrdersListSuccess(response));
        data.payload.callback();
    } catch (e) {
        data.payload.callback();
    }
}

function* submitCFAOrdersManually(data) {
    let message = "Oppss...There is something wrong.";
    let isError = false;
    try {
        isError = false;
        message = "Order assigned to CFA";
        const response = yield Api.postAsync(
            Api.submitCFAOrders,
            data.payload
        )
        yield put(submitOrderCFASuccess(response));
    } catch (e) {
        isError = true;
        if (typeof e.response.data.message !== 'undefined' && e.response.data.message !== null) {
            message = e.response.data.message;
        }
        yield put(submitOrderCFAError(e.response));
    } finally {
        toast = { ...toast, message: message, isActive: true, isError: isError }
        yield put(openToast(toast));
        data.payload.callback();
    }
}

function* submitServiceRequest(data) {
    console.log("data",data)
    let message = "Oppss...There is something wrong.";
    let isError = false;
    try {
        isError = false;
        message = "Request regiestered successfully";
        const response = yield Api.postAsync(
            Api.submitServiceRequest,
            data.payload
        )
        yield put(handleSubmitServiceSuccess(response));
    } catch (e) {
        isError = true;
        if (typeof e.response.data.message !== 'undefined' && e.response.data.message !== null) {
            message = e.response.data.message;
        }
        yield put(handleSubmitServiceError(e.response));
    } finally {
        toast = { ...toast, message: message, isActive: true, isError: isError }
        yield put(openToast(toast));
        data.payload.callback();
    }
}


function* pushOrders(data) {
    let message = "Oppss...There is something wrong.";
    let isError = false;
    try {
        isError = false;
        message = "Order push process is running in background";
        const response = yield Api.postAsync(
            Api.pushOrders,
            data.payload.objdata
        )
        yield put(pushOrdersSuccess(response));
    } catch (e) {
        isError = true;
        message = "Oppss...There is something wrong.";
        yield put(pushOrdersError(e.response));
    } finally {
        toast = { ...toast, message: message, isActive: true, isError: isError }
        yield put(openToast(toast));
        data.payload.callback();
    }
}
function* pushForWarranty(data) {
    let message = "Oppss...There is something wrong.";
    let isError = false;
    try {
        isError = false;
        message = "Order push process is running in background";
        const response = yield Api.postAsync(
            Api.pushForWarranty,
            data.payload.objdata
        )
        yield put(pushForWarrantySuccess(response));
    } catch (e) {
        isError = true;
        message = "Oppss...There is something wrong.";
        yield put(pushForWarrantyError(e.response));
    } finally {
        toast = { ...toast, message: message, isActive: true, isError: isError }
        yield put(openToast(toast));
        data.payload.callback();
    }
}

function* cancelOrders(data) {
    let message = "Oppss...There is something wrong.";
    let isError = false;
    try {
        isError = false;
        message = "Order cancel process is running in background";
        const response = yield Api.postAsync(
            Api.cancelOrders,
            data.payload.objdata
        )
        yield put(cancelOrdersSuccess(response));
    } catch (e) {
        isError = true;
        message = "Oppss...There is something wrong.";
        yield put(cancelOrdersError(e.response));
    } finally {
        toast = { ...toast, message: message, isActive: true, isError: isError }
        yield put(openToast(toast));
        data.payload.callback();
    }
}

function* returnOrderRequest(data) {
    let message = "Oppss...There is something wrong.";
    let isError = false;
    try {
        isError = false;
        message = "Return order request running in background";
        const response = yield Api.postAsync(
            Api.returnOrders,
            data.payload.objData
        )
        yield put(returnOrderRequestSuccess(response));
    } catch (e) {
        isError = true;
        message = "Oppss...There is something wrong.";
        yield put(returnOrderRequestError(e.response));
    } finally {
        toast = { ...toast, message: message, isActive: true, isError: isError }
        yield put(openToast(toast));
        data.payload.callback();
    }
}

function* exportWarrantyOrders(data) {
    let message = "Oppss...There is something wrong.";
    let isError = false;
    try {
        isError = false;
        message = "Export order request running in background";
        const response = yield Api.postAsync(
            Api.exportWarrantyOrders,
            data.payload.objdata
        )
        yield put(exportWarrantyOrdersSuccess(response));
    } catch (e) {
        isError = true;
        message = "Oppss...There is something wrong.";
        yield put(exportWarrantyOrdersError(e.response));
    } finally {
        toast = { ...toast, message: message, isActive: true, isError: isError }
        yield put(openToast(toast));
        data.payload.callback();
    }
}

function* exportReturnOrders(data) {
    let message = "Oppss...There is something wrong.";
    let isError = false;
    try {
        isError = false;
        message = "Export order request running in background";
        const response = yield Api.postAsync(
            Api.exportReturnOrders,
            data.payload.objdata
        )
        yield put(exportOrderReturnSuccess(response));
    } catch (e) {
        isError = true;
        message = "Oppss...There is something wrong.";
        yield put(exportOrderReturnError(e.response));
    } finally {
        toast = { ...toast, message: message, isActive: true, isError: isError }
        yield put(openToast(toast));
        data.payload.callback();
    }
}

function* exportFailedOrders(data) {
    let message = "Oppss...There is something wrong.";
    let isError = false;
    try {
        isError = false;
        message = "Export order request running in background";
        const response = yield Api.postAsync(
            Api.exportFailedOrders,
            data.payload.objdata
        )
        yield put(exportOrderFailedSuccess(response));
    } catch (e) {
        isError = true;
        message = "Oppss...There is something wrong.";
        yield put(exportOrderFailedError(e.response));
    } finally {
        toast = { ...toast, message: message, isActive: true, isError: isError }
        yield put(openToast(toast));
        data.payload.callback();
    }
}

function* replacementOrders(data) {
    let message = "Oppss...There is something wrong.";
    let isError = false;
    try {
        isError = false;
        message = "Order replacement process is running in background";
        const response = yield Api.postAsync(
            Api.replacementOrders,
            data.payload.objdata
        )
        yield put(replacementOrdersSuccess(response));
    } catch (e) {
        isError = true;
        message = "Oppss...There is something wrong.";
        yield put(replacementOrdersError(e.response));
    } finally {
        toast = { ...toast, message: message, isActive: true, isError: isError }
        yield put(openToast(toast));
        data.payload.callback();
    }
}

function* orderReturnRequest(data) {
    let message = "Oppss...There is something wrong.";
    let isError = false;
    try {
        isError = false;
        message = "Order replacement process is running in background";
        const response = yield Api.postAsync(
            Api.OrderReturnRequest,
            data.payload.objdata
        )
        yield put(orderReturnSuccess(response));
    } catch (e) {
        isError = true;
        message = "Oppss...There is something wrong.";
        yield put(orderReturnError(e.response));
    } finally {
        toast = { ...toast, message: message, isActive: true, isError: isError }
        yield put(openToast(toast));
        data.payload.callback();
    }
}

function* syncOrder(data) {
    let message = "Oppss...There is something wrong.";
    let isError = false;
    try {
        isError = false;
        message = "Syncing process is running in background"
        const response = yield Api.postAsync(
            Api.getSyncOrdersData,
            data.payload
        )
        yield put(syncOrderSuccess(response));
        data.payload.callback();
    } catch (e) {
        isError = true;
        if (typeof e.response.data.message !== 'undefined' && e.response.data.message !== null) {
            message = e.response.data.message;
        }
        yield put(syncOrderError(e.response));
    } finally {
        toast = { ...toast, message: message, isActive: true, isError: isError }
        yield put(openToast(toast));
        data.payload.callback();
    }
}
function* exportOrders(data) {
    let message = "Oppss...There is something wrong.";
    let isError = false;
    try {
        isError = false;
        message = "Exported data send to your mail";
        const response = yield Api.postAsync(
            Api.exportOrders,
            data.payload.objdata
        )
        yield put(createExportOrdersSuccess(response));
    } catch (e) {
        isError = true;
        message = "Oppss...There is something wrong.";
        yield put(createExportOrdersError(e.response));
    } finally {
        toast = { ...toast, message: message, isActive: true, isError: isError }
        yield put(openToast(toast));
        data.payload.callback();
    }
}

function* refundOrders(data) {
    let message = "", isError = false;
    try {
        isError = false;
        message = "Refund process started";
        const response = yield Api.postAsync(
            Api.ordersRefund,
            data.payload.objdata
        )
        yield put(createRefundOrdersSuccess(response));
    } catch (e) {
        isError = true;
        message = "Oppss...There is something wrong.";
        yield put(createRefundOrdersError(e.response));
    } finally {
        toast = { ...toast, message: message, isActive: true, isError: isError }
        yield put(openToast(toast));
        data.payload.callback();
    }
}

function* saveOrderDetails(data) {
    let message = "";
    let isError = false;
    try {
        isError = false;
        message = "Order details updated successfully";
        const response = yield Api.postAsync(
            Api.saveOrderDetails,
            data.payload.objdata
        )
        yield put(saveOrderDetailsSuccess(response));
    } catch (e) {
        isError = true;
        message = "Oppss...There is something wrong.";
        yield put(saveOrderDetailsError(e.response));
    } finally {
        toast = { ...toast, message: message, isActive: true, isError: isError }
        yield put(openToast(toast));
        data.payload.callback();
    }
}

function* saveRefundList(data) {
    let message = "Oppss...There is something wrong.";
    let isError = false;
    try {
        isError = false;   
        message = "Data Saved";
        const response = yield Api.postAsync(
            Api.saveRefundFile,
            data.payload.formData
        )
        yield put(saveRefundFileSuccess(response));
    } catch (e) {
        isError = true;   
        if (typeof e.response.data.message !== 'undefined' && e.response.data.message !== null) {
            message = e.response.data.message;
        }
        yield put(saveRefundFileError(e.response));
    } finally {
        toast = { ...toast, message: message, isActive: true, isError: isError }
        yield put(openToast(toast));   
        data.payload.callback();
    }
}


export default function* ordersSaga() {
    yield takeLatest(orderTypes.GET_ORDERS_LIST_REQUEST, getOrdersList);
    yield takeLatest(orderTypes.GET_FAILED_ORDERS_REQUEST, getFailedOrders);
    yield takeLatest(orderTypes.GET_WARRANTY_ORDERS_REQUEST, getWarrantyOrders);
    yield takeLatest(orderTypes.GET_RETURN_ORDERS_LIST_REQUEST, getReturnOrdersList);
    yield takeLatest(orderTypes.PUSH_ORDERS_REQUEST, pushOrders);
    yield takeLatest(orderTypes.CANCEL_ORDERS_REQUEST, cancelOrders);
    yield takeLatest(orderTypes.REPLACEMENT_ORDERS_REQUEST, replacementOrders);
    yield takeLatest(orderTypes.ORDER_RETURN_REQUEST, orderReturnRequest);
    yield takeLatest(orderTypes.EXPORT_ORDERS_REQUEST, exportOrders);
    yield takeLatest(orderTypes.GET_ORDERS_SYNC_REQUEST, syncOrder);
    yield takeLatest(orderTypes.SUBMIT_ORDER_CFA_REQUEST, submitCFAOrdersManually);
    yield takeLatest(orderTypes.REFUND_ORDERS_REQUEST, refundOrders);
    yield takeLatest(orderTypes.SAVE_ORDER_DETAILS_REQUEST, saveOrderDetails);
    yield takeLatest(orderTypes.RETURN_ORDER_REQUEST, returnOrderRequest);
    yield takeLatest(orderTypes.EXPORT_ORDER_RETURN_REQUEST, exportReturnOrders);
    yield takeLatest(orderTypes.EXPORT_FAILED_ORDERS_REQUEST, exportReturnOrders);
    yield takeLatest(orderTypes.SUBMIT_SERVICE_REQUEST,submitServiceRequest );
    yield takeLatest(orderTypes.SEND_REFUND_FILE_REQUEST,saveRefundList);
    yield takeLatest(orderTypes.EXPORT_WARRANTY_ORDERS_REQUEST,exportWarrantyOrders);
    yield takeLatest(orderTypes.PUSH_FOR_WARRANTY_REQUEST,pushForWarranty);
}
