let fileName = process.env.NODE_ENV;
const CONFIG = require('../config/config_' + fileName + '.json');
//Axios Call
const axios = require('axios');
const response = require('../helper/appResponse');
const Sequelize = require("sequelize");
const dbConnection = require("../models");
const sequelize = require('sequelize');
const randomString = require('randomstring');
const delhiveryController = require('./delhivery-controller');
const orderHistoryHelper = require('../helper/orderHistory')
const modelOperators = Sequelize.Op;
const emailController = require('./email-controller');
// register warranty data 
exports.warrantyRegistration = async (req, res) => {
    let successMessage = response.responseMessage(CONFIG.status.FAILED, CONFIG.msg.FAILED)
    let status = 500
    try {
        const warrantyReq = await this.getWarrantydata(req.body, CONFIG.crm.warrantyToken, CONFIG.crm.warrantyUserId, 0)
        const options = {
            headers: {
                'Content-Type': 'application/json'
            }
        }
        const shop = req.query.shop
        const shopResponse = await dbConnection.shop.findOne({ where: { myshopify_domain: shop } })
        if (shopResponse) {
            if (req.body.secret_token) {
                const validate = await dbConnection.otpVarification.findOne({ where: { store_id: shopResponse.id, mobile: req.body.mobileNumber } })
                if (validate) {
                    const warrantyLogReq = await this.getWarrantydata(req.body, CONFIG.crm.token, CONFIG.crm.userId, 1)
                    console.log("Warranty Req Data==>", JSON.stringify(warrantyLogReq.data))
                    if (validate.dataValues.secret_token == req.body.secret_token && validate.dataValues.otp == req.body.otp) {
                        await axios.post(CONFIG.crm.warranty_url, warrantyLogReq.data, options)
                            .then(createRes => {
                                if (createRes.data.wSState != 0) {
                                    successMessage.status = CONFIG.status.SUCCESS
                                    successMessage.message = CONFIG.msg.SUCCESS
                                    successMessage.data = createRes.data
                                    status = 200
                                } else {
                                    console.log("createRes==>", createRes)
                                }
                            }).catch(err => console.log("errror++>", err))
                    } else {
                        successMessage.status = CONFIG.status.ERROR
                        successMessage.message = "OTP is not Valid"
                    }
                } else {
                    console.log("mobile Number is not valid")
                }
            } else {
                console.log("Warranty original Req Data==>", JSON.stringify(warrantyReq.data))
                await axios.post(CONFIG.crm.warranty_url, warrantyReq.data, options)
                    .then(createRes => {
                        if (createRes.data.wSState != 0) {
                            successMessage.status = CONFIG.status.SUCCESS
                            successMessage.message = CONFIG.msg.SUCCESS
                            successMessage.data = createRes.data
                            status = 200
                        } else {
                            console.log("createRes==>", createRes)
                        }
                    }).catch(err => console.log("errror++>", err))
            }
        }
    } catch (err) {
        console.log("Error==>", err)
        successMessage.status = CONFIG.status.ERROR
        successMessage.message = CONFIG.msg.ERROR
    }
    res.status(status).send(successMessage)
}

// log a complaint
exports.logComplaint = async (req, res) => {
    let successMessage = response.responseMessage(CONFIG.status.FAILED, CONFIG.msg.FAILED)
    let status = 500
    try {
        const logReq = await this.getLogData(req.body)
        const shop = req.query.shop
        const shopResponse = await dbConnection.shop.findOne({ where: { myshopify_domain: shop } })
        if (shopResponse) {
            const options = {
                headers: {
                    'Content-Type': 'application/json'
                }
            }
            if (logReq.status) {
                console.log("dt====>", JSON.stringify(logReq.data))
                const logDataRes = await axios.post(CONFIG.crm.log_url, logReq.data, options)
                if (logDataRes.data.wSState != 0) {
                    const customerCode = logDataRes.data.userProfile.customerCode

                    const optData = await this.sendOtp(req.body.mobileNumber, shop)
                    successMessage.status = CONFIG.status.SUCCESS
                    successMessage.message = CONFIG.msg.SUCCESS
                    successMessage.customerCode = customerCode
                    successMessage.isRegister = true
                    successMessage.secret_token = optData.secret_token
                    status = 200
                } else {
                    successMessage.status = CONFIG.status.FAILED
                    successMessage.message = CONFIG.msg.FAILED
                    successMessage.isRegister = false
                }
            }
        } else {
            successMessage.status = CONFIG.status.FAILED
            successMessage.message = "Pincode is not valid"
            successMessage.isRegister = false
        }
    } catch (err) {
        // console.log("errror--", err)
        successMessage.status = CONFIG.status.ERROR
        successMessage.message = CONFIG.msg.ERROR
    }
    res.status(status).send(successMessage)
}

// track a log complaint

exports.trackComplaint = async (req, res) => {
    let successMessage = response.responseMessage(CONFIG.status.FAILED, CONFIG.msg.FAILED)
    let status = 500
    try {
        const shop = req.query.shop
        const shopResponse = await dbConnection.shop.findOne({ where: { myshopify_domain: shop } })
        const options = {
            headers: {
                'Content-Type': 'application/json'
            }
        }

        const data = {
            "userId": CONFIG.crm.warrantyUserId,
            "token": CONFIG.crm.warrantyToken,
            "customerMobileNumber": req.body.mobileNumber,
            "version": CONFIG.crm.version
        }
        if (shopResponse) {
            await axios.post(CONFIG.crm.track_url, data, options)
                .then(createRes => {
                    successMessage.status = CONFIG.status.SUCCESS
                    successMessage.message = CONFIG.msg.SUCCESS
                    successMessage.data = createRes.data
                    status = 200
                }).catch(err => console.log("errror++>", err))
        }
    } catch (err) {
        console.log("error==>", err)
        successMessage.status = CONFIG.status.ERROR
        successMessage.message = CONFIG.msg.ERROR
    }
    res.status(status).send(successMessage)
}
exports.getCallList = async (callId) => {
    try {
        const reqPayload = {
            userId: CONFIG.crm.userId,
            token: CONFIG.crm.token,
            version: CONFIG.crm.version,
            callId: callId?.replace(/,$/, "")
        }

        const headers = {
            headers: {
                'Content-Type': 'application/json',
            }
        }
        const response = await axios.post(CONFIG.crm.getCallList, reqPayload, headers);
        if (response.status === 200 && response?.data && response?.data?.callList?.length > 0) {
            return { success: true, data: response?.data.callList[0] };
        }
        return { success: false, data: response?.data.callList[0] };
    } catch (error) {
        console.error('Error getCallList call list:', error);
        throw error;
    }
}
// getCallList('GU05022500070')

exports.getLogData = (body) => {
    return new Promise(async (resolve, reject) => {
        if (body) {
            let tableData
            if (body.stateCode && body.cityCode) {
                tableData = await dbConnection.cityStatePin.findOne({ where: { city_code: body.cityCode, state_code: body.stateCode } })
            }
            const data = {
                "serviceType": "submitRegistrationDetails",
                "submitRegistrationDetails": {
                    "companyCode": CONFIG.crm.companyCode,
                    "name": body.customerName ? body.customerName : "",
                    "mobileNumber": getLastTenDigits(body.mobileNumber),
                    "loginType": CONFIG.crm.loginType,
                    "socialMediaId": "",
                    "languageId": "English",
                    "address": body.address ? body.address : "",
                    "state": body.stateCode ? body.stateCode : "",
                    "city": body.cityCode ? body.cityCode : "",
                    "area": tableData ? tableData.area : "",
                    "pin": body.pinCode ? body.pinCode : "",
                    "profilePhoto": "",
                    "fileSize": "",
                    "fileType": "",
                    "alternateMobileNo": "",
                    "emailAddress": body.email ? body.email : "",
                    "landmark": body.landmark ? body.landmark : "",
                    "preferredLanguage": "",
                    "customerType": CONFIG.crm.customerType,
                    "customerPriorityType": ""
                }
            }
            resolve({ status: true, data: data })
        } else {
            resolve({ status: false })
        }
    })
}

function getLastTenDigits(str) {
    str = str.toString()
    // Remove all non-digit characters from the string
    const digitsOnly = str.replace(/\D/g, '');

    // If the string contains more than 10 digits, extract the last 10 digits
    if (digitsOnly.length > 10) {
        return digitsOnly.substring(digitsOnly.length - 10);
    } else {
        // If the string contains 10 or fewer digits, return the whole string
        return digitsOnly;
    }
}

exports.getWarrantydata = (body, token, userId, serviceTypeCode) => {
    return new Promise(async (resolve, reject) => {
        if (body) {
            let tableData
            if (body.stateCode && body.cityCode) {
                tableData = await dbConnection.cityStatePin.findOne({ where: { city_code: body.cityCode, state_code: body.stateCode } })
            }
            const data = {
                "serviceType": "submitCallRegistration",
                "userId": userId,
                "token": token,
                "submitCallRegistration": {
                    "distributorCode": CONFIG.crm.distributorCode,
                    "dealerCode": CONFIG.crm.dealerCode,
                    "customerType": CONFIG.crm.customerType,
                    "mobileNumber": body.customerCode ? body.customerCode : getLastTenDigits(body.mobileNumber),
                    "customerName": body.customerName ? body.customerName : "",
                    "address": body.address ? body.address : " ",
                    "area": tableData ? tableData.area : " ",
                    "cityCode": body.cityCode ? body.cityCode : " ",
                    "stateCode": body.stateCode ? body.stateCode : " ",
                    "pinCode": body.pinCode ? body.pinCode : " ",
                    "brandCode": CONFIG.crm.brandCode,
                    "productCategoryCode": checkSKU(body?.model) ? 3 : CONFIG.crm.productCategoryCode,
                    "productCode": checkSKU(body?.model) ? 7 : CONFIG.crm.productCode,
                    "model": body.model ? body.model : "",
                    "productSerialNumber": body.productSerialNumber ? body.productSerialNumber : "",
                    "dateOfPurchase": body.dateOfPurchase ? body.dateOfPurchase : "",
                    "quantity": 1,
                    "serviceTypeCode": checkSKU(body?.model) ? 3 : serviceTypeCode,
                    "customerRemarks": body.customerRemarks ? body.customerRemarks : " ",
                    "location": " ",
                    "email": body.email ? body.email : " ",
                    "techRemarks": " ",
                    "dealerDesc": " ",
                    "cityDesc": CONFIG.crm.cityDesc
                },
                "version": CONFIG.crm.version
            }
            resolve({ status: true, data: data })
        } else {
            resolve({ status: false })
        }

    })
}

exports.getLogServicedata = (body, token, userId, serviceTypeCode) => {
    return new Promise(async (resolve, reject) => {
        if (body) {
            let tableData
            if (body.stateCode && body.cityCode) {
                tableData = await dbConnection.cityStatePin.findOne({ where: { city_code: body.cityCode, state_code: body.stateCode } })
            }
            const data = {
                "serviceType": "submitCallRegistration",
                "userId": userId,
                "token": token,
                "submitCallRegistration": {
                    "distributorCode": CONFIG.crm.distributorCode,
                    "dealerCode": CONFIG.crm.dealerCode,
                    "customerType": CONFIG.crm.customerType,
                    "mobileNumber": body.customerCode ? body.customerCode : body.mobileNumber,
                    "customerName": body.customerName ? body.customerName : "",
                    "address": body.address ? body.address : " ",
                    "area": tableData ? tableData.area : " ",
                    "cityCode": body.cityCode ? body.cityCode : " ",
                    "stateCode": body.stateCode ? body.stateCode : " ",
                    "pinCode": body.pinCode ? body.pinCode : " ",
                    "brandCode": CONFIG.crm.brandCode,
                    "productCategoryCode": CONFIG.crm.productCategoryCode,
                    "productCode": CONFIG.crm.productCode,
                    "model": body.model ? body.model : "",
                    "productSerialNumber": body.productSerialNumber ? body.productSerialNumber : "",
                    "dateOfPurchase": "",
                    // "quantity": 1,
                    "serviceTypeCode": serviceTypeCode,
                    "techRemarks": body.customerRemarks ? body.customerRemarks : " ",
                },
                "version": CONFIG.crm.version
            }
            resolve({ status: true, data: data })
        } else {
            resolve({ status: false })
        }

    })
}

exports.getCityAndStateCode = async (req, res) => {
    let successMessage = response.responseMessage(CONFIG.status.FAILED, CONFIG.msg.FAILED)
    let status = 500
    try {
        const pin = req.query.pincode
        const shop = req.query.shop
        const shopResponse = await dbConnection.shop.findOne({ where: { myshopify_domain: shop } })
        if (shopResponse) {
            const cityStateData = await dbConnection.cfaPincodeMapping.findOne({ where: { pincode: pin } })
            if (cityStateData) {
                const state = cityStateData.dataValues.state
                const city = cityStateData.dataValues.city
                const cityStatePin = await dbConnection.cityStatePin.findOne({ where: { state: state, city: city } })
                if (cityStatePin) {
                    const obj = {
                        city: cityStatePin.dataValues.city,
                        city_code: cityStatePin.dataValues.city_code,
                        state: cityStatePin.dataValues.state,
                        state_code: cityStatePin.dataValues.state_code,
                    }
                    successMessage.status = CONFIG.status.SUCCESS
                    successMessage.message = CONFIG.msg.SUCCESS
                    successMessage.data = obj
                    status = 200
                    res.status(status).send(successMessage)
                } else {
                    successMessage.status = CONFIG.status.SUCCESS
                    successMessage.message = "Not Found"
                    status = 200
                    res.status(status).send(successMessage)
                }
            } else {
                successMessage.status = CONFIG.status.SUCCESS
                successMessage.message = "Pincode Not Found"
                status = 200
                res.status(status).send(successMessage)
            }
        }
    } catch (err) {
        console.log("pincode==>", err)
        successMessage.status = CONFIG.status.ERROR
        successMessage.message = CONFIG.msg.ERROR
        res.status(status).send(successMessage)
    }
}

exports.sendOtp = async (mobile, shop) => {
    let successMessage = response.responseMessage(CONFIG.status.FAILED, CONFIG.msg.FAILED)
    let status = 500
    try {
        const shopResponse = await dbConnection.shop.findOne({ where: { myshopify_domain: shop } })
        if (shopResponse) {
            const secret_token = randomString.generate({ length: 12, charset: 'alphanumeric' });
            const otp = randomString.generate({ length: 4, charset: 'numeric' });
            const phoneRes = await dbConnection.otpVarification.findOne({ where: { store_id: shopResponse.id, mobile: mobile } })
            if (phoneRes) {
                await dbConnection.otpVarification.update({ secret_token: secret_token, otp: otp }, { where: { store_id: shopResponse.id, mobile: mobile } })
                await sendSMS(mobile, otp).then(otpRes => {
                    if (otpRes.status) {
                        successMessage.status = CONFIG.status.SUCCESS
                        successMessage.message = CONFIG.status.SUCCESS
                        successMessage.secret_token = secret_token
                        status = 200
                    } else {
                        throw new Error()
                    }
                })
            } else {
                const data = {
                    store_id: shopResponse.id,
                    mobile: mobile,
                    secret_token: secret_token,
                    otp: otp
                }
                await dbConnection.otpVarification.create(data)
                await sendSMS(mobile, otp).then(otpRes => {
                    if (otpRes.status) {
                        successMessage.status = CONFIG.status.SUCCESS
                        successMessage.message = CONFIG.status.SUCCESS
                        successMessage.secret_token = secret_token
                        status = 200
                    } else {
                        throw new Error()
                    }
                })
            }
        } else {
            successMessage.status = CONFIG.status.ERROR
            successMessage.message = CONFIG.status.ERROR
        }
    } catch (err) {
        successMessage.status = CONFIG.status.ERROR
        successMessage.message = CONFIG.msg.ERROR
    }
    return successMessage
}

exports.validateOtp = async (req, res) => {
    let successMessage = response.responseMessage(CONFIG.status.FAILED, CONFIG.msg.FAILED)
    let status = 500
    try {
        const mobile = req.body.mobile
        const secret_token = req.body.secret_token
        const otp = req.body.otp
        const shop = req.query.shop
        const shopResponse = await dbConnection.shop.findOne({ where: { myshopify_domain: shop } })
        if (shopResponse) {
            const phoneRes = await dbConnection.otpVarification.findOne({ where: { store_id: shopResponse.id, mobile: mobile } })
            if (phoneRes) {
                if (phoneRes.dataValues.secret_token == secret_token) {
                    if (phoneRes.dataValues.otp == otp) {
                        successMessage.status = CONFIG.status.SUCCESS
                        successMessage.message = CONFIG.msg.SUCCESS
                        status = 200
                    } else {
                        successMessage.status = CONFIG.status.ERROR
                        successMessage.message = "Invalid OTP"
                    }
                } else {
                    successMessage.status = CONFIG.status.ERROR
                    successMessage.message = "Invalid Secret Key"
                }
            } else {
                successMessage.status = CONFIG.status.ERROR
                successMessage.message = "Invalid Mobile Number"
            }
        } else {
            successMessage.status = CONFIG.status.ERROR
            successMessage.message = CONFIG.msg.ERROR
        }
    } catch (err) {
        console.log("error==>", err)
        successMessage.status = CONFIG.status.ERROR
        successMessage.message = CONFIG.msg.ERROR
    }
    return successMessage
}

let sendSMS = (mobile, otp) => {
    return new Promise(async (resolve, reject) => {
        const url = CONFIG.otp.url + `?Userid=${CONFIG.otp.user_id}&UserPassword=${CONFIG.otp.password}&PhoneNumber=${mobile}&Text=Your Syphony Cool Care Code is : ${otp} ${CONFIG.otp.otp_secret}&GSM=${CONFIG.otp.gsm}&Entityid=${CONFIG.otp.entity_id}&Templateid=${CONFIG.otp.template_id}`
        const otpRes = await axios.post(url)
        resolve({ status: true })
    })
}
exports.autoWarrantyRegistration = async (req, res) => {
    try {
        let orderNames = req.body.names
        for (let orderName of orderNames) {
            console.log(orderName)
            await this.crmWarrantyRegistration(orderName)
        }
        res.status(200).send({ status: "suceess" })
    } catch (error) {
        console.log("Errrrrr", error);
    }
}

exports.crmWarrantyRegistration = (orderName, requestData = null) => {
    return new Promise(async (resolve, reject) => {
        const options = {
            headers: { 'Content-Type': 'application/json' }
        }
        let orderData = await delhiveryController.getorderSplitDataById(orderName);
        if (orderData.status) {
            let body = {};
            body.model = orderData.data.sku != null && orderData.data.sku != "null" ? orderData.data.sku : "";
            body.dateOfPurchase = orderData.data.order_created_at != null && orderData.data.order_created_at != "null" ? orderData.data.order_created_at : "";
            body.productSerialNumber = orderData.data.product_serial_number != null && orderData.data.product_serial_number != "null" ? orderData.data.product_serial_number : "";
            body.email = orderData.data.orderCustomers[0].customer_email != null && orderData.data.orderCustomers[0].customer_email != "null" ? orderData.data.orderCustomers[0].customer_email : "";
            body.customerName = orderData.data.orderAddresses[0].name != null && orderData.data.orderAddresses[0].name != "null" ? orderData.data.orderAddresses[0].name : "";
            body.mobileNumber = orderData.data.orderAddresses[0].phone != null && orderData.data.orderAddresses[0].phone != "null" ? orderData.data.orderAddresses[0].phone.trim() : "";
            body.address = orderData.data.orderAddresses[0].address1 != null && orderData.data.orderAddresses[0].address1 != "null" ? orderData.data.orderAddresses[0].address1 : "";
            body.pinCode = orderData.data.orderAddresses[0].zip_code != null && orderData.data.orderAddresses[0].zip_code != "null" ? orderData.data.orderAddresses[0].zip_code : "";
            if (requestData) body = requestData
            if (body.pinCode != null) {
                let codeData = await this.findCityAndStateCodeFromCRMTable(body.pinCode)
                if (codeData.status) {
                    body.cityCode = codeData.obj.city_code != null && codeData.obj.city_code != "null" ? codeData.obj.city_code : '';
                    body.stateCode = codeData.obj.state_code != null && codeData.obj.state_code != "null" ? codeData.obj.state_code : '';
                }
            }
            let logReq = await this.getLogData(body);
            if (logReq.status) {
                let logDataRes = {}
                let token = CONFIG.crm.token
                let userId = CONFIG.crm.userId
                if (requestData) {
                    logDataRes = await axios.post(CONFIG.crm.log_url, logReq.data, options);
                } else {
                    token = CONFIG.crm.new_warrantyToken
                    userId = CONFIG.crm.new_warrantyUserId
                }
                let warrantyLogReq;
                let warrantyCode;
                if (requestData && logDataRes?.data?.wSState != 0) {
                    let customerCode = logDataRes?.data?.userProfile?.customerCode;
                    warrantyCode = customerCode
                    body.customerCode = customerCode;
                    warrantyLogReq = await this.getWarrantydata(body, token, userId, 1);
                } else {
                    warrantyCode = getLastTenDigits(body.mobileNumber)
                    warrantyLogReq = await this.getWarrantydata(body, token, userId, 0);
                }
                if (warrantyLogReq.status) {


                    let warrantyResponse = await axios.post(CONFIG.crm.warranty_url, warrantyLogReq.data, options)

                    let helperObj = {
                        shopify_order_id: orderData.data.shopify_order_id,
                        shopify_order_name: orderData.data.shopify_order_name,
                        order_name: orderData.data.order_name,
                        platform_type: requestData ? "LOG-A-Complain" : "CRM-WarrantyRegistration",
                        request_data: JSON.stringify(warrantyLogReq.data),
                        response_status_code: warrantyResponse.status,
                        response_data: warrantyResponse.data ? JSON.stringify(warrantyResponse.data) : 'warranty error'
                    }
                    await orderHistoryHelper.insertOrderHistory(helperObj)
                    if (warrantyResponse) {
                        if (requestData) {
                            let warrantyData = warrantyResponse.data
                            serviceReqObj = {
                                order_name: orderData.data.order_name,
                                request_id: Math.floor(Math.random() * 100) + 1,
                                customer_code: warrantyCode,
                                response_id: warrantyData.responseId || null,
                                call_id: warrantyData.callId || null,
                                message_description: warrantyData.messageDescription
                            }
                            await dbConnection.serviceRequestLog.create(serviceReqObj)
                        } else {
                            warrantyCode = warrantyResponse.data?.responseId
                            await dbConnection.orderItemSplit.update({ warranty_code: warrantyCode }, { where: { order_name: orderData.data.order_name } })

                        }
                        resolve({ status: true, data: warrantyResponse.data });
                    } else {
                        resolve({ status: false });
                    }
                } else {
                    resolve({ status: false });
                }

            } else {
                resolve({ status: false });
            }
        } else {
            resolve({ status: false });
        }
    })
}

function checkSKU(sku) {
    const skus = ["AGYST007", "AGYST008", "AGYST009", "AGYST004", "AGYST005", "AGYST006", "AGYST001", "AGYST002", "AGYST003"];
    return skus.includes(sku);
}

exports.logServiceRequest = (orderName, requestData = null) => {
    return new Promise(async (resolve, reject) => {
        const options = {
            headers: { 'Content-Type': 'application/json' }
        }
        let orderData = await delhiveryController.getorderSplitDataById(orderName);
        if (orderData.status) {
            let body = {};
            body.model = orderData.data.sku != null && orderData.data.sku != "null" ? orderData.data.sku : "";
            body.dateOfPurchase = orderData.data.order_created_at != null && orderData.data.order_created_at != "null" ? orderData.data.order_created_at : "";
            body.productSerialNumber = orderData.data.product_serial_number != null && orderData.data.product_serial_number != "null" ? orderData.data.product_serial_number : "";
            body.email = orderData.data.orderCustomers[0].customer_email != null && orderData.data.orderCustomers[0].customer_email != "null" ? orderData.data.orderCustomers[0].customer_email : "";
            body.customerName = orderData.data.orderAddresses[0].name != null && orderData.data.orderAddresses[0].name != "null" ? orderData.data.orderAddresses[0].name : "";
            body.mobileNumber = orderData.data.orderAddresses[0].phone != null && orderData.data.orderAddresses[0].phone != "null" ? orderData.data.orderAddresses[0].phone.trim() : "";
            body.address = orderData.data.orderAddresses[0].address1 != null && orderData.data.orderAddresses[0].address1 != "null" ? orderData.data.orderAddresses[0].address1 : "";
            body.pinCode = orderData.data.orderAddresses[0].zip_code != null && orderData.data.orderAddresses[0].zip_code != "null" ? orderData.data.orderAddresses[0].zip_code : "";
            if (requestData) body = requestData
            if (body.pinCode != null) {
                let codeData = await this.findCityAndStateCodeFromCRMTable(body.pinCode)
                if (codeData.status) {
                    body.cityCode = codeData.obj.city_code != null && codeData.obj.city_code != "null" ? codeData.obj.city_code : '';
                    body.stateCode = codeData.obj.state_code != null && codeData.obj.state_code != "null" ? codeData.obj.state_code : '';
                }
            }
            let logReq = await this.getLogData(body);
            if (logReq.status) {
                let logDataRes = await axios.post(CONFIG.crm.log_url, logReq.data, options);

                if (logDataRes) {
                    let warrantyLogReq;
                    let warrantyCode;
                    if (logDataRes.data.wSState != 0) {
                        let customerCode = logDataRes.data.userProfile.customerCode;
                        warrantyCode = customerCode
                        body.customerCode = customerCode;
                        warrantyLogReq = await this.getLogServicedata(body, CONFIG.crm.token, CONFIG.crm.userLogId, 1);
                    } else {
                        warrantyCode = body.mobileNumber
                        warrantyLogReq = await this.getLogServicedata(body, CONFIG.crm.token, CONFIG.crm.userLogId, 0);
                    }
                    if (warrantyLogReq.status) {
                        let warrantyResponse = await axios.post(CONFIG.crm.warranty_url, warrantyLogReq.data, options)
                        let helperObj = {
                            shopify_order_id: orderData.data.shopify_order_id,
                            shopify_order_name: orderData.data.shopify_order_name,
                            order_name: orderData.data.order_name,
                            platform_type: requestData ? "LOG-A-Complain" : "CRM-WarrantyRegistration",
                            request_data: JSON.stringify(warrantyLogReq.data),
                            response_status_code: warrantyResponse.status,
                            response_data: warrantyResponse.data ? JSON.stringify(warrantyResponse.data) : 'warranty error'
                        }
                        await orderHistoryHelper.insertOrderHistory(helperObj)
                        if (warrantyResponse) {
                            if (requestData) {
                                let warrantyData = warrantyResponse.data
                                serviceReqObj = {
                                    order_name: orderData.data.order_name,
                                    request_id: Math.floor(Math.random() * 100) + 1,
                                    customer_code: warrantyCode,
                                    response_id: warrantyData.responseId || null,
                                    call_id: warrantyData.callId || null,
                                    message_description: warrantyData.messageDescription
                                }
                                await dbConnection.serviceRequestLog.create(serviceReqObj)
                            } else {
                                await dbConnection.orderItemSplit.update({ warranty_code: warrantyCode }, { where: { order_name: orderData.data.order_name } })

                            }
                            resolve({ status: true, data: warrantyResponse.data });
                        } else {
                            resolve({ status: false });
                        }
                    } else {
                        resolve({ status: false });
                    }
                } else {
                    resolve({ status: false });
                }
            } else {
                resolve({ status: false });
            }
        } else {
            resolve({ status: false });
        }
    })
}

exports.findCityAndStateCode = (zipcode) => {
    return new Promise(async (resolve, reject) => {
        const cityStateData = await dbConnection.cfaPincodeMapping.findOne({ where: { pincode: zipcode } })
        if (cityStateData && cityStateData.dataValues.state != null && cityStateData.dataValues.state != "null" && cityStateData.dataValues.city != null && cityStateData.dataValues.city != "null") {
            const state = cityStateData.dataValues.state;
            const city = cityStateData.dataValues.city;
            const cityStatePin = await dbConnection.cityStatePin.findOne({ where: { state: state, city: city } })
            if (cityStatePin && cityStatePin.dataValues.city_code != null && cityStatePin.dataValues.city_code != "null" && cityStatePin.dataValues.state_code != null && cityStatePin.dataValues.state_code != "null") {
                const obj = {
                    city_code: cityStatePin.dataValues.city_code,
                    state_code: cityStatePin.dataValues.state_code,
                }
                resolve({ status: true, obj: obj })
            } else {
                resolve({ status: false });
            }
        } else {
            resolve({ status: false });
        }
    })
}

exports.escalationEmail = async (req, res) => {
    try {
        const shop = req.query.shop;
        let data = req.body.data;
        const shopResponse = await dbConnection.shop.findOne({ where: { myshopify_domain: shop } })
        if (shopResponse && data && data.phone && data.complaint_number && data.escalation_type) {
            let emailObj = {};
            emailObj.phone = data.phone;
            emailObj.complaint_number = data.complaint_number;
            emailObj.escalation_type = data.escalation_type;
            if (data.email_id != "" && data.email_id != null && data.email_id != undefined) { emailObj.email_id = data.email_id }
            if (data.name != "" && data.name != null && data.name != undefined) { emailObj.name = data.name }
            if (data.feedback_type != "" && data.feedback_type != null && data.feedback_type != undefined) { emailObj.feedback_type = data.feedback_type }
            if (data.feedback != "" && data.feedback != null && data.feedback != undefined) { emailObj.feedback = data.feedback }
            dbConnection.escalationEmail.create(emailObj).then(async (response) => {
                res.status(CONFIG.status.SUCCESS).send({ "status": CONFIG.status.SUCCESS, "message": CONFIG.msg.SUCCESS });
                let emailResp = await emailController.escalationEmailSend(emailObj);
                if (emailResp.status) {
                    await dbConnection.escalationEmail.update({ is_email_send: '1' }, {
                        where: { id: response.dataValues.id }
                    })
                }
            }).catch((err) => {
                console.log("Error==>>", err);
                res.status(CONFIG.status.ERROR).send({ "status": CONFIG.status.ERROR, "is_registered": true, "message": CONFIG.msg.FAILED })
            })
        } else {
            res.status(CONFIG.status.ERROR).send({ "status": CONFIG.status.ERROR, "message": CONFIG.msg.FAILED })
        }
    } catch (error) {
        res.status(CONFIG.status.ERROR).send({ "status": CONFIG.status.ERROR, "message": CONFIG.msg.FAILED })

    }
}
exports.findCityAndStateCodeFromCRMTable = (zipcode) => {
    return new Promise(async (resolve, reject) => {
        const cityStateData = await dbConnection.cityStatePin.findOne({ where: { pincode: zipcode } })
        if (cityStateData && cityStateData.dataValues.city_code != null && cityStateData.dataValues.city_code != "null" && cityStateData.dataValues.state_code != null && cityStateData.dataValues.state_code != "null") {
            const obj = {
                city_code: cityStateData.dataValues.city_code,
                state_code: cityStateData.dataValues.state_code,
            }
            resolve({ status: true, obj: obj })
        } else {
            resolve({ status: false });
        }

    })
}
