const dbConnection = require("../models");
const shopConnection = dbConnection.shop;
const orderItemSplit = dbConnection.orderItemSplit;
const Sequelize = require("sequelize");
const modelOperators = Sequelize.Op;
const { Op, QueryTypes } = require('sequelize')
const CONFIG = require('../config/config_' + [process.env.NODE_ENV || 'local'] + '.json');
const response = require('../helper/appResponse');
const helper = require('../helper/helper');
const xlsx = require('json-as-xlsx')
const xlsxFile = require("xlsx");
const fs = require('fs');
const { promisify } = require('util')
const unlinkAsync = promisify(fs.unlink)
const path = require('path');
const s3 = require("../helper/awsClient");
const refundController = require('./refund-controller');
const delhiveryController = require('./delhivery-controller');
const sapController = require('./sap_controller');
const razorpayController = require('./razorpay-controller');
const extendedWarrantyServiceController = require('./extendedWarranty/extendedWarrantyService-controller');
const { default: axios } = require("axios");
const { getLogData, findCityAndStateCodeFromCRMTable, crmWarrantyRegistration, logServiceRequest } = require("./crm-controller");
const emailController = require('./email-controller');
const orderHistoryHelper = require('../helper/orderHistory')
const moment = require("moment")
const zippeeController = require('./zippee-controller')

exports.testOrder = async (req, res) => {
    console.log("hello world");
}
exports.getAllReturnOrders = async (req, res) => {
    try {
        const { shop, page, per_page, search, return_status, start_date, end_date, sort } = req.query;
        var options = {};
        let isRefund = false;
        var sortOrder = 'DESC';
        if (search) {
            options = {
                [modelOperators.or]: [
                    { order_name: { [modelOperators.like]: `%${search}%` } },
                    { shopify_order_name: { [modelOperators.like]: `%${search}%` } },
                    { sap_order_number: { [modelOperators.like]: `%${search}%` } },
                    { waybill_number: { [modelOperators.like]: `%${search}%` } },
                    { sku: { [modelOperators.like]: `%${search}%` } }
                ]
            }
        }
        if (return_status) {
            options.return_applied_status = return_status
        } else {
            // options.return_applied_status = { [modelOperators.ne]: null }
            options.return_applied_status = '0'
        }
        let shopResponse = await dbConnection.shop.findOne({ where: { myshopify_domain: shop } })
        if (start_date && end_date) {
            const { start, end } = helper.getStartEndDate(start_date, end_date, shopResponse.iana_timezone)
            options.order_created_at = {
                [modelOperators.lte]: end,
                [modelOperators.gte]: start
            }
        }
        if (sort == 'oldest') {
            sortOrder = 'ASC'
        }
        const { limit, offset } = helper.getPagination(page, per_page);
        orderItemSplit.findAndCountAll({
            where: options, limit: limit, offset: offset, order: [['order_created_at', `${sortOrder}`]],
            include: [
                {
                    model: dbConnection.delhiveryLog, as: "delhiveryLog",
                },
                {
                    model: dbConnection.sapLog, as: "sapLog"
                },
                {
                    model: dbConnection.order_refund, as: "orderRefund",
                    required: isRefund
                },
                {
                    model: dbConnection.orderCustomer, as: 'orderCustomers',
                    required: true
                },
                {
                    model: dbConnection.orderAddress, as: 'orderAddresses'
                }
            ],
        })
            .then(async data => {
                let orderArray = [];
                // for (let obj of data.rows) {
                //     let customerArr = []
                //     let customerAdd = await dbConnection.orderCustomer.findOne({ where: { order_id: obj.shopify_order_id } })
                //     customerArr.push(customerAdd.dataValues)
                //     obj.dataValues.orderCustomers = customerArr
                //     orderArray.push(obj);
                // }
                // const count = await orderItemSplit.count({
                //     where: options, limit: limit, offset: offset, order: [['order_created_at', `${sortOrder}`]],
                //     include: [
                //         {
                //             model: dbConnection.order_refund, as: "orderRefund",
                //             required: isRefund
                //         }
                //     ]
                // })
                // if (orderArray.length > 0) {
                //     let orderDataObj = {
                //         count: count,
                //         rows: orderArray
                //     };
                //     const response = helper.getPagingData(data, page, limit);
                //     res.status(CONFIG.status.SUCCESS).send(response);
                // } else {
                //     let orderDataObj = {
                //         count: 0,
                //         rows: []
                //     };
                //     const response = helper.getPagingData(orderDataObj, page, limit);
                //     res.status(CONFIG.status.SUCCESS).send(response);
                // }
                const response = helper.getPagingData(data, page, limit);
                res.status(CONFIG.status.SUCCESS).send(response);
            }).catch(err => {
                console.log("Error=======>", err)
                res.status(CONFIG.status.FAILED).send(CONFIG.msg.ERROR);
            });
    } catch (error) {
        console.log('error==--', error)
        res.status(CONFIG.status.FAILED).send(CONFIG.msg.ERROR);
    }
}
exports.getAllFailedOrders = async (req, res) => {
    try {
        const { shop, page, per_page, search, start_date, end_date, sort } = req.query;
        var options = {};
        let isRefund = false;
        var sortOrder = 'DESC';
        if (search) {
            options = {
                [modelOperators.or]: [
                    { order_name: { [modelOperators.like]: `%${search}%` } },
                    { shopify_order_name: { [modelOperators.like]: `%${search}%` } },
                    { sap_order_number: { [modelOperators.like]: `%${search}%` } },
                    { waybill_number: { [modelOperators.like]: `%${search}%` } },
                    { sku: { [modelOperators.like]: `%${search}%` } }
                ]
            }
        }
        options.middleware_status = { [modelOperators.like]: `%FAILED%` }
        let shopResponse = await dbConnection.shop.findOne({ where: { myshopify_domain: shop } })
        if (start_date && end_date) {
            const { start, end } = helper.getStartEndDate(start_date, end_date, shopResponse.iana_timezone)
            options.order_created_at = {
                [modelOperators.lte]: end,
                [modelOperators.gte]: start
            }
        }
        if (sort == 'oldest') {
            sortOrder = 'ASC'
        }
        const { limit, offset } = helper.getPagination(page, per_page);
        orderItemSplit.findAndCountAll({
            where: options, limit: limit, offset: offset, order: [['order_created_at', `${sortOrder}`]],
            include: [
                {
                    model: dbConnection.delhiveryLog, as: "delhiveryLog",
                },
                {
                    model: dbConnection.sapLog, as: "sapLog"
                },
                {
                    model: dbConnection.order_refund, as: "orderRefund",
                    required: isRefund
                },
                {
                    model: dbConnection.orderCustomer, as: 'orderCustomers',
                    required: true
                },
                {
                    model: dbConnection.orderAddress, as: 'orderAddresses'
                }
            ],
        })
            .then(async data => {
                let orderArray = [];
                // for (let obj of data.rows) {
                //     let customerArr = []
                //     let customerAdd = await dbConnection.orderCustomer.findOne({ where: { order_id: obj.shopify_order_id } })
                //     customerArr.push(customerAdd.dataValues)
                //     obj.dataValues.orderCustomers = customerArr
                //     orderArray.push(obj);
                // }
                // const count = await orderItemSplit.count({
                //     where: options, limit: limit, offset: offset, order: [['order_created_at', `${sortOrder}`]],
                //     include: [
                //         {
                //             model: dbConnection.order_refund, as: "orderRefund",
                //             required: isRefund
                //         }
                //     ]
                // })
                // if (orderArray.length > 0) {
                //     let orderDataObj = {
                //         count: count,
                //         rows: orderArray
                //     };
                //     const response = helper.getPagingData(orderDataObj, page, limit);
                //     res.status(CONFIG.status.SUCCESS).send(response);
                // } else {
                //     let orderDataObj = {
                //         count: 0,
                //         rows: []
                //     };
                //     const response = helper.getPagingData(orderDataObj, page, limit);
                //     res.status(CONFIG.status.SUCCESS).send(response);
                // }
                const response = helper.getPagingData(data, page, limit);
                res.status(CONFIG.status.SUCCESS).send(response);
            }).catch(err => {
                console.log("Error=======>", err)
                res.status(CONFIG.status.FAILED).send(CONFIG.msg.ERROR);
            });
    } catch (error) {
        console.log('error==--', error)
        res.status(CONFIG.status.FAILED).send(CONFIG.msg.ERROR);
    }
}
// exports.exportReturnOrderData = async (req, res) => {
//     let successMessage = response.responseMessage(CONFIG.status.FAILED, CONFIG.msg.FAILED)
//     let buffer
//     try {
//         res.status(CONFIG.status.SUCCESS).send(CONFIG.msg.SUCCESS)
//         let orderIds = []
//         let finalData = []
//         if (req.body.isAllResource) {
//             orderIds = await getFilterOrderData(req.body.filters)
//         } else {
//             orderIds = req.body.order_ids
//         }
//         for (let orderId of orderIds) {
//             let orderData = await this.getorderSplitDataByNameExport(orderId)
//             let orderCustomerData = orderData.data.dataValues.orderCustomers;
//             let delhiveryLogs = orderData.data.dataValues.delhiveryLogs;
//             let returnWayBillNo = null;
//             let WayBillNo = null;
//             let delhiveryArr = delhiveryLogs.filter(item => {
//                 return item.is_return == '1' && item.return_order_name == `${orderData.data.dataValues.return_order_name}`
//             })
//             let WayBillNodelhiveryArr = delhiveryLogs.filter(item => {
//                 return item.is_return == '0'&& item.is_cancel == '0' && item.reference_number == `${orderData.data.dataValues.order_name}`
//             })
//             returnWayBillNo = delhiveryArr.length > 0 ? delhiveryArr[0].waybill_number : ''
//             WayBillNo = WayBillNodelhiveryArr.length > 0 ? WayBillNodelhiveryArr[0].waybill_number : ''
//             for (let customer of orderCustomerData) {
//                 let customerName = customer.first_name + " " + customer.last_name
//                 dataObj = await exportOrderDataObject(orderData.data, customerName, returnWayBillNo, WayBillNo)
//                 finalData.push(dataObj)
//             }
//         }
//         const mainsettings = {
//             writeOptions: {
//                 type: 'buffer',
//                 bookType: 'xlsx'
//             }
//         }

//         const data = [
//             {
//                 sheet: 'Order Data',
//                 columns: [
//                     { label: 'Middleware Order Id', value: row => (row.middlewareOrderId ? row.middlewareOrderId : "") },
//                     { label: 'Shopify Order Id', value: row => (row.shopifyOrderId ? row.shopifyOrderId : "") },
//                     { label: 'Order Date', value: row => (row.orderDate ? row.orderDate : "") },
//                     { label: 'Customer Name', value: row => (row.customerName ? row.customerName : "") },
//                     { label: 'AWB Number', value: row => (row.awbNumber ? row.awbNumber : "") },
//                     { label: 'Product Title', value: row => (row.productTitle ? row.productTitle : "") },
//                     { label: 'Quantity', value: row => (row.qty ? row.qty : "") },
//                     { label: 'Order Amount', value: row => (row.amount ? row.amount : "") },
//                     { label: 'Middleware Order Status', value: row => (row.middlewareOrderStatus ? row.middlewareOrderStatus : "") },
//                     { label: 'Shipment Tracking Status', value: row => (row.delhiveryTrackingStatus ? row.delhiveryTrackingStatus : "") },
//                     { label: 'Replacement Reason', value: row => (row.replacementReason ? row.replacementReason : "") },
//                     { label: 'CRM Ticket Number', value: row => (row.crmTicketNumber ? row.crmTicketNumber : "") },
//                     { label: 'CRM Ticket Status', value: row => (row.crmTicketStatus ? row.crmTicketStatus : "") },
//                     { label: 'Warranty Code', value: row => (row.warrantyCode ? row.warrantyCode : "") },
//                     { label: 'Plant Code', value: row => (row.plantCode ? row.plantCode : "") },
//                     { label: 'Return AWB', value: row => (row.returnWayBillNo ? row.returnWayBillNo : "") },
//                     { label: 'SAP Invoice Date', value: row => (row.sapInvoiceDate ? row.sapInvoiceDate : "") },
//                     { label: 'Order Cancel Date', value: row => (row.orderCancelDate ? row.orderCancelDate : "") },
//                     { label: 'SAP Delivery Number', value: row => (row.sapDeliveryNo ? row.sapDeliveryNo : "") },
//                     { label: 'SAP Billing Number', value: row => (row.sapBillingNo ? row.sapBillingNo : "") },
//                     { label: 'SAP Order Number', value: row => (row.sapOrderNo ? row.sapOrderNo : "") },
//                     { label: 'Settlement Id', value: row => (row.settlementId ? row.settlementId : "") },
//                     { label: 'Razorpay Transfer Id', value: row => (row.razorpayTransferId ? row.razorpayTransferId : "") },
//                     { label: 'Refund Status', value: row => (row.refundStatus ? row.refundStatus : "") },
//                     { label: 'Refund Id', value: row => (row.refundId ? row.refundId : "") },
//                     { label: 'Pay Id', value: row => (row.checkoutId ? row.checkoutId : "") },
//                 ],
//                 content: finalData
//             }
//         ]

//         buffer = xlsx(data, mainsettings)

//         const fileName = 'orderData.xlsx';
//         let filePath = path.join(__dirname, '../uploads', fileName)
//         fs.writeFileSync(filePath, buffer);
//         const fileContent = fs.readFileSync(filePath);
//         var fileData = {
//             Bucket: CONFIG.aws.bucketName,
//             Key: "exportReturnOrderData/" + fileName,
//             Body: fileContent,
//             ContentEncoding: "base64",
//             ACL: "public-read",
//             ContentType: "application/xlsx",
//         };

//         const awsData = await s3.upload(fileData).promise();
//         fs.unlinkSync(filePath);
//         if (awsData) {
//             let exportFileUrl = awsData.Location;
//             await emailController.sendExportDataEmail(fileName, exportFileUrl, CONFIG.email.exportto, null, 'Exported Order Data');
//         }

//     } catch (e) {
//         console.log("error", e);
//         successMessage.status = CONFIG.status.ERROR
//         successMessage.message = CONFIG.msg.ERROR
//     }
// }

// let getFilterOrderData = (filters) => {
//     return new Promise(async (resolve, reject) => {
//         try{
//             let { search, return_status, start_date, end_date, sort } = filters;
//             var sortOrder = 'DESC';
//             let whereString = ""
//             let queryStr = `SELECT id FROM order_item_splits INNER JOIN order_customers ON order_item_splits.shopify_order_id = order_customers.order_id WHERE`

//             if (search) {
//                 whereString += ` (order_item_splits.order_name LIKE '%${search}%' OR order_item_splits.shopify_order_name LIKE '%${search}%' OR order_item_splits.sap_order_number LIKE '%${search}%' OR order_item_splits.waybill_number LIKE '%${search}%' OR order_item_splits.sku LIKE '%${search}%' )`
//             }
//             if (return_status) {
//                 whereString += whereString.length > 0 ? ` AND order_item_splits.return_applied_status = ${return_status}` : ` order_item_splits.return_applied_status = ${return_status}`
//             } else {
//                 whereString += whereString.length > 0 ? ` AND order_item_splits.return_applied_status IS NOT NULL` : ` order_item_splits.return_applied_status IS NOT NULL`
//             }
//             if (start_date && end_date) {
//                 start_date += ' 00:00:00'
//                 end_date += ' 23:59:59'
//                 whereString += whereString.length > 0 ? ` AND (order_item_splits.order_created_at >= '${start_date}' AND order_item_splits.order_created_at <= '${end_date}')` : `(order_item_splits.order_created_at >= '${start_date}' AND order_item_splits.order_created_at <= '${end_date}')`
//             }
//             if (sort == 'oldest') {
//                 sortOrder = 'ASC'
//                 whereString += ` ORDER BY order_item_splits.order_created_at ${sortOrder}`
//             } else {
//                 whereString += ` ORDER BY order_item_splits.order_created_at ${sortOrder}`
//             }
//             queryStr += whereString
//             let queryData = await dbConnection.sequelize.query(queryStr, { type: QueryTypes.SELECT })
//             resolve(queryData)
//         } catch (err) {
//             console.log('Error getFilterOrderData=====>', err)
//         }
//     })
// }

exports.exportFailedOrderData = async (req, res) => {
    let successMessage = response.responseMessage(CONFIG.status.FAILED, CONFIG.msg.FAILED)
    let buffer
    try {
        res.status(CONFIG.status.SUCCESS).send(CONFIG.msg.SUCCESS)
        let orderDatas
        let finalData = []
        if (req.body.isAllResource) {
            orderDatas = await getFilterOrderData(req.body.filters)
        } else {
            orderDatas = await orderItemSplit.findAll({
                where: { order_name: req.body.order_ids },
                include: [
                    {
                        model: dbConnection.order_refund, as: "orderRefund"
                    },
                    {
                        model: dbConnection.orderCustomer, as: 'orderCustomers',
                        required: true
                    }
                ],
            })
        }
        for (let data of orderDatas) {
            const obj = {
                returnWayBillNo: null,
                WayBillNo: null,
                returnSapConfirmationNo: null,
                sapConfirmationNo: null,
                serviceRequestLogObj: null,
                sapOne: null,
                sapNew: null,
            };

            // Delivery
            let deliveryLogs = await dbConnection.delhiveryLog.findAll({ where: { order_name: data.order_name } });
            if (deliveryLogs.length > 0) {
                let delhiveryArr = deliveryLogs.filter(item => item.is_return === '1' && item.return_order_name === data.return_order_name);
                let WayBillNodelhiveryArr = deliveryLogs.filter(item => item.is_return === '0' && item.is_cancel === '0' && item.reference_number === data.order_name);
                obj.returnWayBillNo = delhiveryArr.length > 0 ? delhiveryArr[0].waybill_number : '';
                obj.WayBillNo = WayBillNodelhiveryArr.length > 0 ? WayBillNodelhiveryArr[0].waybill_number : '';
            }

            // SAP
            let sapLogs = await dbConnection.sapLog.findAll({ where: { order_name: data.order_name } });
            if (sapLogs.length > 0) {
                let sapReturnArr = []
                if (data.is_cancel == "1") {
                    sapReturnArr = sapLogs.filter(item => item.is_cancel === '1');
                } else {
                    sapReturnArr = sapLogs.filter(item => item.is_return === '1' && item.return_order_name === data.return_order_name);
                }
                let sapArr = sapLogs.filter(item => item.is_return === '0' && item.is_cancel === '0' && item.order_name === data.order_name);
                obj.returnSapConfirmationNo = sapReturnArr.length > 0 ? sapReturnArr[0].sap_confirmation_id : '';
                obj.sapConfirmationNo = sapArr.length > 0 ? sapArr[0].sap_confirmation_id : '';

                obj.sapOne = JSON.parse(JSON.stringify(sapArr))[0]
                obj.sapNew = JSON.parse(JSON.stringify(sapReturnArr))[0]
            }

            // serviceRequestLog
            let serviceRequestLog = await dbConnection.serviceRequestLog.findAll({ where: { order_name: data.order_name }, order: [['id', 'DESC']] });
            if (serviceRequestLog.length > 0) {
                obj.serviceRequestLogObj = JSON.parse(JSON.stringify(serviceRequestLog))[0]
            }

            let dataObj = await exportOrderDataObject(data, obj);
            finalData.push(dataObj);
        }
        const mainsettings = {
            writeOptions: {
                type: 'buffer',
                bookType: 'xlsx'
            }
        }

        const data = [
            {
                sheet: 'Order Data',
                columns: [
                    { label: 'Middleware Order Id', value: row => (row.middlewareOrderId ? row.middlewareOrderId : "") },
                    { label: 'Shopify Order Id', value: row => (row.shopifyOrderId ? row.shopifyOrderId : "") },
                    { label: 'Order Date', value: row => (row.orderDate ? row.orderDate : "") },
                    { label: 'Customer Name', value: row => (row.customerName ? row.customerName : "") },
                    { label: 'AWB Number', value: row => (row.awbNumber ? row.awbNumber : "") },
                    { label: 'Product Title', value: row => (row.productTitle ? row.productTitle : "") },
                    { label: 'Quantity', value: row => (row.qty ? row.qty : "") },
                    { label: 'Order Amount', value: row => (row.amount ? row.amount : "") },
                    { label: 'Middleware Status', value: row => (row.middlewareStatus ? row.middlewareStatus : "") },
                    { label: 'Failed Reason', value: row => (row.failedReason ? row.failedReason : "") },
                    //{ label: 'Shipment Tracking Status', value: row => (row.delhiveryTrackingStatus ? row.delhiveryTrackingStatus : "") },
                    //{ label: 'Replacement Reason', value: row => (row.replacementReason ? row.replacementReason : "") },
                    { label: 'CRM Ticket Number', value: row => (row.crmTicketNumber ? row.crmTicketNumber : "") },
                    { label: 'CRM Ticket Status', value: row => (row.crmTicketStatus ? row.crmTicketStatus : "") },
                    { label: 'Warranty Code', value: row => (row.warrantyCode ? row.warrantyCode : "") },
                    { label: 'Plant Code', value: row => (row.plantCode ? row.plantCode : "") },
                    { label: 'Return AWB', value: row => (row.returnAWB ? row.returnAWB : "") },
                    { label: 'SAP Invoice Date', value: row => (row.sapInvoiceDate ? row.sapInvoiceDate : "") },
                    { label: 'Order Cancel Date', value: row => (row.orderCancelDate ? row.orderCancelDate : "") },
                    { label: 'SAP Delivery Number', value: row => (row.sapDeliveryNo ? row.sapDeliveryNo : "") },
                    { label: 'SAP Billing Number', value: row => (row.sapBillingNo ? row.sapBillingNo : "") },
                    { label: 'SAP Order Number', value: row => (row.sapOrderNo ? row.sapOrderNo : "") },
                    { label: 'New SAP Delivery Number', value: row => (row.returnsapDeliveryNo ? row.returnsapDeliveryNo : "") },
                    { label: 'New SAP Billing Number', value: row => (row.returnsapBillingNo ? row.returnsapBillingNo : "") },
                    { label: 'New SAP Order Number', value: row => (row.returnsapOrderNo ? row.returnsapOrderNo : "") },
                    { label: 'SAP Confirmation Number', value: row => (row.sapConfirmationNo ? row.sapConfirmationNo : "") },
                    { label: 'SAP New Confirmation Number', value: row => (row.returnSapConfirmationNo ? row.returnSapConfirmationNo : "") },
                    // { label: 'Settlement Id', value: row => (row.settlementId ? row.settlementId : "") },
                    //{ label: 'Razorpay Transfer Id', value: row => (row.razorpayTransferId ? row.razorpayTransferId : "") },
                    //{ label: 'Refund Status', value: row => (row.refundStatus ? row.refundStatus : "") },
                    { label: 'Refund Id', value: row => (row.refundId ? row.refundId : "") },
                    { label: 'Pay Id', value: row => (row.checkoutId ? row.checkoutId : "") },
                    { label: 'Service Call Id', value: row => (row.serviceCallId ? row.serviceCallId : "") },
                    { label: 'Service Response Id', value: row => (row.serviceResponseId ? row.serviceResponseId : "") },
                    { label: 'Service Message', value: row => (row.serviceMessage ? row.serviceMessage : "") }
                ],
                content: finalData
            }
        ]

        buffer = xlsx(data, mainsettings)

        const fileName = 'orderData.xlsx';
        let filePath = path.join(__dirname, '../uploads', fileName)
        fs.writeFileSync(filePath, buffer);
        const fileContent = fs.readFileSync(filePath);
        var fileData = {
            Bucket: CONFIG.aws.bucketName,
            Key: "exportfailedOrderData/" + fileName,
            Body: fileContent,
            ContentEncoding: "base64",
            ACL: "public-read",
            ContentType: "application/xlsx",
        };

        const awsData = await s3.upload(fileData).promise();
        fs.unlinkSync(filePath);
        if (awsData) {
            let exportFileUrl = awsData.Location;
            //console.log('exportFileUrl---->',exportFileUrl)
            await emailController.sendExportDataEmail(fileName, exportFileUrl, CONFIG.email.exportto, null, 'Exported Order Data');
        }

    } catch (e) {
        console.log("error", e);
        successMessage.status = CONFIG.status.ERROR
        successMessage.message = CONFIG.msg.ERROR
    }
}

let getFilterOrderData = (filters) => {
    return new Promise(async (resolve, reject) => {
        try {
            let { search, start_date, end_date } = filters;
            let { shop } = querypara;

            let options = {};
            if (search) {
                options = {
                    [modelOperators.or]: [
                        { order_name: { [modelOperators.like]: `%${search}%` } },
                        { shopify_order_name: { [modelOperators.like]: `%${search}%` } },
                        { sap_order_number: { [modelOperators.like]: `%${search}%` } },
                        { waybill_number: { [modelOperators.like]: `%${search}%` } },
                        { sku: { [modelOperators.like]: `%${search}%` } }
                    ]
                }
            }

            options.middleware_status = { [modelOperators.like]: "FAILED" }
            let shopResponse = await dbConnection.shop.findOne({ where: { myshopify_domain: shop } })
            if (start_date && end_date) {
                const { start, end } = helper.getStartEndDate(start_date, end_date, shopResponse.iana_timezone)
                options.order_created_at = {
                    [modelOperators.lte]: end,
                    [modelOperators.gte]: start
                }
            }

            let queryData = await orderItemSplit.findAll({
                where: options,
                include: [
                    {
                        model: dbConnection.order_refund, as: "orderRefund"
                    },
                    {
                        model: dbConnection.orderCustomer, as: 'orderCustomers',
                        required: true
                    }
                ],
            })
            resolve(queryData)
        } catch (err) {
            console.log('Error filterOrderData=====>', err)
        }
    })
}

exports.exportDataByPlantCode = async (req, res) => {
    try {
        const previousDate = moment().subtract(30, 'days').format('YYYY-MM-DD HH:mm:ss');
        let plantResponse = await dbConnection.cfaPlantLocation.findAll()
        for (let plant of plantResponse) {
            if (plant.email) {
                let orderData = await dbConnection.orderItemSplit.findAll({
                    where: {
                        plant_code: plant.plant_code,
                        order_status: "Completed",
                        shipment_status: 'label_printed',
                        sap_status: 'Pushed',
                        delhivery_status: 'Pushed',
                        is_cancel: '0',
                        order_created_at: {
                            [Op.gte]: previousDate
                        }
                    }
                })
                if (orderData && orderData.length > 0) {
                    let fileName = `plancode_${plant.plant_code}`
                    await this.sendPlantOrderData(orderData, fileName, '<EMAIL>', null)
                    // await this.sendPlantOrderData(orderData, fileName, plant.email, '<EMAIL>,<EMAIL>,<EMAIL>')
                }
            }
        }
        res.status(200).send({ message: "Success" })
    } catch (error) {
        console.log("exportDataByPlantCode error ", error);
        res.status(400).send({ message: "Failed" })
    }
}

exports.sendPlantOrderData = (orderData, file_name, email, cc_mail) => {
    return new Promise(async (resolve, reject) => {
        try {
            let formatedDate = moment().format("YYYY-MM-DD")
            let finalData = []
            for (let order of orderData) {
                let orderCustomer = await dbConnection.orderCustomer.findOne({ where: { order_id: order.shopify_order_id } })
                finalData.push({
                    orderNumber: order.order_name,
                    awbNumber: order.waybill_number,
                    customerName: orderCustomer.first_name || '' + " " + orderCustomer.last_name || '',
                    customerNumber: orderCustomer.phone_number,
                    sapStatus: order.sap_status,
                    sapOrderNo: order.sap_order_number,
                    sapBillingNo: order.sap_billing_number,
                    sapDeliveryNo: order.sap_delivery_number,
                })
            }
            const mainsettings = {
                writeOptions: {
                    type: 'buffer',
                    bookType: 'xlsx'
                }
            }

            const data = [
                {
                    sheet: 'Order Data',
                    columns: [
                        { label: 'Order Number', value: row => (row.orderNumber ? row.orderNumber : "") },
                        { label: 'AWB Number', value: row => (row.awbNumber ? row.awbNumber : "") },
                        { label: 'Customer Name', value: row => (row.customerName ? row.customerName : "") },
                        { label: 'Customer Number', value: row => (row.customerNumber ? row.customerNumber : "") },
                        { label: 'SAP Status', value: row => (row.sapStatus ? row.sapStatus : "") },
                        { label: 'SAP Delivery Number', value: row => (row.sapDeliveryNo ? row.sapDeliveryNo : "") },
                        { label: 'SAP Order Number', value: row => (row.sapOrderNo ? row.sapOrderNo : "") },
                        { label: 'SAP Billing Number', value: row => (row.sapBillingNo ? row.sapBillingNo : "") }
                    ],
                    content: finalData
                }
            ]
            buffer = xlsx(data, mainsettings)

            const fileName = 'orderData.xlsx';
            let filePath = path.join(__dirname, '../uploads', fileName)
            fs.writeFileSync(filePath, buffer);
            const fileContent = fs.readFileSync(filePath);
            var fileData = {
                Bucket: CONFIG.aws.bucketName,
                Key: `${file_name}/${formatedDate}/${fileName}`,
                Body: fileContent,
                ContentEncoding: "base64",
                ACL: "public-read",
                ContentType: "application/xlsx",
            };

            const awsData = await s3.upload(fileData).promise();
            fs.unlinkSync(filePath);
            if (awsData) {
                let exportFileUrl = awsData.Location;
                //console.log("exportFileUrl=>>>>>", exportFileUrl);
                await emailController.sendExportDataEmail(fileName, exportFileUrl, email, cc_mail, 'Pending to Be invoiced - Symphony D2C');
            }
            resolve()
        } catch (error) {
            console.log("errror", error);
            resolve()
        }
    })
}

exports.orderNotPickedData = async () => {
    try {
        let date = moment().subtract(3, 'days').format("YYYY-MM-DD")
        let year = '2023-01-01 00:00:00'
        let orderData = await dbConnection.orderItemSplit.findAll({
            where: {
                order_status: "Completed",
                sap_status: 'Invoiced',
                delhivery_status: 'Pushed',
                shipment_status: 'label_printed',
                is_return: '0',
                is_cancel: '0',
                delivered_at: {
                    [Op.is]: null
                },
                sap_invoice_date: { [Op.lt]: date },
                order_created_at: {
                    [Op.gte]: year
                }
            }
        })
        if (orderData && orderData.length > 0) {
            let fileName = `not_pickup_data`
            // await this.sendOrderData(orderData, fileName, CONFIG.email.pendingPickupMail, 'Pending to Be Picked up - Symphony D2C')
            await this.sendOrderData(orderData, fileName, '<EMAIL>', 'Pending to Be Picked up - Symphony D2C')
        }
    } catch (error) {
        console.log("error", error);
    }
}

exports.orderNotDeliveredData = async () => {
    try {
        let date = moment().subtract(8, 'days').format("YYYY-MM-DD")
        let year = '2023-01-01 00:00:00'

        let orderData = await dbConnection.orderItemSplit.findAll({
            where: {
                order_status: "Completed",
                sap_status: 'Invoiced',
                delhivery_status: 'Pushed',
                is_return: '0',
                is_cancel: '0',
                delivered_at: {
                    [Op.is]: null
                },
                sap_invoice_date: { [Op.lt]: date },
                [Op.and]: [{
                    shipment_status: {
                        [Op.ne]: 'label_printed'
                    }
                }, {
                    shipment_status: {
                        [Op.ne]: 'delivered'
                    }
                }],
                order_created_at: {
                    [Op.gte]: year
                }
            }
        })
        if (orderData && orderData.length > 0) {
            let fileName = `not_delivered_data`
            // await this.sendOrderData(orderData, fileName, CONFIG.email.pendingDeliveryMail, 'Delay in Delivery - Symphony D2C')
            await this.sendOrderData(orderData, fileName, '<EMAIL>', 'Delay in Delivery - Symphony D2C')
        }
    } catch (error) {
        console.log("error", error);
    }
}

exports.sendOrderData = async (orderData, file_name, email, subject) => {
    return new Promise(async (resolve, reject) => {
        try {
            let formatedDate = moment().format("YYYY-MM-DD")
            let finalData = []
            for (let order of orderData) {
                let orderCustomer = await dbConnection.orderCustomer.findOne({ where: { order_id: order.shopify_order_id } })
                finalData.push({
                    orderNumber: order.order_name,
                    awbNumber: order.waybill_number,
                    customerName: orderCustomer.first_name || '' + " " + orderCustomer.last_name || '',
                    customerNumber: orderCustomer.phone_number
                })
            }
            const mainsettings = {
                writeOptions: {
                    type: 'buffer',
                    bookType: 'xlsx'
                }
            }

            const data = [
                {
                    sheet: 'Order Data',
                    columns: [
                        { label: 'Order NUmber', value: row => (row.orderNumber ? row.orderNumber : "") },
                        { label: 'AWB Number', value: row => (row.awbNumber ? row.awbNumber : "") },
                        { label: 'Customer Name', value: row => (row.customerName ? row.customerName : "") },
                        { label: 'Customer Number', value: row => (row.customerNumber ? row.customerNumber : "") }
                    ],
                    content: finalData
                }
            ]
            buffer = xlsx(data, mainsettings)

            const fileName = 'orderData.xlsx';
            let filePath = path.join(__dirname, '../uploads', fileName)
            fs.writeFileSync(filePath, buffer);
            const fileContent = fs.readFileSync(filePath);
            var fileData = {
                Bucket: CONFIG.aws.bucketName,
                Key: `exportOrderData/${file_name}_${formatedDate}.xlsx`,
                Body: fileContent,
                ContentEncoding: "base64",
                ACL: "public-read",
                ContentType: "application/xlsx",
            };

            const awsData = await s3.upload(fileData).promise();
            fs.unlinkSync(filePath);
            if (awsData) {
                let exportFileUrl = awsData.Location;
                await emailController.sendExportDataEmail(fileName, exportFileUrl, email, null, subject);
            }
            resolve()
        } catch (error) {
            console.log("errror", error);
            resolve()
        }
    })
}

exports.getAllOrder = async (req, res) => {
    try {
        const { shop, page, per_page, search, financial_status, middleware_status, order_status, sap_status, delhivery_status, start_date, end_date, sort, shipment_status, refund_status } = req.query;

        var options = {};
        let refundOptions = {}
        let isRefund = false;
        var sortOrder = 'DESC';
        options.middleware_status = { [modelOperators.not]: ["FAILED", "OLD ORDERS"] }
        if (search) {
            options = {
                [modelOperators.or]: [
                    { order_name: { [modelOperators.like]: `%${search}%` } },
                    { shopify_order_name: { [modelOperators.like]: `%${search}%` } },
                    { sap_order_number: { [modelOperators.like]: `%${search}%` } },
                    { waybill_number: { [modelOperators.like]: `%${search}%` } },
                    { sku: { [modelOperators.like]: `%${search}%` } }
                ]
            }
        }
        if (order_status) {
            options.order_status = { [modelOperators.like]: `%${order_status}%` }
        }
        if (middleware_status) {
            options.middleware_status = middleware_status
        }
        if (sap_status) {
            options.sap_status = { [modelOperators.like]: `%${sap_status}%` }
        }
        if (delhivery_status) {
            options.delhivery_status = { [modelOperators.like]: `%${delhivery_status}%` }
        }
        if (financial_status?.length > 0) {
            if (financial_status[0] == "COD") {
                options.gateway = "Cash on Delivery (COD)"
            } else {
                options.gateway = { [modelOperators.not]: "Cash on Delivery (COD)" }
            }
        }
        if (shipment_status) {
            options.shipment_status = { [modelOperators.like]: `%${shipment_status}%` }
        }
        let shopResponse = await dbConnection.shop.findOne({ where: { myshopify_domain: shop } })
        if (start_date && end_date) {
            const { start, end } = helper.getStartEndDate(start_date, end_date, shopResponse.iana_timezone)
            options.order_created_at = {
                [modelOperators.lte]: end,
                [modelOperators.gte]: start
            }
        }
        if (sort == 'oldest') {
            sortOrder = 'ASC'
        }
        if (refund_status) {
            isRefund = true;
            let redundStatus = refund_status == 'all' ? ['processed', 'failed', 'pending', 'CN-pending'] : (refund_status == 'success' ? ['processed'] : [refund_status])
            refundOptions = { '$orderRefund.refund_status$': redundStatus }
        }
        options.is_order_hold = '0'
        const { limit, offset } = helper.getPagination(page, per_page);
        orderItemSplit.findAndCountAll({
            where: options, limit: limit, offset: offset, order: [['order_created_at', `${sortOrder}`]],
            include: [
                {
                    model: dbConnection.delhiveryLog, as: "delhiveryLog",
                    required: false,
                },
                {
                    model: dbConnection.sapLog, as: "sapLog",
                    required: false,
                },
                {
                    model: dbConnection.order_refund, as: "orderRefund",
                    required: isRefund,
                    where: refundOptions
                },
                {
                    model: dbConnection.orderCustomer, as: 'orderCustomers',
                    required: true
                },
                {
                    model: dbConnection.orderAddress, as: 'orderAddresses'
                }
            ],

        })
            .then(async data => {
                const response = helper.getPagingData(data, page, limit);
                res.status(CONFIG.status.SUCCESS).send(response);
            }).catch(err => {
                console.log("Error=======>", err)
                res.status(CONFIG.status.FAILED).send(CONFIG.msg.ERROR);
            });
    } catch (error) {
        console.log('err==--', error)
        res.status(CONFIG.status.FAILED).send(CONFIG.msg.ERROR);
    }
}

exports.pushOrder = async (req, res) => {
    try {
        let order_ids = [];
        if (req.body.isAllResource) {
            order_ids = await getOrderIds(req.body.filters);
        } else {
            order_ids = req.body.order_ids;
            if (order_ids.length == 0) {
                throw new ErrorHandler(400, 'order id is required')
            }
        }
        this.pushOrderOnDelhiveryAndSap(order_ids)
        return res.status(200).send({ statusCode: 200, message: "success" });
    } catch (err) {
        console.log("err-------", err)
        res.status(CONFIG.status.FAILED).send(CONFIG.msg.ERROR);
    }
}

//Create service request
exports.createServiceRequest = async (req, res) => {
    try {
        let { serviceRequest } = req.body
        console.log(req.body)
        logServiceRequest(serviceRequest.orderName, serviceRequest.serviceData)
        return res.status(200).send({ statusCode: 200, message: "success" });
    } catch (error) {
        console.log("pushOrderManually---->", error)
    }
}



// push order manually
exports.pushOrderManually = async (req, res) => {
    try {
        let order_ids = [];
        if (req.body.isAllResource) {
            order_ids = await getOrderIds(req.body.filters);
        } else {
            order_ids = req.body.order_ids;
            if (order_ids.length == 0) {
                throw new ErrorHandler(400, 'order id is required')
            }
        }
        let plantCode = req.body.plantCode ? req.body.plantCode : null
        this.pushOrderOnDelhiveryAndSap(order_ids, plantCode)
        return res.status(200).send({ statusCode: 200, message: "success" });
    } catch (err) {
        console.log("ekeke", err)
        res.status(CONFIG.status.FAILED).send(CONFIG.msg.ERROR);
    }
}

exports.cancelAppOrder = async (req, res) => {
    try {
        let order_ids = [];
        if (req.body.isAllResource) {
            order_ids = await getOrderIds(req.body.filters);
        } else {
            order_ids = req.body.order_ids;
            if (order_ids.length == 0) {
                throw new ErrorHandler(400, 'order id is required')
            }
        }
        res.status(200).send({ statusCode: 200, message: "success" });
        cancelOrderOnDelhiveryAndSap(order_ids)
    } catch (err) {
        console.log("erer", err)
        res.status(CONFIG.status.FAILED).send(CONFIG.msg.ERROR);
    }
}
exports.returnAppOrder = async (req, res) => {
    try {
        let { isAllResource, returnStatus } = req.body
        let order_ids = [];
        if (isAllResource) {
            order_ids = await getOrderIds(req.body.filters);
        } else {
            order_ids = req.body.order_ids;
            if (order_ids.length == 0) {
                throw new ErrorHandler(400, 'order id is required')
            }
        }
        res.status(200).send({ statusCode: 200, message: "success" });
        returnOrderOnDelhiveryAndSap(order_ids, returnStatus)
    } catch (err) {
        console.log("eeeer", err)
        res.status(CONFIG.status.FAILED).send(CONFIG.msg.ERROR);
    }
}
let returnOrderOnDelhiveryAndSap = async (orderNames, returnStatus) => {
    try {
        for (const order of orderNames) {
            let orderData = await delhiveryController.getorderSplitDataById(order)
            if (orderData.status) {
                orderData = orderData.data
            }
            let middlewareStatus = returnStatus == 'approved' ? 'RETURN REQUEST APPROVED' : 'RETURN REQUEST REJECTED'
            let dbRes = await dbConnection.orderCancellation.create({ order_name: order, status_type: "return requested " + returnStatus, return_order_name: orderData.return_order_name, middleware_status: middlewareStatus })
            if (dbRes.dataValues.id) {
                if (returnStatus == 'approved') {
                    let returnApprovedDate = new Date().toISOString()
                    await dbConnection.orderItemSplit.update({ return_approved_date: returnApprovedDate }, { where: { order_name: order } })
                    await delhiveryController.reverseOrderDelhivery(order)
                } else {
                    await dbConnection.orderItemSplit.update({ middleware_status: "DELIVERED", order_status: "Completed", return_applied_status: '2', is_return: "0", return_approved_date: null }, { where: { order_name: order } })
                }
            }
        }
    } catch (err) {
        console.log('Error returnOrderOnDelhiveryAndSap=====>', err)
    }
}
let getOrderIds = (filters) => {
    return new Promise((resolve, reject) => {
        try {
            const { search, order_status, sap_status, delhivery_status, financial_status, shipment_status, start_date, end_date, sort } = filters;
            var options = {};
            var sortOrder = 'DESC';
            if (search) {
                options = {
                    [modelOperators.or]: [
                        { order_name: { [modelOperators.like]: `%${search}%` } },
                        { shopify_order_name: { [modelOperators.like]: `%${search}%` } },
                        { sap_order_number: { [modelOperators.like]: `%${search}%` } },
                        { waybill_number: { [modelOperators.like]: `%${search}%` } },
                        { sku: { [modelOperators.like]: `%${search}%` } }
                    ]
                }
            }
            if (order_status.length > 0) {
                options.order_status = { [modelOperators.like]: `%${order_status[0]}%` }
            }
            if (sap_status.length > 0) {
                options.sap_status = { [modelOperators.like]: `%${sap_status[0]}%` }
            }
            if (delhivery_status.length > 0) {
                options.delhivery_status = { [modelOperators.like]: `%${delhivery_status[0]}%` }
            }
            if (financial_status.length > 0) {
                if (financial_status[0] == "COD") {
                    options.gateway = "Cash on Delivery (COD)"
                } else {
                    options.gateway = { [modelOperators.not]: "Cash on Delivery (COD)" }
                }
            }
            if (shipment_status) {
                options.shipment_status = { [modelOperators.like]: `%${shipment_status}%` }
            }
            if (start_date && end_date) {
                const { start, end } = helper.getStartEndDate(start_date, end_date)
                options.order_created_at = {
                    [modelOperators.lte]: end,
                    [modelOperators.gte]: start
                }
            }
            if (sort == 'oldest') {
                sortOrder = 'ASC'
            }
            orderItemSplit.findAll({
                where: options, attributes: ['order_name'], order: [['order_created_at', `${sortOrder}`]]
            })
                .then((ids) => {
                    orederIds = ids.map(id => id.order_name)
                    resolve(orederIds);
                }).catch(err => {
                    resolve(orederIds)
                })
        } catch (err) {
            console.log('Error getOrderIds=====>', err)
        }
    })
}

exports.returnOrderRequest = async (req, res) => {
    try {
        let { isAllResource } = req.body
        let order_ids = [];
        if (isAllResource) {
            order_ids = await getOrderIds(req.body.filters);
        } else {
            order_ids = req.body.order_ids;
            if (order_ids.length == 0) {
                throw new ErrorHandler(400, 'order id is required')
            }
        }
        res.status(200).send({ statusCode: 200, message: "success" });
        createReturnOrderRequest(order_ids)
    } catch (err) {
        console.log("returnOrderRequest", err)
        res.status(CONFIG.status.FAILED).send(CONFIG.msg.ERROR);
    }
}
exports.returnRequestNumber = (order, orderNumber) => {

    if (orderNumber) {
        let requestOrderNumber = orderNumber.split("R-")
        orderNum = parseInt(requestOrderNumber[1]) + 1
        return requestOrderNumber[0] + "R-" + orderNum
    } else {
        return order + "-R-1"
    }
}

let createReturnOrderRequest = async (orderNames) => {
    try {
        for (const order of orderNames) {
            let orderResData = await this.getorderSplitDataByName(order)
            let orderData = JSON.parse(JSON.stringify(orderResData.data))
            let returnRequestNum = this.returnRequestNumber(order, orderData.return_order_name)
            let returnRequestDate = new Date().toISOString()
            if ((orderData.middleware_status == "OLD ORDERS" || orderData.middleware_status == "DELIVERED" || orderData.middleware_status == "RETURN PICKUP CANCELLED BY CUSTOMER") && (orderData.return_applied_status == null || orderData.return_applied_status == 2)) {
                await dbConnection.orderCancellation.create({ order_name: order, status_type: "order_return_by_app", return_order_name: returnRequestNum, middleware_status: 'RETURN REQUESTED' }).then(async (dbRes) => {
                    if (dbRes.dataValues.id) {
                        await orderItemSplit.update({ replacement_reason: 'Middleware return order request', middleware_status: 'RETURN REQUESTED', return_applied_status: '0', return_order_name: returnRequestNum, return_request_date: returnRequestDate }, { where: { order_name: order } })
                    }
                }).catch((e) => {
                    console.log('eroorr======', e)
                })
            }
        }
    } catch (err) {
        console.log('Error createReturnOrderRequest=====>', err)
    }
}

let filterOrderData = (filters, querypara) => {
    return new Promise(async (resolve, reject) => {
        try {
            let { search, middleware_status, start_date, end_date, sort } = filters;
            let { shop } = querypara;

            let options = {};
            let sortOrder = 'DESC';
            if (search) {
                options = {
                    [modelOperators.or]: [
                        { order_name: { [modelOperators.like]: `%${search}%` } },
                        { shopify_order_name: { [modelOperators.like]: `%${search}%` } },
                        { sap_order_number: { [modelOperators.like]: `%${search}%` } },
                        { waybill_number: { [modelOperators.like]: `%${search}%` } },
                        { sku: { [modelOperators.like]: `%${search}%` } }
                    ]
                }
            }
            if (middleware_status) {
                options.middleware_status = { [modelOperators.like]: `%${middleware_status}%` }
            }
            console.log('shop------->', shop)
            let shopResponse = await dbConnection.shop.findOne({ where: { myshopify_domain: shop } })
            if (start_date && end_date) {
                const { start, end } = helper.getStartEndDate(start_date, end_date, shopResponse.iana_timezone)
                options.order_created_at = {
                    [modelOperators.lte]: end,
                    [modelOperators.gte]: start
                }
            }
            if (sort == 'oldest') {
                sortOrder = 'ASC'
            }

            let queryData = await orderItemSplit.findAll({
                where: options,
                include: [
                    {
                        model: dbConnection.order_refund, as: "orderRefund"
                    },
                    {
                        model: dbConnection.orderCustomer, as: 'orderCustomers',
                        required: true
                    }
                ],

            })

            resolve(queryData)
        } catch (err) {
            console.log('Error filterOrderData=====>', err)
        }
    })
}

let cancelOrderOnDelhiveryAndSap = async (orderNames) => {
    try {
        for (const order of orderNames) {
            let orderResData = await this.getorderSplitDataByName(order)
            if (orderResData.status) {
                let orderData = JSON.parse(JSON.stringify(orderResData.data))
                let dbRes = await dbConnection.orderCancellation.create({ order_name: orderData.order_name, status_type: "order_cancel_app", return_order_name: orderData.order_name, middleware_status: "ORDER_CANCELLED" })
                if (dbRes.dataValues.id) {
                    if (orderData.sap_status == 'Pushed') {
                        await delhiveryController.sapDelivery(order);
                        let orderResData = await this.getorderSplitDataByName(order)
                        orderData = JSON.parse(JSON.stringify(orderResData.data))
                    }
                    if (orderData.financial_status == "paid" && orderData.gateway != "Cash on Delivery (COD)") {
                        await refundController.checkingForRefund(orderData);
                        //emailController.sendEmailToSymphony(orderData.order_name, 'Cancel')
                    }
                    await delhiveryController.cancelOrder(orderData.order_name)
                }
            }
        }
    } catch (err) {
        console.log('Error cancelOrderOnDelhiveryAndSap=====>', err)
    }
}

exports.pushOrderOnDelhiveryAndSap = async (orderNames, plantCode) => {
    try {
        for (const order of orderNames) {
            console.log("order pushOrderOnDelhiveryAndSap namee===>>>", order)
            let orderResData = await this.getorderSplitDataByName(order)
            if (orderResData.status) {
                let orderData = JSON.parse(JSON.stringify(orderResData.data))
                if (orderData.is_return == '1') {
                    if (orderData.sap_status == 'Failed') {
                        sapDataRes = await dbConnection.sapLog.findOne({ where: { return_order_name: orderData.return_order_name, is_return: "1" } })
                        if (sapDataRes && sapDataRes?.sap_delivery_number != "" && sapDataRes?.sap_delivery_number != null) {
                            delhiveryController.sapDelivery(orderData.order_name)
                        } else {
                            sapController.returnSaleOrderSap(orderData)
                        }
                    } else if (orderData.delhivery_status == 'Failed') {
                        delhiveryController.reverseOrderDelhivery(orderData.order_name)
                    } else {
                        delhiveryController.sapDelivery(orderData.order_name)
                    }
                } else if (orderData.is_cancel == '1') {
                    if (orderData.sap_status == 'Pushed') {
                        delhiveryController.sapDelivery(orderData.order_name)
                    } else if (orderData.delhivery_status == 'Failed') {
                        if (orderData.waybill_number) {
                            delhiveryController.cancelOrderDelhivery(orderData.waybill_number, orderData);
                        }
                    } else if (orderData.sap_status == 'Failed') {
                        delhiveryController.cancelOrder(orderData.order_name, true)
                    } else {
                        console.log("pushOrderOnDelhiveryAndSap in cancel else")
                    }
                } else if ((orderData.is_cancel == '0' && orderData.is_return == '0')) {
                    if (orderData.sap_status == 'Pending' || orderData.sap_status == 'Failed') {
                        //call create sales order function
                        //call delhivery(3PL) function api
                        //call sap delivery note function
                        // let isSkuAvailable = await dbConnection.childSku.findOne({ where: { child_sku: orderData.sku } })
                        // if (isSkuAvailable) {
                        //     delhiveryController.pushOrderDelhiveryAndSap(orderData, isSkuAvailable.dataValues.cfa)
                        // } else {
                        await delhiveryController.pushOrderDelhiveryAndSap(orderData, plantCode)
                        //}
                    } else if (orderData.sap_status == 'Pushed') {
                        //so number created
                        if (orderData.delhivery_status != 'Pushed') {
                            const orderAddresses = orderData.orderAddresses.length > 0 ? orderData.orderAddresses[0] : null
                            const checkLogisticPartner = await zippeeController.checkZippeeOrder(orderData.sku, orderAddresses.zip_code, plantCode);
                            //create delhivery(3PL) order
                            //sap delivery note function
                            if(checkLogisticPartner?.zippee){
                                zippeeController.pushOrderOnZippee(orderData)
                            } else{
                                delhiveryController.pushOrderOnDelhivery(orderData)
                            }
                        } else {
                            //call delivery note function
                            delhiveryController.sapDelivery(orderData.order_name)
                        }
                    }
                } else {
                    console.log("pushOrderOnDelhiveryAndSap Order not found")
                }
            }
        }
    } catch (err) {
        console.log("Push order cron Error==>", err)
    }
}

exports.getorderSplitDataByNameExport = (orderName) => {
    return new Promise(async (resolve, reject) => {
        try {
            let getOrderDataByName = await dbConnection.orderItemSplit.findOne({
                where: { order_name: orderName },
                // raw: true,
                // nest: true,
            })

            if (getOrderDataByName) {
                let refundData = await dbConnection.order_refund.findOne({ where: { order_name: getOrderDataByName.dataValues.order_name } });
                let orderCustomer = await dbConnection.orderCustomer.findOne({ where: { order_id: getOrderDataByName.dataValues.shopify_order_id } })
                let orderAddress = await dbConnection.orderAddress.findOne({ where: { order_id: getOrderDataByName.dataValues.shopify_order_id } })
                let data = await dbConnection.delhiveryLog.findAll({ where: { order_name: getOrderDataByName.dataValues.order_name } })
                let orderData = getOrderDataByName.dataValues
                let orderCustomerArr = []
                let orderAddressArr = []
                let delhiveryLogData = data.length > 0 ? JSON.parse(JSON.stringify(data)) : []
                orderCustomer ? orderCustomerArr.push(orderCustomer.dataValues) : []
                orderAddress ? orderAddressArr.push(orderAddress.dataValues) : []
                delhiveryLogData ? delhiveryLogData : []
                refundData = refundData ? refundData.dataValues : ''
                orderData.orderCustomers = orderCustomerArr
                orderData.orderAddresses = orderAddressArr
                orderData.delhiveryLogs = delhiveryLogData
                orderData.refundData = refundData
                resolve({ status: true, data: getOrderDataByName })
            } else {
                resolve({ status: false })
            }
        } catch (err) {
            console.log('Error getorderSplitDataByNameExport=====>', err)
        }
    })
}
exports.getorderSplitDataByName = (orderName) => {
    return new Promise(async (resolve, reject) => {
        try {
            let getOrderDataByName = await dbConnection.orderItemSplit.findOne({
                where: { order_name: orderName },
                // raw: true,
                // nest: true,
            })
            if (getOrderDataByName) {
                let orderCustomer = await dbConnection.orderCustomer.findOne({ where: { order_id: getOrderDataByName.dataValues.shopify_order_id } })
                let orderAddress = await dbConnection.orderAddress.findOne({ where: { order_id: getOrderDataByName.dataValues.shopify_order_id } })
                let orderData = getOrderDataByName.dataValues
                let orderCustomerArr = []
                let orderAddressArr = []
                orderCustomer ? orderCustomerArr.push(orderCustomer.dataValues) : []
                orderAddress ? orderAddressArr.push(orderAddress.dataValues) : []
                orderData.orderCustomers = orderCustomerArr
                orderData.orderAddresses = orderAddressArr
                resolve({ status: true, data: getOrderDataByName })
            } else {
                resolve({ status: false })
            }
        } catch (err) {
            console.log('Error getorderSplitDataByName=====>', err)
        }
    })
}

exports.cancelOrderSapDelhivery = async (req, res) => {
    try {
        let orderName = req.body.orderName
        await dbConnection.orderCancellation.create({ order_name: orderName, status_type: "order_cancel", return_order_name: orderName, middleware_status: "Customer Cancelation" }).then(async (dbRes) => {
            if (dbRes.dataValues.id) {
                let orderResData = await this.getorderSplitDataByName(orderName)
                if (orderResData.status) {
                    let orderData = JSON.parse(JSON.stringify(orderResData.data))
                    if (orderData.sap_status == 'Pushed') {
                        let sapDeliveryResp = await delhiveryController.sapDelivery(orderName);
                        let orderResData = await this.getorderSplitDataByName(orderName)
                        orderData = JSON.parse(JSON.stringify(orderResData.data))
                    }
                    if (orderData.financial_status == "paid" && orderData.gateway != "Cash on Delivery (COD)") {
                        await refundController.checkingForRefund(orderData);
                        // emailController.sendEmailToSymphony(orderName, 'Cancel')
                    }
                    delhiveryController.cancelOrder(orderName)
                }
            }
            res.status(CONFIG.status.SUCCESS).send({ message: CONFIG.msg.SUCCESS, status: CONFIG.status.SUCCESS })
        }).catch((e) => {
            console.log("Error =>>>", e);
            res.status(CONFIG.status.SUCCESS).send({ message: CONFIG.msg.SUCCESS, status: CONFIG.status.SUCCESS })
        })
    } catch (err) {
        console.log('Error cancelOrderSapDelhivery=====>', err)
    }
}

exports.exportOrderData = async (req, res) => {
    let successMessage = response.responseMessage(CONFIG.status.FAILED, CONFIG.msg.FAILED);
    let buffer;

    try {
        res.status(CONFIG.status.SUCCESS).send(CONFIG.msg.SUCCESS);

        let orderDatas
        if (req.body.isAllResource) {
            orderDatas = await filterOrderData(req.body.filters, req.query);
        } else {
            orderDatas = await orderItemSplit.findAll({
                where: { order_name: req.body.order_ids },
                include: [
                    {
                        model: dbConnection.order_refund, as: "orderRefund"
                    },
                    {
                        model: dbConnection.orderCustomer, as: 'orderCustomers',
                        required: true
                    }
                ],
            })
        }

        orderDatas = JSON.parse(JSON.stringify(orderDatas))
        let finalData = [];
        for (let data of orderDatas) {
            const obj = {
                returnWayBillNo: null,
                WayBillNo: null,
                returnSapConfirmationNo: null,
                sapConfirmationNo: null,
                pickUpDate: null,
                returnpickUpDate: null,
                orderDeliveryDateWh: null,
                serviceRequestLogObj: null,
                sapOne: null,
                sapNew: null,
            };

            // Delivery
            let deliveryLogs = await dbConnection.delhiveryLog.findAll({ where: { order_name: data.order_name } });
            if (deliveryLogs.length > 0) {
                let WayBillNodelhiveryArr = deliveryLogs.filter(item => item.is_return === '0' && item.is_cancel === '0' && item.reference_number === data.order_name);
                obj.WayBillNo = WayBillNodelhiveryArr.length > 0 ? WayBillNodelhiveryArr[0].waybill_number : '';
                obj.pickUpDate = WayBillNodelhiveryArr.length > 0 ? WayBillNodelhiveryArr[0].pickup_date : '';
                let delhiveryArr = []
                if (data.is_cancel == "1") {
                    delhiveryArr = deliveryLogs.filter(item => item.is_cancel === '1');
                } else {
                    delhiveryArr = deliveryLogs.filter(item => item.is_return === '1' && item.return_order_name === data.return_order_name);
                }
                obj.orderDeliveryDateWh = delhiveryArr.length > 0 ? delhiveryArr[0].rto_delivered_date : '';
                obj.returnWayBillNo = delhiveryArr.length > 0 ? delhiveryArr[0].waybill_number : '';
                obj.returnpickUpDate = delhiveryArr.length > 0 ? delhiveryArr[0].pickup_date : '';
            }

            // SAP
            let sapLogs = await dbConnection.sapLog.findAll({ where: { order_name: data.order_name } });
            if (sapLogs.length > 0) {
                let sapReturnArr = []
                if (data.is_cancel == "1") {
                    sapReturnArr = sapLogs.filter(item => item.is_cancel === '1');
                    let returnCreatedDate = sapReturnArr.length > 0 ? sapReturnArr[0].return_created_date : null
                    obj.orderDeliveryDateWh = returnCreatedDate && (returnCreatedDate != null) ? returnCreatedDate : obj.orderDeliveryDateWh;
                } else {
                    sapReturnArr = sapLogs.filter(item => item.is_return === '1' && item.return_order_name === data.return_order_name);
                }
                let sapArr = sapLogs.filter(item => item.is_return === '0' && item.is_cancel === '0' && item.order_name === data.order_name);
                obj.returnSapConfirmationNo = sapReturnArr.length > 0 ? sapReturnArr[0].sap_confirmation_id : '';
                obj.sapConfirmationNo = sapArr.length > 0 ? sapArr[0].sap_confirmation_id : '';

                obj.sapOne = JSON.parse(JSON.stringify(sapArr))[0]
                obj.sapNew = JSON.parse(JSON.stringify(sapReturnArr))[0]
            }

            // serviceRequestLog
            let serviceRequestLog = await dbConnection.serviceRequestLog.findAll({ where: { order_name: data.order_name }, order: [['id', 'DESC']] });
            if (serviceRequestLog.length > 0) {
                obj.serviceRequestLogObj = JSON.parse(JSON.stringify(serviceRequestLog))[0]
            }

            let dataObj = await exportOrderDataObject(data, obj);
            finalData.push(dataObj);
        }

        const mainSettings = {
            writeOptions: {
                type: 'buffer',
                bookType: 'xlsx',
            },
        };

        let data = [
            {
                sheet: 'Order Data',
                columns: [
                    { label: 'Middleware Order Id', value: row => (row.middlewareOrderId ? row.middlewareOrderId : "") },
                    { label: 'Shopify Order Id', value: row => (row.shopifyOrderId ? row.shopifyOrderId : "") },
                    { label: 'Order Date', value: row => (row.orderDate ? row.orderDate : "") },
                    { label: 'Customer Name', value: row => (row.customerName ? row.customerName : "") },
                    { label: 'AWB Number', value: row => (row.awbNumber ? row.awbNumber : "") },
                    { label: 'Product Title', value: row => (row.productTitle ? row.productTitle : "") },
                    { label: 'Quantity', value: row => (row.qty ? row.qty : "") },
                    { label: 'Order Amount', value: row => (row.amount ? row.amount : "") },
                    { label: 'Order Status', value: row => (row.middlewareStatus ? row.middlewareStatus : "") },
                    { label: 'Plant Code', value: row => (row.plantCode ? row.plantCode : "") },
                    { label: 'Order Pickup Date WH', value: row => (row.orderPickUpDateWh ? row.orderPickUpDateWh : "") },
                    { label: 'Order Delivery Date Customer', value: row => (row.orderDeliveryDate ? row.orderDeliveryDate : "") },
                    { label: 'Order Cancellation Date', value: row => (row.orderCancellationDate ? row.orderCancellationDate : "") },
                    { label: 'Order Pickup Date Customer', value: row => (row.returnOrderPickupDate ? row.returnOrderPickupDate : "") },
                    { label: 'Order Delivery Date WH', value: row => (row.orderDeliveryDateWH ? row.orderDeliveryDateWH : "") },
                    { label: 'Service Call Id', value: row => (row.serviceCallId ? row.serviceCallId : "") },
                    { label: 'Service Response Id', value: row => (row.serviceResponseId ? row.serviceResponseId : "") },
                    { label: 'Service Message', value: row => (row.serviceMessage ? row.serviceMessage : "") },
                    { label: 'Warranty Code', value: row => (row.warrantyCode ? row.warrantyCode : "") },
                    { label: 'Order Invoiced Date', value: row => (row.orderInvoicedDate ? row.orderInvoicedDate : "") },

                    //{ label: 'Shipment Tracking Status', value: row => (row.delhiveryTrackingStatus ? row.delhiveryTrackingStatus : "") },
                    //{ label: 'Replacement Reason', value: row => (row.replacementReason ? row.replacementReason : "") },
                    { label: 'CRM Ticket Number', value: row => (row.crmTicketNumber ? row.crmTicketNumber : "") },
                    { label: 'CRM Ticket Status', value: row => (row.crmTicketStatus ? row.crmTicketStatus : "") },
                    { label: 'Return AWB', value: row => (row.returnAWB ? row.returnAWB : "") },
                    { label: 'SAP Invoice Date', value: row => (row.sapInvoiceDate ? row.sapInvoiceDate : "") },
                    { label: 'Order Cancel Date', value: row => (row.orderCancelDate ? row.orderCancelDate : "") },
                    { label: 'SAP Delivery Number', value: row => (row.sapDeliveryNo ? row.sapDeliveryNo : "") },
                    { label: 'SAP Billing Number', value: row => (row.sapBillingNo ? row.sapBillingNo : "") },
                    { label: 'SAP Order Number', value: row => (row.sapOrderNo ? row.sapOrderNo : "") },
                    { label: 'Return SAP Delivery Number', value: row => (row.returnsapDeliveryNo ? row.returnsapDeliveryNo : "") },
                    { label: 'Return SAP Billing Number', value: row => (row.returnsapBillingNo ? row.returnsapBillingNo : "") },
                    { label: 'Return SAP Order Number', value: row => (row.returnsapOrderNo ? row.returnsapOrderNo : "") },
                    { label: 'SAP Confirmation Number', value: row => (row.sapConfirmationNo ? row.sapConfirmationNo : "") },
                    { label: 'SAP Return Confirmation Number', value: row => (row.returnSapConfirmationNo ? row.returnSapConfirmationNo : "") },
                    { label: 'SAP CQ Date', value: row => (row.returnInvoicCQDate ? row.returnInvoicCQDate : "") },
                    { label: 'Refund Id', value: row => (row.refundId ? row.refundId : "") },
                    { label: 'Pay Id', value: row => (row.checkoutId ? row.checkoutId : "") },
                    { label: 'Order Returned Request Date', value: row => (row.returnRequestedDate ? row.returnRequestedDate : "") },
                    { label: 'Order Returned Approved Date', value: row => (row.returnApprovedDate ? row.returnApprovedDate : "") },
                    { label: 'GST NUMBER', value: row => (row.gstNumber ? row.gstNumber : "") }
                ],
                content: finalData,
            },
        ];

        if (req.body.exportType == "refund") {
            data = [
                {
                    sheet: 'Order Data',
                    columns: [
                        { label: 'Middleware Order Id', value: row => (row.middlewareOrderId ? row.middlewareOrderId : "") },
                        { label: 'Customer Name', value: row => (row.customerName ? row.customerName : "") },
                        { label: 'GT Number', value: row => (row.sapBillingNo ? row.sapBillingNo : "") },
                        { label: 'CQ Number', value: row => (row.returnInvoicCQDate ? row.returnInvoicCQDate : "") },
                        { label: 'Refund Id', value: "" },
                        { label: 'Refund Date', value: "" },
                    ],
                    content: finalData,
                },
            ];
        }

        buffer = xlsx(data, mainSettings);
        const fileName = 'orderData.xlsx';
        let filePath = path.join(__dirname, '../uploads', fileName);
        fs.writeFileSync(filePath, buffer);

        const fileContent = fs.readFileSync(filePath);
        var fileData = {
            Bucket: CONFIG.aws.bucketName,
            Key: 'exportOrderDataFile/' + fileName,
            Body: fileContent,
            ContentEncoding: 'base64',
            ACL: 'public-read',
            ContentType: 'application/xlsx',
        };

        const awsData = await s3.upload(fileData).promise();
        fs.unlinkSync(filePath);

        if (awsData) {
            let exportFileUrl = awsData.Location;
            console.log('exportFileUrl---->', exportFileUrl);
            await emailController.sendExportDataEmail(fileName, exportFileUrl, CONFIG.email.exportto, null, 'Exported Order Data');
        }
    } catch (e) {
        console.error('Error:', e);
        successMessage.status = CONFIG.status.ERROR;
        successMessage.message = CONFIG.msg.ERROR;
        res.status(CONFIG.status.ERROR).send(CONFIG.msg.ERROR);
    }
};

let exportOrderDataObject = async (data, obj) => {
    try {
        dataObj = {
            middlewareOrderId: data.order_name,
            shopifyOrderId: data.shopify_order_id,
            orderDate: moment.utc(data.order_created_at).tz('Asia/Kolkata').format('L'),
            customerName: data.orderCustomers.first_name + ' ' + data.orderCustomers.last_name,
            awbNumber: obj.WayBillNo,
            productTitle: data.product_title,
            qty: data.quantity,
            amount: data.order_amount,
            middlewareStatus: data.middleware_status,
            delhiveryTrackingStatus: data.shipment_status,
            replacementReason: data.replacement_reason,
            crmTicketNumber: data.crm_ticket_number,
            crmTicketStatus: data.crm_ticket_status,
            warrantyCode: data.warranty_code,
            plantCode: data.plant_code,
            returnAWB: obj.returnWayBillNo,
            sapInvoiceDate: data.sap_invoice_date,
            orderCancelDate: data.order_cancel_date,
            sapDeliveryNo: obj.sapOne?.sap_delivery_number,
            sapBillingNo: obj.sapOne?.sap_billing_number,
            sapOrderNo: obj.sapOne?.sap_order_number,
            returnsapDeliveryNo: obj.sapNew?.sap_delivery_number,
            returnsapBillingNo: obj.sapNew?.sap_billing_number,
            returnsapOrderNo: obj.sapNew?.sap_order_number,
            returnInvoicCQDate: obj.sapNew?.cn_created_date ? moment.utc(obj.sapNew?.cn_created_date).tz('Asia/Kolkata').format('L') : null,
            settlementId: data.settlement_id,
            razorpayTransferId: data.razorpay_transfer_id,
            refundStatus: data.orderRefund.refund_status,
            refundId: data.orderRefund.refund_id,
            checkoutId: data.checkout_id,
            sapConfirmationNo: obj.sapConfirmationNo,
            returnSapConfirmationNo: obj.returnSapConfirmationNo,
            serviceCallId: obj.serviceRequestLogObj?.call_id,
            serviceResponseId: obj.serviceRequestLogObj?.response_id,
            serviceMessage: obj.serviceRequestLogObj?.message_description,
            failedReason: data.failed_reason,
            gstNumber: data.gstin != 'null' ? data.gstin : null,
            orderPickUpDateWh: obj.pickUpDate ? moment.utc(obj.pickUpDate).tz('Asia/Kolkata').format('L') : null,
            orderInvoicedDate: data.sap_invoice_date ? moment.utc(data.sap_invoice_date).tz('Asia/Kolkata').format('L') : null,
            orderDeliveryDate: data.delivered_at ? moment.utc(data.delivered_at).tz('Asia/Kolkata').format('L') : null,
            orderCancellationDate: data.order_cancel_date ? moment.utc(data.order_cancel_date).tz('Asia/Kolkata').format('L') : null,
            returnOrderPickupDate: obj.returnpickUpDate ? moment.utc(obj.returnpickUpDate).tz('Asia/Kolkata').format('L') : null,
            returnRequestedDate: data.return_request_date ? moment.utc(data.return_request_date).tz('Asia/Kolkata').format('L') : null,
            returnApprovedDate: data.return_approved_date ? moment.utc(data.return_approved_date).tz('Asia/Kolkata').format('L') : null,
            orderDeliveryDateWH: obj.orderDeliveryDateWh ? moment.utc(obj.orderDeliveryDateWh).tz('Asia/Kolkata').format('L') : null,
        }
        return dataObj
    } catch (err) {
        console.log('errrr exportOrderDataObject=====>', err)
        return
    }
}


exports.callCloudApi = (orderData) => {
    return new Promise(async (resolve, reject) => {
        try {
            const options = {
                headers: {
                    'Content-Type': 'application/json'
                }
            }
            let logObj = {}
            logObj.model = orderData.sku
            logObj.productSerialNumber = orderData.product_serial_number ? orderData.product_serial_number : ''
            for (let customerValue of orderData.orderCustomers) {
                logObj.customerName = customerValue.first_name + " " + customerValue.last_name
                logObj.mobileNumber = customerValue.phone_number.trim()
                logObj.email = customerValue.customer_email
            }
            for (let addressValue of orderData.orderAddresses) {
                logObj.address = addressValue.address1
                logObj.pinCode = addressValue.zip_code
                let codeData = await findCityAndStateCodeFromCRMTable(addressValue.zip_code)
                logObj.stateCode = codeData?.obj?.state_code || null
                logObj.cityCode = codeData?.obj?.city_code || null
            }
            let logData = await getLogData(logObj)

            let apiRes = await axios.post(CONFIG.crm.log_url, logData.data, options)

            let helperObj = {
                shopify_order_id: orderData.shopify_order_id,
                shopify_order_name: orderData.shopify_order_name,
                order_name: orderData.order_name,
                platform_type: "Installation Request First Log",
                request_data: JSON.stringify(logData.data),
                response_status_code: "200",
                response_data: apiRes?.data ? JSON.stringify(apiRes.data) : 'warranty error'
            }
            await orderHistoryHelper.insertOrderHistory(helperObj)

            if (apiRes.data.wSState != 0) {
                const customerCode = apiRes.data.userProfile.customerCode
                logObj.customerCode = customerCode

                let tableData
                if (logObj.stateCode && logObj.cityCode) {
                    tableData = await dbConnection.cityStatePin.findOne({ where: { city_code: logObj.cityCode, state_code: logObj.stateCode } })
                }
                let warrantyBodyObj = {
                    "serviceType": "submitCallRegistration",
                    "userId": CONFIG.crm.userId,
                    "token": CONFIG.crm.token,
                    "submitCallRegistration": {
                        "distributorCode": CONFIG.crm.distributorCode,
                        "dealerCode": CONFIG.crm.dealerCode,
                        "customerType": CONFIG.crm.customerType,
                        "mobileNumber": customerCode ? customerCode : logObj.mobileNumber,
                        "customerName": logObj.customerName ? logObj.customerName : "",
                        "address": logObj.address ? logObj.address : "0",
                        "area": tableData ? tableData.area : "0",
                        "cityCode": logObj.cityCode ? logObj.cityCode : 0,
                        "stateCode": logObj.stateCode ? logObj.stateCode : 0,
                        "pinCode": logObj.pinCode ? logObj.pinCode : 0,
                        "brandCode": CONFIG.crm.brandCode,
                        "productCategoryCode": checkSKU(logObj.model) ? 3 : CONFIG.crm.productCategoryCode,
                        "productCode": checkSKU(logObj.model) ? 7 : CONFIG.crm.productCode,
                        "model": logObj.model ? logObj.model : " ",
                        "productSerialNumber": logObj.productSerialNumber ? logObj.productSerialNumber : "",
                        "dateOfPurchase": "",
                        "quantity": 1,
                        "serviceTypeCode": checkSKU(logObj.model) ? 3 : 1,
                        "customerRemarks": logObj.customerRemarks ? logObj.customerRemarks : " ",
                        "location": " ",
                        "email": logObj.email ? logObj.email : " ",
                        "techRemarks": " ",
                        "dealerDesc": " ",
                        "cityDesc": CONFIG.crm.cityDesc
                    },
                    "version": CONFIG.crm.version
                }
                let warrantyRes = await axios.post(CONFIG.crm.warranty_url, warrantyBodyObj, options)
                let crmTicketObj = {
                    crm_ticket_number: warrantyRes.data?.callId,
                    crm_ticket_status: "open"
                }
                await dbConnection.orderItemSplit.update(crmTicketObj, { where: { order_name: orderData.order_name } })
                let helperObj = {
                    shopify_order_id: orderData.shopify_order_id,
                    shopify_order_name: orderData.shopify_order_name,
                    order_name: orderData.order_name,
                    platform_type: "Installation Request",
                    request_data: JSON.stringify(warrantyBodyObj),
                    response_status_code: "200",
                    response_data: warrantyRes.data ? JSON.stringify(warrantyRes.data) : 'warranty error'
                }
                await orderHistoryHelper.insertOrderHistory(helperObj)
                resolve({ status: true, response: warrantyRes.data })
            } else {
                resolve({ status: false })
            }
        } catch (err) {
            console.log('Error callCloudApi=====>', err)
        }
    })
}

// Function to check if the SKU exists in the array
function checkSKU(sku) {
    const skus = ["AGYST007", "AGYST008", "AGYST009", "AGYST004", "AGYST005", "AGYST006", "AGYST001", "AGYST002", "AGYST003"];
    return skus.includes(sku);
}

exports.refundOrderData = async (req, res) => {
    try {
        let orderId = [];
        if (req.body.isAllResource) {
            orderId = await getOrderIds(req.body.filters);
        } else {
            orderId = req.body.order_ids;
        }
        for (let i = 0; i < orderId.length; i++) {
            let orderResData = await dbConnection.order_refund.findOne({ where: { order_name: orderId[i] } })
            if (orderResData && orderResData.dataValues.is_refund == '0' && orderResData.dataValues.refund_status == 'pending') {
                let orderData = JSON.parse(JSON.stringify(orderResData));
                let multipleRefund = await razorpayController.fetchMultipleRefunds(orderData.payment_id);
                if (multipleRefund.status) {
                    let refundArr = multipleRefund.data.items;
                    let refundData = await refundArr.filter(x => x.receipt == orderData.order_name);
                    if (refundData.length == 0) {
                        await razorpayController.createRefund(orderResData.dataValues);
                    } else {
                        let refundObj = {};
                        refundObj.refund_status = refundData[0].status
                        refundObj.is_refund = '2'
                        refundObj.refund__id = refundData[0].id
                        await dbConnection.order_refund.update(refundObj, { where: { id: orderResData.dataValues.id } });
                    }
                }
            }
        }
        res.status(CONFIG.status.SUCCESS).send(CONFIG.msg.SUCCESS);
    } catch (error) {
        res.status(CONFIG.status.ERROR).send(CONFIG.msg.ERROR);
    }
}

exports.refundOrderUpdate = (orderName) => {
    return new Promise(async (resolve, reject) => {
        try {
            let orderResData = await dbConnection.order_refund.findOne({ where: { order_name: orderName } })
            if (orderResData && orderResData.dataValues.is_refund == '0' && orderResData.dataValues.refund_status == 'pending') {
                let orderData = JSON.parse(JSON.stringify(orderResData));
                let multipleRefund = await razorpayController.fetchMultipleRefunds(orderData.payment_id);
                if (multipleRefund.status) {
                    let refundArr = multipleRefund.data.items;
                    let refundData = await refundArr.filter(x => x.receipt == orderName);
                    if (refundData.length == 0) {
                        await razorpayController.createRefund(orderResData.dataValues);
                    } else {
                        let refundObj = {};
                        refundObj.refund_status = refundData[0].status
                        refundObj.is_refund = '2'
                        refundObj.refund__id = refundData[0].id
                        await dbConnection.order_refund.update(refundObj, { where: { id: orderResData.dataValues.id } });
                    }
                }
            }
            resolve({ status: true })
        } catch (err) {
            console.log('Error refundOrderUpdate=====>', err)
        }
    })
}

exports.editOrderDetails = async (req, res) => {
    try {
        let orderData = req.body.data
        let orderStatus = req.body.orderStatus
        let dataObj = {}
        orderData.waybillNumber && orderData.waybillNumber.length > 0 ? dataObj.waybill_number = orderData.waybillNumber : null
        orderData.sapOrderNumber && orderData.sapOrderNumber.length > 0 ? dataObj.sap_order_number = orderData.sapOrderNumber : null
        orderData.sapDeliveryNumber && orderData.sapDeliveryNumber.length > 0 ? dataObj.sap_delivery_number = orderData.sapDeliveryNumber : null
        orderData.sapInvoiceNumber && orderData.sapInvoiceNumber.length > 0 ? dataObj.sap_billing_number = orderData.sapInvoiceNumber : null
        if (orderData.cfaNumber && orderData.cfaNumber.length > 0) {
            let planLocationResponse = await dbConnection.cfaPlantLocation.findOne({ where: { plant_code: orderData.cfaNumber } })
            if (!planLocationResponse) throw new Error("Plant location is not found.")
            dataObj.plant_code = orderData.cfaNumber
            dataObj.cfa_name = planLocationResponse.plant_name
        }
        let orderSplitData = await dbConnection.orderItemSplit.findOne({ where: { order_name: orderData.orderName } })
        if (orderSplitData && Object.keys(dataObj).length > 0) {
            let createTableData = {
                store_id: orderSplitData.store_id,
                order_id: orderSplitData.shopify_order_id,
                line_item_id: orderSplitData.line_item_id,
                order_name: orderData.orderName,
                shopify_order_name: orderSplitData.shopify_order_name,
                is_cancel: '0',
                is_return: '0',
                ...dataObj
            }
            let options = {
                order_name: orderData.orderName,
                is_cancel: '0',
                is_return: '0'
            }
            if (orderStatus == 'cancel') {
                options.is_cancel = '1'
                createTableData.is_cancel = '1'
            } else if (orderStatus == 'replaced') {
                options.is_return = '1'
                createTableData.is_return = '1'
            }
            if (dataObj.waybill_number != null) {
                let isDelhiveryLogUpdate = await dbConnection.delhiveryLog.update(dataObj, { where: options })
                isDelhiveryLogUpdate[0] == 0 ? await dbConnection.delhiveryLog.create(createTableData) : null
            }
            if (dataObj.waybill_number == null) {
                delete dataObj.waybill_number
            }
            await dbConnection.orderItemSplit.update(dataObj, { where: { order_name: orderData.orderName } })
            let isSapLogUpdate = await dbConnection.sapLog.update(dataObj, { where: options })
            isSapLogUpdate[0] == 0 ? await dbConnection.sapLog.create(createTableData) : null
        }
        res.status(CONFIG.status.SUCCESS).send(CONFIG.msg.UPDATE)
    } catch (error) {
        console.log('error', error)
        res.status(CONFIG.status.ERROR).send(CONFIG.msg.ERROR);
    }
}

exports.customerOrder = async (req, res) => {
    try {
        const { page, per_page, customerId } = req.query;
        if (!customerId || !page || !per_page) {
            return res.status(CONFIG.status.FAILED).send({ err: CONFIG.msg.PARAMETER });
        }
        const { limit, offset } = helper.getPagination(page, per_page);
        const customerOrderData = await orderItemSplit.findAndCountAll({
            where: { shopify_customer_id: customerId },
            order: [
                ['order_created_at', 'DESC']
            ],
            offset: offset,
            limit: limit,
            attributes: ['id', 'is_replaceable', 'is_return', 'is_cancel', 'is_pickup', 'shopify_product_id', 'shopify_order_id', 'shopify_variant_id', 'order_amount', 'product_title', 'sku', 'order_created_at', 'order_name', 'discount', 'shopify_order_name', 'financial_status', 'shipment_status', 'product_title', 'sku', 'quantity', 'invoice_url', 'order_status', 'is_extended_warranty'],
            raw: true,
            nest: true,
        })
        if (customerOrderData.rows.length == 0) {
            return res.status(CONFIG.status.SUCCESS).send({ err: CONFIG.msg.NO_DATA });
        }
        let customerArray = [];
        for (let obj of customerOrderData.rows) {
            let is_cancel = obj.is_cancel
            let is_return = obj.is_return
            let product = await dbConnection.product.findOne({ where: { shopify_product_id: obj.shopify_product_id }, raw: true })
            let fulfillmentItem = await dbConnection.fulfillmentItem.findOne({
                where: { order_id: obj.id },
                attributes: ['tracking_url', 'tracking_company', 'tracking_number'],
                raw: true
            })
            if (product) {
                let productImage = await dbConnection.productImage.findOne({ where: { product_id: product.id }, raw: true })
                obj.product_image = productImage ? productImage.image_url : ''
            }

            //get warranty certificate url
            if (obj.is_extended_warranty === '1') {
                let extendedWarrantyData = await dbConnection.extendedWarrantyOrder.findOne({ where: { order_item_split_id: obj.id }, raw: true })
                obj.warranty_certificate_url = extendedWarrantyData?.warranty_certificate_url ? extendedWarrantyData?.warranty_certificate_url : null;
            }

            let orderAddress = await dbConnection.orderAddress.findOne({ where: { order_id: obj.shopify_order_id }, raw: true })
            obj.order_address = orderAddress ? orderAddress : {}
            obj.is_return = obj.is_replaceable == '0' ? false : true
            obj.is_cancel = obj.is_pickup == '0' ? true : false
            obj.is_pickup = obj.is_pickup == '1'
            obj.fulfilment_status = fulfillmentItem ? 'Fulfilled' : 'Unfulfilled'
            obj.tracking_url = fulfillmentItem ? fulfillmentItem.tracking_url : ''
            if (is_cancel == "1") {
                obj.shipment_status = "Cancel"
            } else if (is_return == "1") {
                obj.shipment_status = "Return"
            } else {
                if (obj.order_status == 'Completed') {
                    obj.shipment_status = "Completed"
                } else {
                    obj.shipment_status = "Pending"
                }
            }
            obj.order_created_at = moment.utc(obj.order_created_at).tz('Asia/Kolkata').format('YYYY-MM-DD HH:mm:ss'),
                obj.tracking_company = fulfillmentItem ? fulfillmentItem.tracking_company : ''
            obj.tracking_number = fulfillmentItem ? fulfillmentItem.tracking_number : ''

            let refundOrder = await dbConnection.order_refund.findOne({ where: { order_name: obj.order_name, is_refund: '2' }, raw: true })
            obj.refund_id = refundOrder ? refundOrder.refund_id : '';
            customerArray.push(obj);
        }
        if (customerArray.length > 0) {
            let customerObj = {
                count: customerOrderData.count,
                rows: customerArray
            };
            const response = helper.getPagingData(customerObj, page, limit);
            res.status(CONFIG.status.SUCCESS).send(response);
        } else {
            let orderDataObj = {
                count: 0,
                rows: []
            };
            const response = helper.getPagingData(orderDataObj, page, limit);
            res.status(CONFIG.status.SUCCESS).send(response);
        }
    } catch (error) {
        console.log(error)
        res.status(CONFIG.status.FAILED).send(CONFIG.msg.ERROR);
    }
}

//Import Refund Data

exports.importRefundData = async (req, res) => {
    let successMessage = response.responseMessage(CONFIG.status.FAILED, CONFIG.msg.FAILED)
    let status = 500
    try {
        let shopName = req.query.shop
        if (shopName) {
            const file = xlsxFile.readFile(req.file.path)
            let data = []
            const sheets = file.SheetNames
            for (let i = 0; i < sheets.length; i++) {
                const fileUpload = xlsxFile.utils.sheet_to_json(
                    file.Sheets[file.SheetNames[i]], { header: ["MiddlewareOrderId", "CustomerName", "GTNumber", "CQNumber", "RefundId", "RefundDate"], skipHeader: true })
                fileUpload.forEach((res) => {
                    data.push(res)
                })
            }
            data.splice(0, 1)
            for (let value of data) {
                if (value.RefundId) {
                    console.log('RefundId-----', value.RefundId)
                    console.log('RefundId-----', value.MiddlewareOrderId)
                    let rawDate = value?.RefundDate;
                    let formattedDate = null;

                    if (rawDate) {
                        if (!isNaN(rawDate)) {
                            // Convert Excel serial number to proper date
                            formattedDate = moment("1899-12-30").add(rawDate, "days").format("YYYY-MM-DD");
                        } else if (typeof rawDate === "string") {
                            // Handle string-based dates in multiple formats
                            let parsedDate = moment(rawDate.trim(), ["DD-MM-YYYY", "MM-DD-YYYY", "DD/MM/YYYY", "MM/DD/YYYY"], true);
                            if (parsedDate.isValid()) {
                                formattedDate = parsedDate.format("YYYY-MM-DD");
                            } else {
                                console.log("Invalid date format:", rawDate); // Debugging
                            }
                        }
                    }

                    let dataObj = {
                        refund_status: "REFUNDED",
                        refund_id: value.RefundId,
                        refund_date: formattedDate
                    }
                    await dbConnection.orderItemSplit.update({ middleware_status: "REFUNDED" }, { where: { order_name: value.MiddlewareOrderId } })
                    await dbConnection.order_refund.update(dataObj, { where: { order_name: value.MiddlewareOrderId } })
                }
            }
            unlinkAsync(req.file.path)
            successMessage.status = CONFIG.status.SUCCESS
            successMessage.message = CONFIG.msg.UPLOAD
            res.status(CONFIG.status.SUCCESS).send(successMessage)

        } else {
            successMessage.status = CONFIG.status.FAILED
            successMessage.message = CONFIG.msg.PARAMETER
            res.status(CONFIG.status.SUCCESS).send(successMessage)
        }
    } catch (err) {
        console.log("error--------", err);
        successMessage.status = CONFIG.status.ERROR
        successMessage.message = CONFIG.msg.ERROR
        res.status(status).send(successMessage)
    }
}

exports.sendMailPendingCQ = async () => {
    try {
        const currentDate = new Date(Date.now() - 24 * 60 * 60 * 1000);
        const formattedDate = currentDate.toISOString().slice(0, 19).replace("T", " ");

        let orderNamesResult = await dbConnection.sequelize.query(
            `SELECT order_name FROM order_item_splits WHERE (is_return ='1' OR is_cancel ='1')
        AND (middleware_status ='CANCELLED' OR middleware_status='RETURNED TO WAREHOUSE' OR middleware_status='RTO RETURNED TO WAREHOUSE') 
        ORDER BY updatedAt DESC`,
            { type: QueryTypes.SELECT }
        );
        let orderNamesArray = orderNamesResult.map((row) => row.order_name);

        const SapOrderData = await dbConnection.sequelize.query(
            `SELECT * FROM symphony.sap_orders_logs WHERE order_name in ('${orderNamesArray.join("','")}') and (is_return = "1" or is_cancel = "1" ) 
        and sap_billing_number is null and createdAt < '${formattedDate}'`,
            { type: QueryTypes.SELECT }
        );
        let finalData = [];

        for (let orderData of SapOrderData) {
            const orderDataHistories = await dbConnection.sequelize.query(
                `SELECT request_data, response_data FROM symphony.order_log_histories 
          WHERE order_name ='${orderData.order_name}' AND (platform_type = 'SAP-Cancel-sapBillingNumber' OR platform_type = 'SAP-sapBillingNumber')
          ORDER BY createdAt DESC 
          LIMIT 1`,
                { type: QueryTypes.SELECT }
            );
            const dataObj = {
                order_name: orderData.order_name,
                createdAt: orderData.createdAt,
                sapResponse: orderDataHistories?.[0]?.response_data || null,
                sapRequest: orderDataHistories?.[0]?.request_data || null
            };
            finalData.push(dataObj);
        }

        const mainsettings = {
            writeOptions: {
                type: "buffer",
                bookType: "xlsx",
            },
        };

        let data = [
            {
                sheet: "CQ Order Data",
                columns: [
                    { label: "Order Number", value: (row) => (row.order_name ? row.order_name : "") },
                    { label: "Return Created Date", value: (row) => (row.createdAt ? moment.utc(row.createdAt).tz('Asia/Kolkata').format('YYYY-MM-DD HH:mm:ss') : "") },
                    { label: "Sap Response", value: (row) => (row.sapResponse ? row.sapResponse : "") },
                    { label: "Sap Request", value: (row) => (row.sapRequest ? row.sapRequest : "") },
                ],
                content: finalData,
            },
        ];

        const buffer = xlsx(data, mainsettings);
        const fileName = "CqPendingOrder.xlsx";
        const filePath = path.join(__dirname, "../uploads", fileName);
        fs.writeFileSync(filePath, buffer);
        const fileContent = fs.readFileSync(filePath);

        const fileData = {
            Bucket: CONFIG.aws.bucketName,
            Key: "exportOrderDataFile/" + fileName,
            Body: fileContent,
            ContentEncoding: "base64",
            ACL: "public-read",
            ContentType: "application/xlsx",
        };

        const awsData = await s3.upload(fileData).promise();
        fs.unlinkSync(filePath);

        if (awsData) {
            const exportFileUrl = awsData.Location;
            await emailController.sendExportDataEmail(
                fileName,
                exportFileUrl,
                CONFIG.email.cqsendmail,
                null,
                "Pending CQ Order Data"
            );
        }
    } catch (error) {
        console.error("Error sendMailPendingCQ:", error);
    }
};