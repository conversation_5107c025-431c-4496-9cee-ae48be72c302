module.exports = app => {
    const delhiveryController = require("../controllers/delhivery-controller.js");
    var router = require("express").Router();
  
    // Login API Routes
    // router.post("/reverse/delhivery", delhiveryController.reverseOrderDelhivery);
    // router.post("/cancel",delhiveryController.cancelOrder)
    // router.post("/create/fulfillment",delhiveryController.createFulfillment)
    // router.post("/fullfillment", delhiveryController.fulfillOrders)
    // router.post("/create/fullfillmentEvent", delhiveryController.fulfillmentEventCreation)

    app.use('/api/order', router);
  };
  