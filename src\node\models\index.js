const CONFIG = require('../config/config_' + [process.env.NODE_ENV || 'local'] + '.json');
//Database Connection Build
const Sequelize = require("sequelize");
const sequelize = new Sequelize(CONFIG.database.db, CONFIG.database.user, CONFIG.database.password, {
  host: CONFIG.database.host,
  dialect: CONFIG.database.dialect,
  operatorsAliases: 0,
  logging: false,
  pool: {
    max: CONFIG.database.pool.max,
    min: CONFIG.database.pool.min,
    acquire: CONFIG.database.pool.acquire,
    idle: CONFIG.database.pool.idle
  },
  dialectOption: {
    useUTC: true
  },
  timezone: "+00:00"
});
//DB Connection
const db = { sequelize: sequelize };

//All Models Include
db.pincodeValidate = require('./pincode_validate.model')(sequelize)
db.shop = require('./shop.model')(sequelize)
db.order_refund = require('./order_refund.model')(sequelize);
db.serviceRequestLog = require('./service_request_log.model')(sequelize)

db.zippeeSkuMapping = require('./zippee_sku_mapping_model')(sequelize)
db.zippeePincodeMapping = require('./zippee_pincode_mapping_model')(sequelize)
db.order = require('./order.model')(sequelize)
db.extendedWarrantyOrder = require('./extended_warranty_order.model')(sequelize)
db.orderItemSplit = require('./order_item_split.model')(sequelize)
db.cfaStockMapping = require('./cfa_stock_mapping.model')(sequelize)
db.fulfillmentItem = require('./fulfillment_item.model')(sequelize)
db.orderCustomer = require('./order_customer.model')(sequelize)
db.orderAddress = require('./order_address.model')(sequelize)
db.orderItem = require('./order_item.model')(sequelize)
db.product = require('./product.model')(sequelize)
db.productVariant = require('./product_variant.model')(sequelize)
db.cfaPlantLocation = require('./cfa_plant_location.model')(sequelize)

// db.cfaTypes = require('./cfa_types.model')(sequelize)
// db.cfaPincode = require('./cfa_pincode.model')(sequelize)
// db.cfaStock = require('./cfa_stocks.model')(sequelize)

db.orderProcessing = require('./order_processing.model')(sequelize)
db.pincodeMapping = require('./pincode_mapping.model')(sequelize)
db.productInventory = require('./product_inventory.model')(sequelize)
db.delhiveryLog = require('./delhivery_log.model')(sequelize)
db.sapLog = require('./sap_log.model')(sequelize)
db.productImage = require('./product_image.model')(sequelize)
db.cityStatePin = require('./city_state_crm_pincode.model')(sequelize)
db.mpsShipment = require('./mps_shipment.model')(sequelize)
db.cfaPincodeMapping = require('./cfa_pincode_mapping.model')(sequelize)
db.otpVarification = require('./otp_varification')(sequelize)
db.fulfillmentService = require('./fulfillment_service.model')(sequelize)
db.stateRegionCode = require('./state_region_code.model')(sequelize)
db.orderCancellation = require('./order_cancellation.model')(sequelize)
db.coverMasterSku = require('./cover_master_sku')(sequelize)
db.childSku = require('./child_sku.model')(sequelize)
db.orderLogHistory = require('./order_log_history.model')(sequelize)
db.escalationEmail = require('./escalation_mail.model')(sequelize)
db.creditNoteOrder = require('./credit_note_order.model')(sequelize)

db.orderCustomer.belongsTo(db.orderItemSplit, { as: 'orderCustomers', foreignKey: "order_id", sourceKey: "shopify_order_id" })
db.orderItemSplit.hasOne(db.orderCustomer, { as: 'orderCustomers', foreignKey: "order_id", sourceKey: "shopify_order_id" })
db.orderAddress.belongsTo(db.orderItemSplit, { as: 'orderAddresses', foreignKey: "order_id", sourceKey: "shopify_order_id" })
db.orderItemSplit.hasOne(db.orderAddress, { as: 'orderAddresses', foreignKey: "order_id", sourceKey: "shopify_order_id" })

db.fulfillmentItem.belongsTo(db.orderItemSplit, { as: 'fulfillmentItem', foreignKey: "order_id", sourceKey: "id" })
db.orderItemSplit.hasMany(db.fulfillmentItem, { as: 'fulfillmentItem', foreignKey: "order_id", sourceKey: "id" })


db.delhiveryLog.belongsTo(db.orderItemSplit, { as: 'delhiveryLog', foreignKey: "order_name", sourceKey: "order_name" })
db.orderItemSplit.hasMany(db.delhiveryLog, { as: 'delhiveryLog', foreignKey: "order_name", sourceKey: "order_name" })

db.serviceRequestLog.belongsTo(db.orderItemSplit, { as: 'serviceRequestLog', foreignKey: "order_name", sourceKey: "order_name" })
db.orderItemSplit.hasMany(db.serviceRequestLog, { as: 'serviceRequestLog', foreignKey: "order_name", sourceKey: "order_name" })

db.sapLog.belongsTo(db.orderItemSplit, { as: 'sapLog', foreignKey: "order_name", sourceKey: "order_name" })
db.orderItemSplit.hasMany(db.sapLog, { as: 'sapLog', foreignKey: "order_name", sourceKey: "order_name" })

db.order_refund.belongsTo(db.orderItemSplit, { as: 'orderRefund', foreignKey: "order_name", sourceKey: "order_name" })
db.orderItemSplit.hasMany(db.order_refund, { as: 'orderRefund', foreignKey: "order_name", sourceKey: "order_name" })

db.sapLog.belongsTo(db.extendedWarrantyOrder, { as: 'extendedWarrantyOrder', foreignKey: "order_name", sourceKey: "order_name" })
db.extendedWarrantyOrder.hasMany(db.sapLog, { as: 'sapLog', foreignKey: "order_name", sourceKey: "order_name" })


db.extendedWarrantyOrder.belongsTo(db.orderItemSplit, { as: 'orderItemSplit', foreignKey: "order_item_split_id", sourceKey: "id" })
db.orderItemSplit.hasMany(db.extendedWarrantyOrder, { as: 'extendedWarrantyOrder', foreignKey: "order_item_split_id", sourceKey: "id" })

db.orderCustomer.belongsTo(db.extendedWarrantyOrder, { as: 'extendedWarrantyOrder', foreignKey: "order_id", sourceKey: "shopify_order_id" })
db.extendedWarrantyOrder.hasOne(db.orderCustomer, { as: 'orderCustomers', foreignKey: "order_id", sourceKey: "shopify_order_id" })
db.orderAddress.belongsTo(db.extendedWarrantyOrder, { as: 'extendedWarrantyOrder', foreignKey: "order_id", sourceKey: "shopify_order_id" })
db.extendedWarrantyOrder.hasOne(db.orderAddress, { as: 'orderAddresses', foreignKey: "order_id", sourceKey: "shopify_order_id" })

db.orderItemSplit.hasOne(db.product, { as: 'product', foreignKey: "shopify_product_id", sourceKey: "shopify_product_id" })
db.product.hasOne(db.productImage, { as: 'productImage', foreignKey: "product_id", sourceKey: "id" })

//Exports
module.exports = db;

// SF_762_757358_1429851 10A1 