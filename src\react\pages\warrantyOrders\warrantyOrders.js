import { Card, Layout, Page, TextContainer, Filters, Link, Modal, ChoiceList, Heading, Badge, IndexTable, Stack, Pagination, Button, Popover, Tooltip } from "@shopify/polaris";
import React, { Component } from "react";
import { connect } from "react-redux";
import { SortMinor } from "@shopify/polaris-icons";
import { getAllWarrantyOrdersRequest, exportWarrantyOrdersRequest, pushForWarrantyRequest } from "../../redux/orders/ordersActions";

import { setPagination, isEmpty, priceConversion, dateConversion, disambiguateLabel } from "../../helpers/appHelpers";
import DateRangePicker from "../../childComponent/DateRangePicker";
import moment from "moment";
import { debounce } from "lodash";

let pagination = setPagination();
var sDate = moment().subtract(30, "days").toDate();
var eDate = moment().toDate();

class WarrantyOrders extends Component {
  constructor(props) {
    super(props);
    this.state = {
      queryValue: "",
      selectedResources: [],
      middlewareStatus: [],
      allResourcesSelected: false,
      orderData: {},
      popactive: false,
      sortValue: [],
      pageLoading: false,
      orderDetailModalActive: false,
      startDate: moment(sDate).format("YYYY-MM-DD"),
      endDate: moment(eDate).format("YYYY-MM-DD"),
      selectedDate: {
        start: moment().subtract(30, "days").toDate(),
        end: moment().toDate(),
      },
      selectedDateText: {
        start: moment().subtract(30, "days").format("YYYY-MM-DD"),
        end: moment().format("YYYY-MM-DD"),
      },
      appOrderName: "",
      createdOrderData: {},
      warrantyCancelOrReturn: {},
      orderDetailsTexts: {
        sapOrderNumber: "",
        sapInvoiceNumber: "",
        sapDeliveryNumber: "",
      },
      orderDetailsTextErrors: {
        sapOrderNumber: false,
        sapInvoiceNumber: false,
        sapDeliveryNumber: false,
      },
    };

    this.props.history.replace("/app/warranty");
  }
  componentDidMount() {
    document.title = "Warranty Orders";
    this.getAllOrders();
  }

  handleChanagePage = (action) => {
    this.setState({ ConfirmationLoadingButton: false, allResourcesSelected: false, selectedResources: [], actionsConfirmationActive: false });

    action === "Next" ? pagination.page++ : pagination.page--;
    this.getAllOrders();
  };


  actionsConfirmationClose = () => {
    this.setState({ actionsConfirmationActive: false, ConfirmationLoadingButton: false })
  }

  handleExportOrder = () => {
    let {
      selectedResources,
      queryValue,
      allResourcesSelected,
      startDate,
      endDate,
      middlewareStatus
    } = this.state;
    let { exportWarrantyOrders } = this.props;
    if (allResourcesSelected === true) {
      selectedResources = [];
    }
    const objdata = {
      order_ids: selectedResources,
      isAllResource: allResourcesSelected,
      filters: {
        search: queryValue,
        start_date: startDate,
        end_date: endDate,
        middleware_status: middlewareStatus
      },
    };

    exportWarrantyOrders(
      {
        objdata,
        callback: () => {
          if (!this.props.error) {
            var response = this.props.orderResponseData;
            this.setState({ ConfirmationLoadingButton: false, selectedResources: [], allResourcesSelected: false, actionsConfirmationActive: false });
            this.getAllOrders();
          } else {
            this.setState({ ConfirmationLoadingButton: false })
          }
        },
      });
  };

  handlePushForWarrantyOrder = () => {
    let {
      selectedResources,
      allResourcesSelected,
    } = this.state;
    let { pushForWarrantyOrders } = this.props;
    if (allResourcesSelected === true) {
      selectedResources = [];
    }
    const objdata = {
      order_ids: selectedResources,
      isAllResource: allResourcesSelected,
    };

    pushForWarrantyOrders(
      {
        objdata,
        callback: () => {
          if (!this.props.error) {
            var response = this.props.orderResponseData;
            this.setState({ ConfirmationLoadingButton: false, selectedResources: [], allResourcesSelected: false, actionsConfirmationActive: false });
            this.getAllOrders();
          } else {
            this.setState({ ConfirmationLoadingButton: false })
          }
        },
      });
  };

  debounceEvent(...args) {
    this.debounceEvent = debounce(...args);
    return (e) => {
      e.perstist();
      return this.debounceEvent(e);
    };
  }

  getAllOrders = () => {

    this.setState({ pageLoading: true });
    let { perpage, page } = pagination;
    const {
      queryValue,
      sortValue,
      startDate,
      endDate,
      middlewareStatus
    } = this.state;
    const request = {
      page: page,
      perpage: perpage,
      search: queryValue,
      sort: sortValue,
      startDate: startDate,
      endDate: endDate,
      middlewareStatus: middlewareStatus
    };
    let { getOrdersList } = this.props;

    getOrdersList({
      request,
      callback: () => {
        if (!this.props.error) {
          const { orderListAllData } = this.props;
          const { page, limit, total, totalPage } = orderListAllData?.data;
          pagination = setPagination(page, limit, totalPage, total);
        }
        this.setState({ pageLoading: false });
      },
    });
  };

  handleFiltersQueryChange = (value) => {
    this.setState({ queryValue: value, selectedResources: [] });
    this.debounceEvent(() => {
      pagination.page = 1;
      this.getAllOrders();
    }, 300);
  };

  handleQueryValueRemove = () => {
    this.setState(
      {
        queryValue: "",
      },
      () => {
        this.getAllOrders();
      }
    );
  };

  actionsConfirmationModal = () => {
    this.setState({ ConfirmationLoadingButton: true })
    if (this.state.actionsConfirmationHeader == "Export Orders") {
      this.handleExportOrder();
    }
    if (this.state.actionsConfirmationHeader == "Generate Warranty") {
      this.handlePushForWarrantyOrder();
    }
  }

  handleFiltersClearAll = () => {
    this.handleQueryValueRemove();
    this.handleMiddlewareStatusRemove();
  };

  handleMiddlewareStatusChange = (value) => {
    this.setState(
      {
        middlewareStatus: value,
        selectedResources: [],
      },
      () => {
        pagination.page = 1;
        this.getAllOrders();
      }
    );
  }

  togglePopover = () => {
    this.setState({
      popactive: !this.state.popactive,
    });
  };

  handleChange = (value) => {
    this.setState(
      {
        sortValue: value,
      },
      () => {
        pagination.page = 1;
        this.getAllOrders();
      }
    );
  };
  handleMiddlewareStatusRemove = () => {
    this.setState(
      {
        middlewareStatus: "",
      },
      () => {
        this.getAllOrders();
      }
    );
  }
  handleOrderDetailModal = (appOrderName, item) => {
    let createdOrderObj = {};
    let customerToCfa = {};
    let sapArr = item.sapLog.filter(el => {
      return el.is_cancel == "0" && el.is_return == "0";
    });
    if (item.order_status == "cancel" || item.order_status == "return") {
      let sapArr = item.sapLog.filter((el) => {
        return el.is_return == "1" || el.is_cancel == "1";
      });

      for (let value of sapArr) {
        customerToCfa.orderName = value.order_name;
        customerToCfa.sapOrderNumber = value.sap_order_number;
        customerToCfa.sapInvoiceNumber = value.sap_billing_number;
        customerToCfa.sapDeliveryNumber = value.sap_delivery_number;
      }
      this.setState({
        warrantyCancelOrReturn: customerToCfa
      });
    }
    createdOrderObj.orderName = sapArr.length > 0 ? sapArr[0].order_name : "";
    createdOrderObj.sapOrderNumber = sapArr.length > 0 ? sapArr[0].sap_order_number : "";
    createdOrderObj.sapInvoiceNumber = sapArr.length > 0 ? sapArr[0].sap_billing_number : "";
    createdOrderObj.sapDeliveryNumber = sapArr.length > 0 ? sapArr[0].sap_delivery_number : "";
    this.setState({
      orderDetailModalActive: true,
      appOrderName: appOrderName,
      createdOrderData: createdOrderObj,
      selectedResources: [],
      allResourcesSelected: false,
      modalHeader: "Order Detail /" + appOrderName,
    });
  };
  handleOrderDetailModalClose = () => {
    this.setState({
      orderDetailModalActive: false,
      appOrderName: "",
      selectedResources: [],
      allResourcesSelected: false,
      createdOrderData: {},
    });
  };

  exportActionsModal = () => {
    this.setState({ actionsConfirmationHeader: "Export Orders", actionsConfirmationActive: true });
  }
  warrantyPushActionsModal = () => {
    this.setState({ actionsConfirmationHeader: "Generate Warranty", actionsConfirmationActive: true });
  }

  onSelection = (type, isChecked, id) => {
    let { selectedResources, allResourcesSelected } = this.state;
    const { loading, orderListAllData } = this.props;
    if (!loading) {
      const dataObj = orderListAllData?.data?.data;
      switch (type) {
        case "page":
          allResourcesSelected = false;
          if (isChecked) {
            dataObj.forEach(function (order_data) {
              let id = order_data.order_name;
              if (selectedResources.indexOf(id) === -1) {
                selectedResources.push(id);
              }
            });
          } else {
            selectedResources = [];
          }
          break;
        case "single":
          allResourcesSelected = false;
          if (isChecked) {
            selectedResources.push(id);
          } else {
            selectedResources = selectedResources.filter(function (value) {
              return value !== id;
            });
          }
          break;
        case "all":
          if (isChecked) {
            allResourcesSelected = true;
            dataObj.forEach(function (order_data) {
              let id = order_data.order_name;
              if (selectedResources.indexOf(id) === -1) {
                selectedResources.push(id);
              }
            });
          }
          break;
        default:
          break;
      }
    }
    this.setState({
      selectedResources: selectedResources,
      allResourcesSelected: allResourcesSelected,
    });
  };
  orderListMarkup = () => {
    const { selectedResources } = this.state;
    const { loading, orderListAllData } = this.props;

    let allPage = 1;
    const resourceName = {
      singular: "Order",
      plural: "Orders",
    };
    const bulkActions = [
      {
        content: "Generate Warranty",
        onAction: this.warrantyPushActionsModal,
      },
      {
        content: "Export",
        onAction: this.exportActionsModal,
      }
    ];
    let rowMarkup = [];
    if (!loading) {
      const dataObj = orderListAllData.data.data;
      const { page, limit, total, totalPage } = orderListAllData.data;
      allPage = totalPage;
      pagination = setPagination(page, limit, totalPage, total);
      rowMarkup = dataObj.map(
        (
          item,
          index
        ) => (
          <IndexTable.Row
            id={item.order_name}
            key={item.order_name}
            selected={selectedResources.includes(item.order_name)}
            position={index}
          >
            <IndexTable.Cell>{item.shopify_order_name}</IndexTable.Cell>
            <IndexTable.Cell>
              <Button
                plain
                onClick={() => {
                  this.handleOrderDetailModal(
                    item.order_name,
                    item,
                  );
                }}
              >
                {item.order_name}
              </Button>
            </IndexTable.Cell>
            <IndexTable.Cell>{item.main_order_name}</IndexTable.Cell>
            <IndexTable.Cell>
              {item.orderCustomers ? (
                (item.orderCustomers.first_name == null ||
                  item.orderCustomers.first_name == "null") &&
                  (item.orderCustomers.last_name == null ||
                    item.orderCustomers.last_name == "null") ? (
                  <div>{item.orderCustomers.customer_email}</div>
                ) : (
                  <div className="capitalize">
                    {(item.orderCustomers.first_name != null &&
                      item.orderCustomers.first_name != "null" &&
                      item.orderCustomers.first_name != ""
                      ? item.orderCustomers.first_name + " "
                      : "") +
                      (item.orderCustomers.last_name != null &&
                        item.orderCustomers.last_name != "null" &&
                        item.orderCustomers.last_name != ""
                        ? item.orderCustomers.last_name
                        : "")}
                  </div>
                )
              ) : (
                ""
              )}
            </IndexTable.Cell>

            <IndexTable.Cell>
              {item.middleware_status === "FAILED" ? (
                <Tooltip content={item.failed_reason}>
                  <Badge status="critical">{item.middleware_status}</Badge>
                </Tooltip>
              ) : (
                <Badge status="success">{item.middleware_status}</Badge>
              )}
            </IndexTable.Cell>
            <IndexTable.Cell>
              <div>
                {item.currency == "USD" ? "$" : "₹"}
                {item.discount !== null &&
                  item.discount !== "" &&
                  item.discount !== "null"
                  ? priceConversion(item.order_amount - item.discount)
                  : priceConversion(item.order_amount)}
              </div>
            </IndexTable.Cell>
            <IndexTable.Cell>
              {dateConversion(item.order_created_at)}
            </IndexTable.Cell>
          </IndexTable.Row>
        )
      );
    }
    return (
      <IndexTable
        resourceName={resourceName}
        itemCount={rowMarkup.length}
        selectedItemsCount={
          this.state.allResourcesSelected
            ? "All"
            : this.state.selectedResources.length
        }
        hasMoreItems={allPage > 1 ? true : false}
        onSelectionChange={this.onSelection}
        loading={this.state.pageLoading}
        bulkActions={bulkActions}
        footerContent={pagination.showing}
        headings={[
          { title: "Shopify Order#" },
          { title: "Order#" },
          { title: "Main Order#" },
          { title: "Customer" },
          { title: "Middleware Status" },
          { title: "Amount" },
          { title: "Date" },
        ]}
      >
        {rowMarkup}
      </IndexTable>
    );
  };
  render() {
    const sortOptions = [
      { label: "Date (oldest first)", value: "oldest" },
      { label: "Date (newest first)", value: "newest" },
    ];
    const filters = [
      {
        key: "middlewareStatus",
        label: "Middleware Status",
        filter: (
          <ChoiceList
            title="Middleware Status"
            titleHidden
            choices={[
              { label: "PENDING", value: "PENDING" },
              { label: "PROCESSING", value: "PROCESSING" },
              { label: "INVOICED", value: "INVOICED" },
              { label: "CANCELLED", value: "CANCELLED" },
              { label: "WARRANTY REGISTERED", value: "WARRANTY REGISTERED" },
              // { label: "REVERSE ORDER PROCESSING", value: "REVERSE ORDER PROCESSING" },
              // { label: "REVERSE ORDER INVOICED", value: "REVERSE ORDER INVOICED" },
              { label: "FAILED", value: "FAILED" },
            ]}
            selected={this.state.middlewareStatus || []}
            onChange={this.handleMiddlewareStatusChange}
          />
        ),
        shortcut: true,
      }
    ];
    const appliedFilters = [];
    if (!isEmpty(this.state.middlewareStatus)) {
      const key = "middlewareStatus";
      appliedFilters.push({
        key,
        label: disambiguateLabel(key, this.state.middlewareStatus),
        onRemove: this.handleMiddlewareStatusRemove,
      });
    }
    return (
      <div>
        <Page
          fullWidth
          title="Warranty Orders"
          primaryAction={
            <Stack>
              <DateRangePicker
                align="right"
                primary={this.state.primary}
                handleApplyFilter={(value) => {
                  this.setState(
                    {
                      startDate: value.startDate,
                      endDate: value.endDate,
                      selectedDate: value.selectedDate,
                      selectedDateText: value.selectedDateText,
                    },
                    () => {
                      this.getAllOrders();
                    }
                  );
                }}
              />
            </Stack>
          }
        >
          <Layout>
            <Layout.Section>
              <Card>
                <Card.Section>
                  <div style={{ padding: "16px", display: "flex" }}>
                    <div style={{ flex: 1 }}>
                      <Filters
                        queryPlaceholder="Search"
                        label="Title"
                        filters={filters}
                        queryValue={this.state.queryValue}
                        appliedFilters={appliedFilters}
                        onQueryChange={this.handleFiltersQueryChange}
                        onQueryClear={this.handleQueryValueRemove}
                        onClearAll={this.handleFiltersClearAll}
                      />
                    </div>
                    <div style={{ paddingLeft: "0.4rem" }}>
                      <Button icon={SortMinor} onClick={this.togglePopover}>
                        Sort
                      </Button>
                    </div>
                    <div>
                      <Popover
                        fluidContent={true}
                        active={this.state.popactive}
                        activator={<div></div>}
                        onClose={this.togglePopover.bind(this)}
                        preferredAlignment="right"
                        preferredPosition="below"
                        footerContent
                      >
                        <Card>
                          <Card.Section>
                            <ChoiceList
                              title="Sort By"
                              choices={sortOptions}
                              selected={this.state.sortValue}
                              onChange={this.handleChange}
                            />
                          </Card.Section>
                        </Card>
                      </Popover>
                    </div>
                  </div>
                </Card.Section>
                {this.orderListMarkup()}
                <Card.Section>
                  <Stack
                    alignment="center"
                    vertical="middle"
                    distribution="center"
                  >
                    <Pagination
                      plain
                      hasPrevious={pagination.hasPrevious}
                      onPrevious={() => {
                        this.handleChanagePage("Previous");
                      }}
                      hasNext={pagination.hasNext}
                      onNext={() => {
                        this.handleChanagePage("Next");
                      }}
                    />
                  </Stack>
                </Card.Section>
              </Card>
            </Layout.Section>
          </Layout>
        </Page>

        <Modal
          medium
          title={this.state.modalHeader}
          open={this.state.orderDetailModalActive}
          onClose={this.handleOrderDetailModalClose}
        >
          <Modal.Section>
            {Object.values(this.state.warrantyCancelOrReturn).some(val => val) == true ? (
              <div>
                <Heading>Order Cancel/Return</Heading>
                <br></br>
                <div>
                  <Stack distribution="fillEvenly">
                    {(<p>Order#</p>)}
                    {(<p>SAPOrder#</p>)}
                    {(<p>SAPInvoice#</p>)}
                    {/* {(<p>SAPDelivery#</p>)} */}
                  </Stack>
                  <Stack distribution="fillEvenly">
                    {this.state.warrantyCancelOrReturn.orderName == null || this.state.warrantyCancelOrReturn.orderName == "" ? (<div></div>) : (<Badge>{this.state.warrantyCancelOrReturn.orderName}</Badge>)}
                    {this.state.warrantyCancelOrReturn.sapOrderNumber == null || this.state.warrantyCancelOrReturn.sapOrderNumber == "" ? (<div></div>) : (<Badge>{this.state.warrantyCancelOrReturn.sapOrderNumber}</Badge>)}
                    {this.state.warrantyCancelOrReturn.sapInvoiceNumber == null || this.state.warrantyCancelOrReturn.sapInvoiceNumber == "" ? (<div></div>) : (<Badge>{this.state.warrantyCancelOrReturn.sapInvoiceNumber}</Badge>)}
                    {/* {this.state.warrantyCancelOrReturn.sapDeliveryNumber == null || this.state.warrantyCancelOrReturn.sapDeliveryNumber == "" ? (<div></div>) : (<Badge>{this.state.warrantyCancelOrReturn.sapDeliveryNumber}</Badge>)} */}
                  </Stack>
                </div>
                <br></br>
              </div>
            ) : null}

            {Object.values(this.state.createdOrderData).some(val => val) == true ? (
              <div>
                <Stack distribution="equalSpacing">
                  <Heading>Order Created</Heading>
                  {/* <Link onClick={() => this.handleOrderDetailsEdit('created')}>Edit</Link> */}
                </Stack>
                <br></br>
                <Stack distribution="fillEvenly">
                  {(<p>Order#</p>)}
                  {(<p>SAPOrder#</p>)}
                  {(<p>SAPInvoice#</p>)}
                  {/* {(<p>SAPDelivery#</p>)} */}
                </Stack>
                <Stack distribution="fillEvenly" >
                  {this.state.createdOrderData.orderName == null || this.state.createdOrderData.orderName == "" ? (<div></div>) : (<Badge >{this.state.createdOrderData.orderName}</Badge>)}
                  {this.state.createdOrderData.sapOrderNumber == null || this.state.createdOrderData.sapOrderNumber == "" ? (<div></div>) : (<Badge>{this.state.createdOrderData.sapOrderNumber}</Badge>)}
                  {this.state.createdOrderData.sapInvoiceNumber == null || this.state.createdOrderData.sapInvoiceNumber == "" ? (<div></div>) : (<Badge>{this.state.createdOrderData.sapInvoiceNumber}</Badge>)}
                  {/* {this.state.createdOrderData.sapDeliveryNumber == null || this.state.createdOrderData.sapDeliveryNumber == "" ? (<div></div>) : (<Badge>{this.state.createdOrderData.sapDeliveryNumber}</Badge>)} */}
                </Stack>
              </div>
            ) : (
              <div>
                <b>Order is Created</b>
                <br></br>
              </div>)}
          </Modal.Section>
        </Modal>

        <Modal
          medium
          title={this.state.actionsConfirmationHeader}
          open={this.state.actionsConfirmationActive}
          onClose={this.actionsConfirmationClose}
          primaryAction={{
            content: 'Yes',
            onAction: this.actionsConfirmationModal,
            loading: this.state.ConfirmationLoadingButton
          }}
          secondaryActions={{
            content: 'No',
            onAction: this.actionsConfirmationClose
          }}
        >
          <Modal.Section>
            <TextContainer>
              <p>Are you sure you want to do this?</p>
            </TextContainer>
          </Modal.Section>
        </Modal>
      </div>
    );
  }
}

const mapStateToProps = (state) => (
  {
    orderListAllData: state.order.warrantyData,
    loading: state.order.warrantyLoading,
    error: state.order.error,
    orderResponseData: state.order.orderData,
    orderLoading: state.order.orderLoading,
  });

const mapDispatchToProps = (dispatch) => ({
  getOrdersList: (orderData) => dispatch(getAllWarrantyOrdersRequest(orderData)),
  exportWarrantyOrders: (object) => dispatch(exportWarrantyOrdersRequest(object)),
  pushForWarrantyOrders: (object) => dispatch(pushForWarrantyRequest(object)),
});

export default connect(mapStateToProps, mapDispatchToProps)(WarrantyOrders);