const Sequelize = require("sequelize");
module.exports = (sequelize) => {
    return sequelize.define("order_processing", {
        id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            unique: true
        },
        order_name: {
            type: Sequelize.STRING,
        },
        type: {
            type: Sequelize.DataTypes.ENUM('manual', 'cron'),
            allowNull: true
        },
        status: {
            type: Sequelize.DataTypes.ENUM('pending', 'success', 'failed'),
            defaultValue: 'pending'
        }
    },
        {
            // indexes: [
            //     {
            //         unique: true,
            //         fields: ['order_name', 'status']
            //     }
            // ]
        });
};