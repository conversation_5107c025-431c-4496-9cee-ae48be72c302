const { default: ShopifyApi, DataType, ApiVersion } = require('@shopify/shopify-api');
const axios = require('axios');

module.exports = class Fulfillment {
    constructor(shop, token, order_id) {
        this.client = new ShopifyApi.Clients.Rest(shop, token)
        this.options = {
            headers: {
                'X-Shopify-Access-Token': token,
                'Content-Type': 'application/json'
            }
        }
        this.shop = shop
        this.path = `https://${shop}/admin/api/2025-07`
    }
    complete = (id, fulfillId) => {
        return new Promise(async (resolve, reject) => {
            let path = `https://${this.shop}/admin/api/2021-10/orders/${id}/fulfillments/${fulfillId}/complete.json`
            let bodyObj = {}
            await axios.post(path, bodyObj, this.options)
                .then(res => {
                    resolve({ data: res.data.fulfillment })
                })
                .catch(err => {
                    // console.log('err fulfilment====>>', err)
                    resolve({ status: false })
                })
        })
    }
    createByGraph = async (orderId, lineItemId, trackingNo) => {
        return new Promise(async (resolve, reject) => {
            let path = this.path + `/graphql.json`
            let bodyObj = {
                "query": `mutation fulfillmentCreate($fulfillment: FulfillmentInput!) { 
                    fulfillmentCreate(fulfillment: $fulfillment) { 
                        fulfillment {
                            id
                            trackingInfo(first: 2) {
                                company
                                number
                                url
                            }
                            location {
                                id
                            }
                            fulfillmentOrders(first: 1) {
                                nodes {
                                    id
                                    lineItems(first: 1) {
                                        nodes {
                                            id
                                            totalQuantity
                                            remainingQuantity
                                            productTitle
                                        }
                                    }
                                }
                            }
                            createdAt
                            updatedAt
                        }
                        userErrors { 
                            message 
                        } 
                    } 
                }`,
                "variables": {
                    "fulfillment": {
                        "lineItemsByFulfillmentOrder": {
                            "fulfillmentOrderId": `gid://shopify/FulfillmentOrder/${orderId}`,
                            "fulfillmentOrderLineItems": { "id": `gid://shopify/FulfillmentOrderLineItem/${lineItemId}`, "quantity": 1 }
                        },
                        "trackingInfo": {
                            "numbers": trackingNo,
                            "company": "Delhivery"
                        }
                    }
                }
            }

            await axios.post(path, bodyObj, this.options)
                .then(res => {
                    if (res.data.errors) throw new Error(res.data.errors[0].message)
                    let fulfillResult = res.data.data.fulfillmentCreate.fulfillment;
                    // Get quantities from the first fulfillmentOrder/lineItem
                    let totalQuantity = null, remainingQuantity = null;
                    if (fulfillResult.fulfillmentOrders?.nodes?.length > 0) {
                        const fo = fulfillResult.fulfillmentOrders.nodes[0];
                        if (fo.lineItems?.nodes?.length > 0) {
                            totalQuantity = fo.lineItems.nodes[0].totalQuantity;
                            remainingQuantity = fo.lineItems.nodes[0].remainingQuantity;
                        }
                    }
                    let fulfillment_status = this.getFulfillmentStatus(totalQuantity, remainingQuantity);
                    let createResponse = {
                        id: (fulfillResult.id).replace("gid://shopify/Fulfillment/", ""),
                        line_items: [{ fulfillment_status }],
                        tracking_url: fulfillResult.trackingInfo[0]?.url,
                        location_id: (fulfillResult.location.id).replace("gid://shopify/Location/", ""),
                        created_at: fulfillResult.createdAt,
                        updated_at: fulfillResult.updatedAt
                    };
                    resolve({ data: createResponse });
                })
                .catch(err => {
                    console.log('Error createByGraph====>>', err)
                    resolve({ status: err?.response?.status })
                })
        })
    }

    updateByGraph = async (fulfillId, data) => {
        return new Promise(async (resolve, reject) => {
            let path = this.path + `/graphql.json`
            let bodyObj = {
                "query": `mutation fulfillmentTrackingInfoUpdate(
                    $fulfillmentId: ID!
                    $trackingInfoInput: FulfillmentTrackingInput!
                ) {
                    fulfillmentTrackingInfoUpdate(
                        fulfillmentId: $fulfillmentId
                        trackingInfoInput: $trackingInfoInput
                    ) {
                        fulfillment {
                            id
                            trackingInfo(first: 10) {
                                company
                                number
                                url
                            }
                            location {
                                id
                            }
                            fulfillmentLineItems(first: 1) {
                                nodes {
                                    lineItem {
                                        fulfillmentStatus
                                    }
                                }
                            }
                            fulfillmentOrders(first: 1) {
                                nodes {
                                    id
                                    lineItems(first: 1) {
                                        nodes {
                                            id
                                            totalQuantity
                                            remainingQuantity
                                            productTitle
                                        }
                                    }
                                }
                            }
                            createdAt
                            updatedAt
                        }
                        userErrors {
                            message
                        }
                    }
                }`,
                "variables": {
                    "fulfillmentId": `gid://shopify/Fulfillment/${fulfillId}`,
                    "trackingInfoInput": {
                        "company": data.tracking_info.company,
                        "numbers": data.tracking_info.number
                    }
                }
            }
            await axios.post(path, bodyObj, this.options)
                .then(res => {
                    if (res.data.errors) throw new Error(res.data.errors[0].message)
                    let fulfillResult = res.data.data.fulfillmentTrackingInfoUpdate.fulfillment;
                    // Get quantities from the first fulfillmentOrder/lineItem
                    let totalQuantity = null, remainingQuantity = null;
                    if (fulfillResult.fulfillmentOrders?.nodes?.length > 0) {
                        const fo = fulfillResult.fulfillmentOrders.nodes[0];
                        if (fo.lineItems?.nodes?.length > 0) {
                            totalQuantity = fo.lineItems.nodes[0].totalQuantity;
                            remainingQuantity = fo.lineItems.nodes[0].remainingQuantity;
                        }
                    }
                    let fulfillment_status = this.getFulfillmentStatus(totalQuantity, remainingQuantity);
                    let createResponse = {
                        id: (fulfillResult.id).replace("gid://shopify/Fulfillment/", ""),
                        line_items: [{ fulfillment_status }],
                        tracking_url: fulfillResult.trackingInfo[0]?.url,
                        location_id: (fulfillResult.location.id).replace("gid://shopify/Location/", ""),
                        created_at: fulfillResult.createdAt,
                        updated_at: fulfillResult.updatedAt
                    };
                    resolve({ data: createResponse });
                })
                .catch(err => {
                    console.log('err updateByGraph====>>', err)
                    resolve({ status: err?.response?.status })
                })
        })
    }

    warrantyShopifyFulfillment = async (fulfillmentOrderId, fulfillmentOrderLineItemId) => {
        try {
            // Prepare the GraphQL mutation payload
            const payload = {
                query: `mutation fulfillmentCreate($fulfillment: FulfillmentInput!) {
                    fulfillmentCreate(fulfillment: $fulfillment) {
                        fulfillment {
                            id
                            status
                        }
                        userErrors {
                            field
                            message
                        }
                    }
                }`,
                variables: {
                    fulfillment: {
                        lineItemsByFulfillmentOrder: [
                            {
                                fulfillmentOrderId: `gid://shopify/FulfillmentOrder/${fulfillmentOrderId}`,
                                fulfillmentOrderLineItems: [
                                    {
                                        id: `gid://shopify/FulfillmentOrderLineItem/${fulfillmentOrderLineItemId}`,
                                        quantity: 1,
                                    },
                                ],
                            },
                        ],
                        notifyCustomer: false,
                    },
                },
            };

            // Make the API call to Shopify's GraphQL endpoint
            const response = await axios.post(`https://${this.shop}/admin/api/2025-04/graphql.json`, payload, this.options);

            // Check for user errors in the response
            const userErrors = response.data?.data?.fulfillmentCreate?.userErrors;
            if (userErrors && userErrors.length > 0) {
                console.error('User errors in fulfillment creation:', userErrors);
                return { status: false, errors: userErrors };
            }

            // Return the fulfillment data
            const fulfillment = response.data?.data?.fulfillmentCreate?.fulfillment;
            return { status: true, data: fulfillment };
        } catch (err) {
            console.error('Error in warrantyShopifyFulfillment:', err);
            return { status: false, error: err.message };
        }
    };

    // Helper to calculate fulfillment_status
    getFulfillmentStatus = (totalQuantity, remainingQuantity) => {
        if (remainingQuantity === totalQuantity) return 'unfulfilled';
        if (remainingQuantity === 0) return 'fulfilled';
        return 'partial';
    }
}