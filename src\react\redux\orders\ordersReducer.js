import { orderTypes } from "./orderTypes";

const initialState = {
    loading: true,
    data: null,
    warrantyLoading:true,
    error: false,
    orderData: null,
    warrantyData:null,
    orderloading: true,
    cfaData: [],
    cfaListloading: false,
}

export default function ordersReducer(state = initialState, action) {

    switch (action.type) {
        case orderTypes.GET_ORDERS_LIST_REQUEST:
            return { ...state, loading: true };

        case orderTypes.GET_ORDERS_LIST_SUCCESS:
            return { ...state, data: action.payload, loading: false };

        case orderTypes.PUSH_ORDERS_REQUEST:
            return { ...state, error: false };

        case orderTypes.PUSH_ORDERS_SUCCESS:
            return { ...state, error: false };

        case orderTypes.PUSH_ORDERS_ERROR:
            return { ...state, error: true };

        case orderTypes.CANCEL_ORDERS_REQUEST:
            return { ...state, error: false };

        case orderTypes.CANCEL_ORDERS_SUCCESS:
            return { ...state, error: false };

        case orderTypes.CANCEL_ORDERS_ERROR:
            return { ...state, error: true };

        case orderTypes.REPLACEMENT_ORDERS_REQUEST:
            return { ...state, error: false };

        case orderTypes.REPLACEMENT_ORDERS_SUCCESS:
            return { ...state, error: false };

        case orderTypes.REPLACEMENT_ORDERS_ERROR:
            return { ...state, error: true };

        case orderTypes.SUBMIT_ORDER_CFA_REQUEST:
            return { ...state, error: false };

        case orderTypes.SUBMIT_ORDER_CFA_SUCCESS:
            return { ...state, error: false };

        case orderTypes.SUBMIT_ORDER_CFA_ERROR:
            return { ...state, error: true };

        case orderTypes.EXPORT_ORDERS_REQUEST:
            return { ...state, error: false, orderloading: false };

        case orderTypes.EXPORT_ORDERS_SUCCESS: 1
            return { ...state, error: false, orderData: action.payload, orderloading: false };

        case orderTypes.EXPORT_ORDERS_ERROR:
            return { ...state, error: true };

        case orderTypes.GET_ORDERS_SYNC_REQUEST:
            return { ...state, ordersyncloading: true };

        case orderTypes.GET_ORDERS_SYNC_SUCCESS:
            return { ...state, orderSyncData: action.payload, ordersyncloading: false };

        case orderTypes.REFUND_ORDERS_REQUEST:
            return { ...state, error: false, orderloading: false }

        case orderTypes.REFUND_ORDERS_SUCCESS:
            return { ...state, error: false, orderData: action.payload, orderloading: false }

        case orderTypes.REFUND_ORDERS_ERROR:
            return { ...state, error: true }

        case orderTypes.SAVE_ORDER_DETAILS_REQUEST:
            return { ...state, error: false }

        case orderTypes.SAVE_ORDER_DETAILS_SUCCESS:
            return { ...state, error: false }

        case orderTypes.SAVE_ORDER_DETAILS_ERROR:
            return { ...state, error: true }

        case orderTypes.RETURN_ORDER_REQUEST:
            return { ...state, error: false }

        case orderTypes.RETURN_ORDER_REQUEST_SUCCESS:
            return { ...state, error: false }

        case orderTypes.RETURN_ORDER_REQUEST_ERROR:
            return { ...state, error: true }

        case orderTypes.EXPORT_FAILED_ORDERS_SUCCESS:
            return { ...state, error: false }

        case orderTypes.EXPORT_FAILED_ORDERS_ERROR:
            return { ...state, error: true }

        case orderTypes.EXPORT_ORDER_RETURN_REQUEST:
            return { ...state, error: false }

        case orderTypes.EXPORT_ORDER_RETURN_SUCCESS:
            return { ...state, error: false }

        case orderTypes.EXPORT_ORDER_RETURN_ERROR:
            return { ...state, error: true }

        case orderTypes.GET_FAILED_ORDERS_REQUEST:
            return { ...state, loading: true };

        case orderTypes.GET_FAILED_ORDERS_SUCCESS:
            return { ...state, data: action.payload, loading: false };

        case orderTypes.SUBMIT_SERVICE_REQUEST:
            return { ...state, error: false };

        case orderTypes.SUBMIT_SERVICE_SUCCESS:
            return { ...state, error: false };

        case orderTypes.SUBMIT_SERVICE_ERROR:
            return { ...state, error: true };

        case orderTypes.SEND_REFUND_FILE_REQUEST:
            return { ...state, error: false };

        case orderTypes.SEND_REFUND_FILE_SUCCESS:
            return { ...state, error: false };

        case orderTypes.SEND_REFUND_FILE_ERROR:
            return { ...state, error: true };

        case orderTypes.GET_WARRANTY_ORDERS_REQUEST:
            return { ...state, error: false,warrantyLoading: true  };

        case orderTypes.GET_WARRANTY_ORDERS_SUCCESS:
            return { ...state,warrantyData: action.payload, warrantyLoading: false };

        case orderTypes.EXPORT_WARRANTY_ORDERS_REQUEST:
            return { ...state, error: false }

        case orderTypes.EXPORT_WARRANTY_ORDERS_SUCCESS:
            return { ...state, error: false }

        case orderTypes.EXPORT_WARRANTY_ORDERS_ERROR:
            return { ...state, error: true }

        default:
            return state;
    }
}