const Sequelize = require("sequelize");

module.exports = (sequelize) => {
    return sequelize.define('order_refund', {
        id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        store_id: {
            type: Sequelize.INTEGER,
            allowNull: false,
            references: {
                model: 'stores',
                key: 'id'
            }
        },
        order_name: {
            type: Sequelize.STRING,
            unique: "order_name"
        },
        payment_id: {
            type: Sequelize.STRING,
        },
        amount: {
            type: Sequelize.DOUBLE(11, 2),
            defaultValue: '0.00'
        },
        is_refund: {
            type: Sequelize.ENUM('0', '1', '2'),
            defaultValue: '0',
            comment: '0 = pending, 1 = cn-pending, 2 = processed'
        },
        refund_id: {
            type: Sequelize.STRING
        },
        refund_status: {
            type: Sequelize.STRING
        },
        refund_date: {
            type: Sequelize.DATE,
            defaultValue: null
        },
        is_credit_note: {
            type: Sequelize.ENUM('0', '1'),
            defaultValue: '0'
        }
    })
}