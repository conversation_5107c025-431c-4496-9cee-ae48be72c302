const Sequelize = require("sequelize");
module.exports = (sequelize) => {
  return sequelize.define("order_customer", {
    id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      unique: true
    },
    store_id: {
      allowNull: false,
      type: Sequelize.INTEGER,
      references: {
        model: 'stores',
        key: 'id'
      },
    },
    first_name: {
      type: Sequelize.STRING,
    },
    last_name: {
      type: Sequelize.STRING
    },
    accepts_marketing: {
      type: Sequelize.STRING,
    },
    customer_id: {
      type: Sequelize.STRING,
    },
    order_id: {
      allowNull: false,
      type: Sequelize.STRING,
      // references: {
      //   model: 'order_item_splits',
      //   key: 'shopify_order_id'
      // },
    },
    customer_email: {
      type: Sequelize.STRING,
    },
    phone_number: {
      type: Sequelize.STRING
    },
  },
    {
      indexes: [
        {
          unique: true,
          fields: ['store_id', 'customer_email','order_id']
        }
      ]
    });
};