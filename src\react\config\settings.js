const NODE_ENV = process.env.NODE_ENV === 'none' ? 'local' : process.env.NODE_ENV;
const CONFIG = require('../../node/config/config_' + [NODE_ENV || 'local'] + '.json');

export const APP_BASE_URL = CONFIG.shopify.appUrl;

let queryParams = new URLSearchParams(window.location.search)
export const shop = queryParams.get('shop');
if (!shop) {

}
export const shopConfig = { apiKey: CONFIG.shopify.apiKey, shopOrigin: shop, forceRedirect: true };
export const _pagination = {
  hasNext: false,
  hasPrevious: false,
  page: 1,
  perpage: 15,
  showing: null
}

export const settingValue = '{"completed":[1,2],"skipped":false}';
export const themeAccentColor = "#008060";