export const settingsTypes = {
    GET_CLOUD_SKU_LIST_REQUEST: "GET_CLOUD_SKU_LIST_REQUEST",
    GET_CLOUD_SKU_LIST_SUCCESS: "GET_CLOUD_SKU_LIST_SUCCESS",

    GET_COVER_SKU_LIST_REQUEST: "GET_COVER_SKU_LIST_REQUEST",
    GET_COVER_SKU_LIST_SUCCESS: "GET_COVER_SKU_LIST_SUCCESS",

    SAVE_CLOUD_SKU_FILE_REQUEST: "SAVE_CLOUD_SKU_FILE_REQUEST",
    SAVE_CLOUD_SKU_FILE_SUCCESS: "SAVE_CLOUD_SKU_FILE_SUCCESS",
    SAVE_CLOUD_SKU_FILE_ERROR: "SAVE_CLOUD_SKU_FILE_ERROR",

    SAVE_COVER_SKU_FILE_REQUEST: "SAVE_COVER_SKU_FILE_REQUEST",
    SAVE_COVER_SKU_FILE_SUCCESS: "SAVE_COVER_SKU_FILE_SUCCESS",
    SAVE_COVER_SKU_FILE_ERROR: "SAVE_COVER_SKU_FILE_ERROR",

    COVER_SKU_EXPORT_REQUEST: "COVER_SKU_EXPORT_REQUEST",
    COVER_SKU_EXPORT_SUCCESS: "COVER_SKU_EXPORT_SUCCESS",
    COVER_SKU_EXPORT_ERROR: "COVER_SKU_EXPORT_ERROR",

    CLOUD_SKU_EXPORT_REQUEST: "CLOUD_SKU_EXPORT_REQUEST",
    CLOUD_SKU_EXPORT_SUCCESS: "CLOUD_SKU_EXPORT_SUCCESS",
    CLOUD_SKU_EXPORT_ERROR: "CLOUD_SKU_EXPORT_ERROR",

    DELETE_COVER_SKU_REQUEST: "DELETE_COVER_SKU_REQUEST",
    DELETE_COVER_SKU_SUCCESS: "DELETE_COVER_SKU_SUCCESS",
    DELETE_COVER_SKU_ERROR: "DELETE_COVER_SKU_ERROR",

    DELETE_CLOUD_SKU_REQUEST: "DELETE_CLOUD_SKU_REQUEST",
    DELETE_CLOUD_SKU_SUCCESS: "DELETE_CLOUD_SKU_SUCCESS",
    DELETE_CLOUD_SKU_ERROR: "DELETE_CLOUD_SKU_ERROR"
}
export const toastObject = {
    message: "",
    isError: false,
    isActive: false
}