const Sequelize = require("sequelize");
module.exports = (sequelize) => {
  return sequelize.define("store", {
    id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      unique: true
    },
    store_id: {
      type: Sequelize.STRING,
    },
    name: {
      type: Sequelize.STRING
    },
    email: {
      type: Sequelize.STRING
    },
    myshopify_domain: {
      type: Sequelize.STRING
    },
    shop_owner: {
      type: Sequelize.STRING
    },
    token: {
      type: Sequelize.STRING
    },
    iana_timezone: {
      type: Sequelize.STRING
    },
    guid: {
      type: Sequelize.STRING
    },
    shopify_plan_name: {
      type: Sequelize.STRING
    },
    access_scopes: {
      type: Sequelize.STRING
    },
    status: {
      type: Sequelize.DataTypes.ENUM('0', '1'),
      defaultValue: '0'
    },
    is_shop_install: {
      type: Sequelize.DataTypes.ENUM('0', '1'),
      defaultValue: '0'
    },
    is_deleted: {
      type: Sequelize.DataTypes.ENUM('0', '1'),
      defaultValue: '0'
    },
    currency: {
      type: Sequelize.STRING
    },
    shop_created_at:{
        type:Sequelize.STRING
    }
  },
    {
      indexes: [
        {
          unique: true,
          fields: ['store_id', 'myshopify_domain']
        }
      ]
    });
};