import axios from 'axios';
import { APP_BASE_URL } from './settings';
import createApp from '@shopify/app-bridge';
import { getSessionToken } from "@shopify/app-bridge-utils";
const CONFIG = require('../../node/config/config_' + [process.env.NODE_ENV || 'local'] + '.json');
import { Redirect } from '@shopify/app-bridge/actions';
let search = window.location.search;
let params = new URLSearchParams(search);
if(params.get('token')){
    let jwt_token = params.get('token');
    localStorage.setItem('token', jwt_token);
}
// const urlParams = new URLSearchParams(window.location.search);
// const shop = urlParams.get('shop');
// const app = createApp({
// 	apiKey: CONFIG.shopify.apiKey,
// 	shopOrigin: shop,
// 	forceRedirect: true
// });

const instance = axios.create({
	baseURL: `${APP_BASE_URL}/api`,
	headers: {
		'Content-Type': 'application/json',
		'Access-Control-Allow-Origin': '*',
	},
});

instance.defaults.headers.common['Authorization'] = `Bearer ` + localStorage.getItem('token');
// instance.interceptors.request.use(function (config) {
// 	return getSessionToken(app) // requires an App Bridge instance
// 		.then((token) => {
// 			// append your request headers with an authenticated token
// 			config.headers["Authorization"] = `Bearer ${token}`;
// 			return config;
// 		});
// });

instance.interceptors.response.use(
	function (response) {
		return response;
	},
	function (error) {
		console.log("errr",error)
		if (error.response.status === 401) {
			localStorage.removeItem('token')
			window.location.href = "/shoplogin";
		}
		return Promise.reject(error);
	}
);
export default instance;
