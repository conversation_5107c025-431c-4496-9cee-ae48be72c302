let axios = require('axios');
const CONFIG = require('../config/config_' + [process.env.NODE_ENV || 'local'] + '.json');
let dbConnection = require('../models')
exports.orderTrack = (req, res) => {
    try {
        if (req.query.waybill_number) {
            let waybillNumber = req.query.waybill_number;
            trackingOrder(waybillNumber).then((result) => {
                if (result.status) {
                    res.status(200).send(result.shipmentData)
                } else {
                    return res.status(200).send({ "ShipmentData": [] });
                }
            })
        } else if (req.query.orderId) {
            let orderId = `#${req.query.orderId}`
            dbConnection.orderItemSplit.findAll({
                where: { shopify_order_name: orderId }
            }).then((result) => {
                if (result) {
                    let wayBillNoArr = [];
                    result.forEach(key => {
                        if (key.dataValues.waybill_number != null) {
                            wayBillNoArr = key.dataValues.waybill_number;
                        }
                    });
                    if (wayBillNoArr.length > 0) {
                        let waybillNumber = wayBillNoArr.toString();
                        let orderNameArr = result.map((key) => {
                            return {
                                orderName: key.dataValues.order_name,
                                waybillCount: key.dataValues.waybill_number.split(',')
                            }

                        })
                        trackingOrder(waybillNumber).then((result) => {
                            if (result.status) {
                                res.status(200).send({ "ShipmentData": result.shipmentData, "OrderName": orderNameArr, "WayBill": waybillNumber.split(','), status: true })
                            } else {
                                res.status(200).send({ "ShipmentData": [], status: false });
                            }
                        })
                    } else {
                        res.status(200).send({ "ShipmentData": [], status: false });
                    }
                }
                else {
                    res.status(200).send({ "ShipmentData": [], status: false });
                }
            })
        } else {
            res.status(200).send({ "ShipmentData": [], status: false });
        }
    } catch (error) {
        res.status(200).send({ "ShipmentData": [] });
    }
}

trackingOrder = (waybillNumber) => {
    return new Promise(async (resolve, reject) => {
        let trackUrl = CONFIG.delhivery.track_order_url;
        let trackOrderResponse = await axios.get(trackUrl, {
            params: { waybill: waybillNumber }
        });
        if (trackOrderResponse.data.Error) {
            resolve({ status: false })
        } else {
            let shipmentData = [];
            for (let i = 0; i < trackOrderResponse.data.ShipmentData.length; i++) {
                let shipment = [], shipmentObj = {}, scanArr = [];
                let scansLength = trackOrderResponse.data.ShipmentData[i].Shipment.Scans.length;
                for (let j = 0; j < scansLength; j++) {
                    shipment.push(trackOrderResponse.data.ShipmentData[i].Shipment.Scans[j].ScanDetail)
                }
                shipment.push(trackOrderResponse.data.ShipmentData[i].Shipment.Status)
                shipmentObj = {
                    "Shipment": shipment
                }
                shipmentData.push(shipmentObj)
            }
            let shipmentDataObj = {
                "ShipmentData": shipmentData
            }
            resolve({ status: true, shipmentData: shipmentDataObj })
        }
    })
}
