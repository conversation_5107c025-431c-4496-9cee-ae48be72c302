let base64 = require('base-64');
const axios = require('axios');
const CONFIG = require('../config/config_' + [process.env.NODE_ENV || 'local'] + '.json');
const Transaction = require('../shopifyObjects/Transaction');
let dbConnection = require('../models');
const { QueryTypes, Op } = require('sequelize');

exports.fetchRateForEmi = (paymentId) => {
    return new Promise(async (resolve, reject) => {
        resolve({ status: false })
        // let discountAmount = 0
        // let url = CONFIG.razorpay.url + `payments/${paymentId}?expand[]=offers`;
        // let headers = {
        //     headers: {
        //         'Authorization': 'Basic ' + base64.encode(CONFIG.razorpay.keyId + ":" + CONFIG.razorpay.keySecret),
        //         'Content-Type': 'application/json'
        //     }
        // }
        // await axios.get(url, headers).then(fetchResponse => {
        //     if (fetchResponse.data) {
        //         if (fetchResponse.data.offers) {
        //             for (let item of fetchResponse.data.offers.items) {
        //                 if (item.id == "offer_IjCn4MV3WiMfLT") {
        //                     // resolve({ status: true, data: discountAmount })
        //                     resolve({ status: false })
        //                 }
        //             }
        //             resolve({ status: false })
        //         } else {
        //             resolve({ status: false, data: discountAmount })
        //         }
        //     } else {
        //         console.log("error===>1")
        //         resolve({ status: false })
        //     }
        // }).catch(err => {
        //     console.log("error===>", err)
        //     resolve({ status: false })
        // });

    })
}

// get razorpayment id
const getPaymentId = (shop, token, orderId) => {
    return new Promise(async (resolve, reject) => {
        try {
            let transaction = new Transaction(shop, token, orderId)
            let transactionRes = await transaction.get(orderId)
            console.log('transactionRes===>', JSON.stringify(transactionRes))
            if (transactionRes.data && transactionRes.data.length > 0) {
                for (transaction of transactionRes.data) {
                    if (transaction?.kind?.toLowerCase() == "sale" && transaction.status.toLowerCase() == 'success' && transaction.gateway != 'gift_card') {
                        console.log('transaction.receiptJson===>', transaction.receiptJson)

                        // Parse receiptJson to show the actual payment_id
                        let parsedReceiptJson = null;
                        try {
                            parsedReceiptJson = typeof transaction.receiptJson === 'string'
                                ? JSON.parse(transaction.receiptJson)
                                : transaction.receiptJson;
                        } catch (e) {
                            console.log('Error parsing receiptJson for logging:', e);
                        }
                        console.log('parsed receiptJson.payment_id===>', parsedReceiptJson?.payment_id)
                        if (transaction.gateway == "snapmint") {
                            resolve({ status: true, payId: transaction.paymentId, gateway: 'SNAPMINT' })
                        } else if (transaction.order.sourceName == "checkout_one") {
                            resolve({ status: true, payId: transaction.paymentId })
                        } else {
                            if (transaction.authorizationCode != null && transaction.authorizationCode != "null") {
                                let authorization = transaction.authorizationCode.split("|")[1] ? transaction.authorizationCode.split("|")[1] : null
                                resolve({ status: true, payId: authorization, data: transaction })
                            } else if (transaction.receiptJson) {
                                try {
                                    // Parse the receiptJson string to get the actual JSON object
                                    let receiptData = typeof transaction.receiptJson === 'string'
                                        ? JSON.parse(transaction.receiptJson)
                                        : transaction.receiptJson;

                                    console.log('Parsed receiptData===>', receiptData);

                                    if (receiptData && receiptData.payment_id) {
                                        console.log('okay')
                                        let paymentId = await this.getAllPaymentDetails(receiptData.payment_id)
                                        console.log('lllll')
                                        paymentId.status ? resolve({ status: true, payId: paymentId.data }) : resolve({ status: false })
                                    } else {
                                        console.log('No payment_id found in receiptJson');
                                        resolve({ status: false })
                                    }
                                } catch (parseError) {
                                    console.log('Error parsing receiptJson:', parseError);
                                    resolve({ status: false })
                                }
                            }
                        }
                    }
                }
            } else {
                resolve({ status: false })
            }
        } catch (error) {
            console.log("Error getPaymentId----", error.message);
            resolve({ status: false })
        }
    })
}
exports.getPaymentId = getPaymentId;

exports.getAllPaymentDetails = async (receiptId) => {
    try {
        console.log("receiptId===>", receiptId)
        let url = CONFIG.razorpay.url + `orders?expand[]=payments&receipt=${receiptId}`;
        let headers = {
            headers: {
                'Authorization': 'Basic ' + base64.encode(CONFIG.razorpay.keyId + ":" + CONFIG.razorpay.keySecret),
                'Content-Type': 'application/json'
            }
        }
        let transactionRes = await axios.get(url, headers)
        console.log("transactionRes===>", transactionRes.data)
        if (transactionRes && transactionRes.data && transactionRes.data.items) {
            let paymentArr = transactionRes.data.items
            for (let i = 0; i < paymentArr.length; i++) {
                let itemsArr = paymentArr[i].payments.items;
                for (let j = 0; j < itemsArr.length; j++) {
                    if (itemsArr[j].status == 'captured') {
                        let payId = itemsArr[j].id ? itemsArr[j].id : null
                        return { status: true, data: payId }
                    }
                }
            }
        } else {
            return { status: false }
        }
    } catch (error) {
        console.log("Error getAllPaymentDetails==", error);
        return { status: false }
    }
}

exports.createRefund = (refundData) => {
    return new Promise(async (resolve) => {
        let order_name = refundData.order_name, payment_id = refundData.payment_id, amount = refundData.amount;
        let url = CONFIG.razorpay.url + `payments/${payment_id}/refund`;
        let headers = {
            headers: {
                'Authorization': 'Basic ' + base64.encode(CONFIG.razorpay.keyId + ":" + CONFIG.razorpay.keySecret),
                'Content-Type': 'application/json'
            }
        }
        let refundObj = {};
        let refundAmount = amount * 100;
        axios.post(url, {
            "amount": refundAmount,
            "speed": "normal",
            "receipt": order_name
        }, headers).then(async (refundGenerated) => {
            console.log("refundGenerated==>", refundGenerated)
            refundObj.is_refund = '2';
            refundObj.refund_id = refundGenerated.data.id;
            refundObj.refund_status = refundGenerated.data.status;
            await dbConnection.order_refund.update(refundObj, { where: { order_name: order_name } });
            resolve();
        }).catch(async (err) => {
            console.log("refund razorpay error", err);
            if (err.response) {
                console.log("refund error razorpay", JSON.stringify(err.response.data))
                if (err.response.data && err.response.data.error && err.response.data.error.description == "The payment has been fully refunded already") {
                    refundObj.is_refund = '2';
                    // refundObj.refund_id = refundGenerated.data.id;
                    refundObj.refund_status = "processed";
                    await dbConnection.order_refund.update(refundObj, { where: { order_name: order_name } });
                }

            }
            resolve();
        })
    })
}

exports.fetchMultipleRefunds = (payment_id) => {
    return new Promise(async (resolve) => {
        let url = CONFIG.razorpay.url + `payments/${payment_id}/refunds`;
        let headers = {
            headers: {
                'Authorization': 'Basic ' + base64.encode(CONFIG.razorpay.keyId + ":" + CONFIG.razorpay.keySecret),
                'Content-Type': 'application/json'
            }
        }
        axios.get(url, headers).then(async (multipleRefundData) => {
            resolve({ status: true, data: multipleRefundData.data });
        }).catch(async (err) => {
            console.log("fetch refund razorpay error", err);
            resolve({ status: false });
        })
    })
}

exports.upsertSettlement = (paymentDetails) => {
    return new Promise(async (resolve) => {
        let headers = {
            headers: {
                'Authorization': 'Basic ' + base64.encode(CONFIG.razorpay.keyId + ":" + CONFIG.razorpay.keySecret),
                'Content-Type': 'application/json'
            }
        }

        for (const payment_id in paymentDetails) {
            let url = CONFIG.razorpay.url + `payments/${payment_id}/?expand[]=transaction`;
            await axios.get(url, headers).then(async ({ data }) => {
                console.log("data.transaction====", data.transaction.settled, data.transaction.settlement_id)
                if (data && data.transaction.settled) {
                    await dbConnection.orderItemSplit.update({ settlement_id: data.transaction.settlement_id }, {
                        where: {
                            order_name: {
                                [Op.in]: paymentDetails[payment_id].order_names,
                            }
                        }
                    });
                }
            }).catch((error) => {
                console.log("settalment razorpay error", error);
            })
        }

        resolve();
    }).catch(async (err) => {
        console.log("settalment razorpay error", err);
        resolve();
    })

}

exports.razorpaySettlement = async (orders) => {
    try {
        let payments = {}
        for (let order of orders) {
            order = JSON.parse(JSON.stringify(order));
            order.checkout_id = null;
            if (!order.checkout_id) {
                let shop = await dbConnection.shop.findOne({ where: { id: order.store_id } })
                if (shop) {
                    let { status, payId } = await getPaymentId(shop.myshopify_domain, shop.token, order.shopify_order_id)
                    if (status) {
                        order.checkout_id = payId
                        dbConnection.orderItemSplit.update({ checkout_id: payId }, { where: { order_name: order.order_name } })
                    } else {
                        continue;
                    }
                } else {
                    console.log("shop not found", order.shopify_order_id)
                    continue;
                }
            }
            if (payments[order.checkout_id]) {
                payments[order.checkout_id].total_amount += Number(order.order_amount)
                payments[order.checkout_id].order_names.push(order.order_name)
            } else {
                payments[order.checkout_id] = {
                    total_amount: Number(order.order_amount),
                    order_names: [order.order_name]
                }
            }
        }
        await this.upsertSettlement(payments)
    } catch (error) {
        console.error("Error in razorpaySettlement:", error);
        throw new Error("Error processing Razorpay settlement");
    }
}