let { con } = require("./sqlConnection");
exports.shopifyProducts = async (store_id, product) => {
  let dataObj = {
    title: product.title.replace(/'/g, "\\'"),
    description: product.body_html,
    handle: product.handle,
    published_at: product.published_at,
    store_id: store_id,
    shopify_product_id: product.id,
    product_type: product.product_type,
    tags: product.tags,
    product_created_at: product.created_at,
    product_updated_at: product.updated_at,
  };
  const publishDate = new Date(dataObj.published_at)
    .toISOString()
    .slice(0, 19)
    .replace("T", " ");
  const createDate = new Date(dataObj.product_created_at)
    .toISOString()
    .slice(0, 19)
    .replace("T", " ");
  const updateDate = new Date(dataObj.product_updated_at)
    .toISOString()
    .slice(0, 19)
    .replace("T", " ");
  con.query(
    `SELECT * FROM products WHERE shopify_product_id = '${product.id}'`,
    async (error, productResult) => {
      if (productResult.length > 0) {
        con.query(
          `UPDATE products SET store_id='${store_id}',title='${dataObj.title}',description='${dataObj.description}',handle='${dataObj.handle}',published_at='${publishDate}',
      shopify_product_id='${dataObj.shopify_product_id}',product_type='${dataObj.product_type}',tags='${dataObj.tags}',product_created_at='${createDate}',product_updated_at='${updateDate}' WHERE shopify_product_id = '${product.id}' AND store_id = '${store_id}' `,
          async (err, res) => {
            await this.shopifyProductVariant(store_id, product);
            await this.shopifyProductImage(store_id, product);
            if (err) throw err;
          }
        );
      } else {
        con.query(
          `INSERT INTO products (store_id,title,description,handle,published_at,shopify_product_id,product_type,tags,product_created_at,product_updated_at,createdAt,updatedAt) VALUES
       ('${store_id}','${dataObj.title}','${dataObj.description}','${dataObj.handle}','${publishDate}','${dataObj.shopify_product_id}','${dataObj.product_type}',
       '${dataObj.tags}','${createDate}','${updateDate}','${createDate}', '${updateDate}')`,
          async (err, res) => {
            await this.shopifyProductVariant(store_id, product);
            await this.shopifyProductImage(store_id, product);
            if (err) throw err;
          }
        );
      }
    }
  );
};

exports.shopifyProductVariant = async (store_id, product) => {
  for (let variant of product.variants) {
    con.query(
      `SELECT id FROM products WHERE store_id='${store_id}' AND shopify_product_id = '${product.id}'`,
      async (err, result) => {
        for (let value of result) {
          let dataObj = {
            sku: variant.sku?.replace(/'/g, "\\'"),
            store_id: store_id,
            grams: variant.grams,
            title: variant.title.replace(/'/g, "\\'"),
            weight: variant.weight,
            barcode: variant.barcode,
            option_value_1: variant.option1?.replace(/'/g, "\\'"),
            option_value_2: variant.option2?.replace(/'/g, "\\'"),
            option_value_3: variant.option3?.replace(/'/g, "\\'"),
            option_1: variant.option1 ? product.options[0].name?.replace(/'/g, "\\'") : null,
            option_2: variant.option2 ? product.options[1].name?.replace(/'/g, "\\'") : null,
            option_3: variant.option3 ? product.options[2].name?.replace(/'/g, "\\'") : null,
            taxable: variant.taxable,
            position: variant.position,
            product_id: value.id,
            weight_unit: variant.weight_unit,
            compared_at_price: variant.compare_at_price,
            price: variant.price,
            inventory_policy: variant.inventory_policy,
            inventory_item_id: variant.inventory_item_id,
            requires_shipping: variant.requires_shipping,
            inventory_quantity: variant.inventory_quantity,
            fulfillment_service: variant.fulfillment_service,
            inventory_managment: variant.inventory_managment,
            shopify_variant_id: variant.id,
          };
          if (variant.sku) {
            await createSkuPincode(variant.sku, store_id);
          }
          const date = new Date().toISOString().slice(0, 19).replace("T", " ");
          con.query(
            `SELECT * FROM product_variants WHERE product_id = '${dataObj.product_id}' AND shopify_variant_id='${dataObj.shopify_variant_id}' `,
            async (err, productVariantResult) => {
              if (productVariantResult.length > 0) {
                con.query(
                  `UPDATE product_variants SET store_id='${store_id}',sku='${dataObj.sku}',grams='${dataObj.grams}',title='${dataObj.title}', weight='${dataObj.weight}', barcode='${dataObj.barcode}', option_value_1='${dataObj.option_value_1}', 
            option_value_2='${dataObj.option_value_2}', option_value_3='${dataObj.option_value_3}', taxable='${dataObj.taxable}', position='${dataObj.position}', product_id='${dataObj.product_id}', weight_unit='${dataObj.weight_unit}',
            compared_at_price='${dataObj.compared_at_price}', price='${dataObj.price}', inventory_policy='${dataObj.inventory_policy}', inventory_item_id='${dataObj.inventory_item_id}', requires_shipping='${dataObj.requires_shipping}',
            inventory_quantity='${dataObj.inventory_quantity}', fulfillment_service='${dataObj.fulfillment_service}', inventory_managment='${dataObj.inventory_managment}', shopify_variant_id='${dataObj.shopify_variant_id}' WHERE product_id = '${dataObj.product_id}' AND shopify_variant_id='${dataObj.shopify_variant_id}'`,
                  async (err, res) => {
                    if (err) throw err;
                  }
                );
              } else {
                con.query(
                  `INSERT INTO product_variants (store_id,sku,grams,title,weight,barcode,option_value_1,option_value_2,option_value_3,option_1,option_2,option_3,taxable,position,product_id,weight_unit,compared_at_price,price,inventory_policy,
              inventory_item_id,requires_shipping,inventory_quantity,fulfillment_service,inventory_managment,shopify_variant_id,createdAt,updatedAt) VALUES 
            ('${store_id}','${dataObj.sku}','${dataObj.grams}','${dataObj.title}','${dataObj.weight}','${dataObj.barcode}','${dataObj.option_value_1}','${dataObj.option_value_2}','${dataObj.option_value_3}','${dataObj.option_1}','${dataObj.option_2}',
            '${dataObj.option_3}','${dataObj.taxable}','${dataObj.position}','${dataObj.product_id}','${dataObj.weight_unit}','${dataObj.compared_at_price}',
            '${dataObj.price}','${dataObj.inventory_policy}','${dataObj.inventory_item_id}','${dataObj.requires_shipping}','${dataObj.inventory_quantity}',
            '${dataObj.fulfillment_service}','${dataObj.inventory_managment}','${dataObj.shopify_variant_id}','${date}', '${date}')`,
                  async (err, res) => {
                    if (err) throw err;
                  }
                );
              }
            }
          );
        }
      }
    );
  }
};

exports.shopifyProductImage = async (store_id, product) => {
  for (let image of product.images) {
    con.query(
      `SELECT id FROM products WHERE store_id='${store_id}' AND shopify_product_id = '${product.id}'`,
      async (err, result) => {
        for (let value of result) {
          let dataObj = {
            store_id: store_id,
            product_id: value.id,
            image_id: image.id,
            image_url: image.src,
          };
          const date = new Date().toISOString().slice(0, 19).replace("T", " ");
          con.query(
            `SELECT * FROM product_images WHERE product_id = '${dataObj.product_id}' AND image_id='${image.id}' `,
            async (err, imageResult) => {
              if (imageResult.length > 0) {
                con.query(
                  `UPDATE product_images SET store_id='${store_id}',product_id='${dataObj.product_id}',image_id='${dataObj.image_id}',image_url='${dataObj.image_url}' WHERE product_id = '${dataObj.product_id}' AND image_id='${image.id}'`,
                  (err, res) => {
                    if (err) throw err;
                  }
                );
              } else {
                con.query(
                  `INSERT INTO product_images (store_id,product_id,image_id,image_url,createdAt,updatedAt) VALUES ('${store_id}','${dataObj.product_id}','${dataObj.image_id}','${dataObj.image_url}','${date}', '${date}')`,
                  (err, res) => {
                    if (err) throw err;
                  }
                );
              }
            }
          );
        }
      }
    );
  }
};

let createSkuPincode = async (sku, storeId) => {
  con.query(
    `SELECT * FROM cfa_plant_locations WHERE store_id = '${storeId}'`,
    async (err, plantCode) => {
      if (plantCode.length > 0) {
        for (element of plantCode) {
          let skuObj = {
            store_id: storeId,
            sku: sku,
            pincode_group: element.plant_code,
          };
          const date = new Date().toISOString().slice(0, 19).replace("T", " ");
          con.query(
            `SELECT * FROM cfa_stock_mappings WHERE sku='${sku}' AND pincode_group='${skuObj.pincode_group}'`,
            async (err, skuData) => {
              if (skuData.length > 0) {
                con.query(
                  `UPDATE cfa_stock_mappings SET store_id='${skuObj.store_id}',sku='${sku}',pincode_group='${skuObj.pincode_group}' WHERE sku='${sku}' AND pincode_group='${skuObj.pincode_group}'`,
                  (err, suceessRes) => {
                    if (err) throw err;
                  }
                );
              } else {
                con.query(
                  `INSERT INTO cfa_stock_mappings (store_id,sku,stock,local_stock,pincode_group,createdAt,updatedAt) VALUES ('${storeId}','${sku}',NULL,NULL,'${skuObj.pincode_group}','${date}','${date}')`,
                  (err, res) => {
                    if (err) throw err;
                  }
                );
              }
            }
          );
        }
      } else {
        console.log('no data found')
      }
    }
  );
};
