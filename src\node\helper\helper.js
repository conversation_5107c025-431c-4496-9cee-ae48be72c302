const crypto = require('crypto');
const CONFIG = require('./../config/config_' + [process.env.NODE_ENV || 'local'] + '.json');
exports.getPagination = (page, size) => {
  page = page - 1;
  const limit = size ? +size : 5;
  const offset = page ? page * limit : 0;
  return { limit, offset };
};

exports.getPagingData = (responseData, page, limit) => {
  const { count: total, rows: data } = responseData;
  const totalPage = Math.ceil(total / limit);
  return { page, limit, total, totalPage, data };
};

exports.getStartEndDate = (start_date, end_date) => {
  const start = new Date(start_date);
  const end = new Date(end_date.concat("T23:59:59Z"));
  return { start, end };
}

exports.defaultindiaLocations = (value) => {

  let arr = [
    { state: "Andaman and Nicobar Islands", code: "AN" },
    { state: "Andhra Pradesh", code: "AP" },
    { state: "Arunachal Pradesh", code: "AR" },
    { state: "Assam", code: "AS" },
    { state: "Bihar", code: "BR" },
    { state: "Chandigarh", code: "CH" },
    { state: "Chhattisgarh", code: "CG" },
    { state: "Dadra and Nagar Haveli", code: "DN" },
    { state: "Daman and Diu", code: "DD" },
    { state: "Delhi", code: "DL" },
    { state: "Goa", code: "GA" },
    { state: "Gujarat", code: "GJ" },
    { state: "Haryana", code: "HR" },
    { state: "Himachal Pradesh", code: "HP" },
    { state: "Jammu and Kashmir", code: "JK" },
    { state: "Jharkhand", code: "JH" },
    { state: "Karnataka", code: "KA" },
    { state: "Kerala", code: "KL" },
    { state: "Ladakh", code: "LA" },
    { state: "Lakshadweep", code: "LD" },
    { state: "Madhya Pradesh", code: "MP" },
    { state: "Maharashtra", code: "MH" },
    { state: "Manipur", code: "MN" },
    { state: "Meghalaya", code: "ML" },
    { state: "Mizoram", code: "MZ" },
    { state: "Nagaland", code: "NL" },
    { state: "Odisha", code: "OR" },
    { state: "Puducherry", code: "PY" },
    { state: "Punjab", code: "PB" },
    { state: "Rajasthan", code: "RJ" },
    { state: "Sikkim", code: "SK" },
    { state: "Tamil Nadu", code: "TN" },
    { state: "Telangana", code: "TS" },
    { state: "Tripura", code: "TR" },
    { state: "Uttar Pradesh", code: "UP" },
    { state: "Uttarakhand", code: "UK" },
    { state: "West Bengal", code: "WB" },
  ]
  return arr.find((ele) => ele.code == value)?.state
}


exports.encryptString = (inputString) => {
  const encryptionMethod = 'aes-256-cbc';
  const encryptionKey = CONFIG.shopify.encryptionkey;

  if (!inputString || !encryptionKey) {
    throw new Error('Missing required fields');
  }

  const key = crypto.createHash('sha512').update(encryptionKey).digest('hex').substring(0, 32);
  const iv = crypto.createHash('sha512').update(encryptionKey).digest('hex').substring(0, 16);
  const cipher = crypto.createCipheriv(encryptionMethod, Buffer.from(key), Buffer.from(iv));

  let encrypted = cipher.update(inputString, 'utf8', 'hex');
  encrypted += cipher.final('hex');

  return encrypted;
};

//decrypting the aws credentials
exports.decryptString = (encryptedString) => {
  const encryptionMethod = 'aes-256-cbc';
  const encryptionKey = CONFIG.shopify.encryptionkey;

  if (!encryptedString || !encryptionKey) {
    throw new Error('Missing required fields');
  }

  const key = crypto.createHash('sha512').update(encryptionKey).digest('hex').substring(0, 32);
  const iv = crypto.createHash('sha512').update(encryptionKey).digest('hex').substring(0, 16);
  const decipher = crypto.createDecipheriv(encryptionMethod, Buffer.from(key), Buffer.from(iv));

  let decrypted = decipher.update(encryptedString, 'hex', 'utf8');
  decrypted += decipher.final('utf8');

  return decrypted;
};