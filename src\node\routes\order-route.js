module.exports = app => {
  const ordersyncController = require("../controllers/ordersync-controller.js");
  const router = require("express").Router();
  const upload = require('../helper/uploadFile')


  //outer.get("/sync", ordersyncController.syncOrder);
  router.get("/all", ordersyncController.getAllOrder);
  router.get("/all-return", ordersyncController.getAllReturnOrders);
  router.get("/failed", ordersyncController.getAllFailedOrders);
  
  //router.post("/return-export", ordersyncController.exportReturnOrderData)
  router.post("/failed-export", ordersyncController.exportFailedOrderData)
  router.post("/push", ordersyncController.pushOrder);
  //router.get("/customer_order", ordersyncController.customerOrder);
  router.post("/app/cancel", ordersyncController.cancelAppOrder);
  router.post("/app/return", ordersyncController.returnAppOrder);
  router.post("/app/return-request", ordersyncController.returnOrderRequest);
  //router.post("/createOrder", ordersyncController.createCoverOrder) //not use
  router.post("/refunds", ordersyncController.refundOrderData)
  router.post("/export", ordersyncController.exportOrderData)
  router.post("/push/manually", ordersyncController.pushOrderManually);
  router.put("/update", ordersyncController.editOrderDetails);
  router.post("/submit/request", ordersyncController.createServiceRequest);
  router.post("/refund/import", upload.single('file'), ordersyncController.importRefundData)
  //API Routes
  app.use('/api/order', router);
};
