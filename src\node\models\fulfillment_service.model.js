const Sequelize = require("sequelize");
module.exports = (sequelize) => {
  return sequelize.define("fulfillment_service", {
    id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      unique: true
    },
    fulfillment_service_id: {
      allowNull:false,
      type: Sequelize.STRING
    },
    service_name: {
      allowNull:false,
      type: Sequelize.STRING
    },
    location_id: {
      type: Sequelize.STRING
    }
  });
};