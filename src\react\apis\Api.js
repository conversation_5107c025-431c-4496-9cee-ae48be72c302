import axiosInstance from "../config/axiosInstance";

export default class Api {
    static async fetchAsync(func, param) {
        const response = await func(param);
        if (response.status === 200) {
            return await response;
        }
        throw new Error("Unexpected error!!!");
    }
    static async postAsync(func, param, data) {
        const response = await func(param, data);
        return response;
    }

    //New OneClick page
    static fileUpload(data) {
        const uri = `/page/file/upload`;
        return axiosInstance.request({
            method: 'post',
            url: uri,
            data: data.formData
        });
    }

    static getAllOrders(data) {
        const { fulfillmentStatus, status, perpage, search, paymentStatus, page, sort, startDate, endDate, delhiveryStatus, orderStatus, sapStatus, shipmentStatus, refundStatus, middlewareStatus } = data.request;
        const uri = `/order/all?page=${page}&per_page=${perpage}&fulfilment_status=${fulfillmentStatus}&order_status=${orderStatus}&delhivery_status=${delhiveryStatus}&sap_status=${sapStatus}&financial_status=${paymentStatus}&shipment_status=${shipmentStatus}&search=${search}&sort=${sort}&start_date=${startDate}&end_date=${endDate}&refund_status=${refundStatus}&middleware_status=${middlewareStatus}`;

        return axiosInstance.get(uri);
    }
    static getAllReturnOrders(data) {
        const { fulfillmentStatus, status, perpage, search, paymentStatus, page, sort, startDate, endDate, delhiveryStatus, orderStatus, sapStatus, shipmentStatus, refundStatus } = data.request;
        const uri = `/order/all-return?page=${page}&per_page=${perpage}&return_status=${orderStatus}&search=${search}&sort=${sort}&start_date=${startDate}&end_date=${endDate}`;

        return axiosInstance.get(uri);
    }
    static getFailedAllOrders(data) {
        const { perpage, search, page, sort, startDate, endDate } = data.request;
        const uri = `/order/failed?page=${page}&per_page=${perpage}&search=${search}&sort=${sort}&start_date=${startDate}&end_date=${endDate}`;

        return axiosInstance.get(uri);
    }
    static getWarrantyAllOrders(data) {
        const { perpage, search, page, middlewareStatus, sort, startDate, endDate } = data.request;
        const uri = `/order/all-warranty-order?page=${page}&per_page=${perpage}&search=${search}&sort=${sort}&middleware_status=${middlewareStatus}&start_date=${startDate}&end_date=${endDate}`;

        return axiosInstance.get(uri);
    }

    static getSyncOrdersData() {
        const uri = `/order/sync`;
        return axiosInstance.get(uri);
    }

    static getCFAList() {
        const uri = `/cfa/get`;
        return axiosInstance.get(uri);
    }

    static submitCFAOrders(data) {
        const uri = `/order/push/manually`;
        return axiosInstance.request({
            method: 'post',
            url: uri,
            data: data.orderObj
        });
    }
    static submitServiceRequest(data) {
        const uri = `/order/submit/request`;
        return axiosInstance.request({
            method: 'post',
            url: uri,
            data: data
        });
    }
    static pushOrders(data) {
        const uri = `/order/push`;
        return axiosInstance.request({
            method: 'post',
            url: uri,
            data: data
        });
    }

    static pushForWarranty(data) {
        const uri = `/order/warranty-purchase`;
        return axiosInstance.request({
            method: 'post',
            url: uri,
            data: data
        });
    }

    static returnOrders(data) {
        const uri = `/order/app/return`;
        return axiosInstance.request({
            method: 'post',
            url: uri,
            data: data
        });
    }

    static exportReturnOrders(data) {
        const uri = `/order/failed-export`;
        return axiosInstance.request({
            method: 'post',
            url: uri,
            data: data
        });
    }

    static exportFailedOrders(data) {
        const uri = `/order/return-export`;
        return axiosInstance.request({
            method: 'post',
            url: uri,
            data: data
        });
    }
    static exportWarrantyOrders(data) {
        const uri = `/order/warranty-export`;
        return axiosInstance.request({
            method: 'post',
            url: uri,
            data: data
        });
    }

    static cancelOrders(data) {
        const uri = `/order/app/cancel`;
        return axiosInstance.request({
            method: 'post',
            url: uri,
            data: data
        });
    }
    static replacementOrders(data) {
        const uri = `/order/app/replacement`;
        return axiosInstance.request({
            method: 'post',
            url: uri,
            data: data
        });
    }
    static OrderReturnRequest(data) {
        const uri = `/order/app/return-request`;
        return axiosInstance.request({
            method: 'post',
            url: uri,
            data: data
        });
    }

    static getCfaList(data) {
        const { perpage, search, page, sort } = data.request;
        const uri = `/cfa/all?page=${page}&per_page=${perpage}&search=${search}`;

        return axiosInstance.get(uri);
    }

    static saveCfaFile(data) {
        const uri = `/cfa/import`;
        return axiosInstance.request({
            method: 'post',
            url: uri,
            data: data,
            headers: { 'Content-Type': 'multipart/form-data' }
        });
    }

    static saveRefundFile(data) {
        const uri = `/order/refund/import`;
        return axiosInstance.request({
            method: 'post',
            url: uri,
            data: data,
            headers: { 'Content-Type': 'multipart/form-data' }

        });
    }

    static getCloudSkuList(data) {
        const uri = `/settings/child/get`;
        return axiosInstance.get(uri);
    }
    static getCoverSkuList(data) {
        const uri = `/settings/master/get`;
        return axiosInstance.get(uri);
    }
    static saveCoverFileData(data) {
        const uri = `settings/master/import`;
        return axiosInstance.request({
            method: 'post',
            url: uri,
            data: data,
            headers: { 'Content-Type': 'multipart/form-data' }

        });
    }
    static saveCloudFileData(data) {
        const uri = `settings/child/import`;
        return axiosInstance.request({
            method: 'post',
            url: uri,
            data: data,
            headers: { 'Content-Type': 'multipart/form-data' }

        });
    }
    static exportOrders(data) {
        const uri = `/order/export`;
        return axiosInstance.request({
            method: 'post',
            url: uri,
            // responseType: 'blob',
            data: data,
        });
    }
    static exportCFAstock(data) {
        const uri = `/cfa/stock/export`;
        return axiosInstance.request({
            method: 'get',
            url: uri,
            // responseType: 'blob'
        });
    }
    static getManageCfa() {
        const uri = `/cfa/list`;
        return axiosInstance.request({
            method: 'get',
            url: uri,
        });
    }

    static saveManageCfaFile(data) {
        const uri = data.get('importType') == 'cfa' ? '/cfa/location/import' : "/cfa/stock/import";
        return axiosInstance.request({
            method: 'post',
            url: uri,
            data: data,
            headers: { 'Content-Type': 'multipart/form-data' }
        });
    }

    static ordersRefund(data) {
        const uri = `/order/refunds`;
        return axiosInstance.request({
            method: 'post',
            url: uri,
            data: data
        })
    }

    static exportCoverSku(data) {
        const uri = `/settings/master/export`;
        return axiosInstance.request({
            method: 'get',
            url: uri,
            responseType: 'blob'
        });
    }

    static exportCloudSku(data) {
        const uri = `/settings/child/export`;
        return axiosInstance.request({
            method: 'get',
            url: uri,
            responseType: 'blob'
        });
    }
    static exportPincode(data) {
        const uri = `/cfa/pincode/export`;
        return axiosInstance.request({
            method: 'get',
            url: uri,
            responseType: 'blob'
        });
    }
    static exportCfaList(data) {
        const uri = `/cfa/location/export`;
        return axiosInstance.request({
            method: 'get',
            url: uri,
            responseType: 'blob'
        });
    }
    static deleteCloudSkuData(data) {
        const uri = `/settings/child/delete`;
        return axiosInstance.request({
            method: 'delete',
            url: uri,
            data: data
        });
    }

    static deleteCoverSkuData(data) {
        const uri = `/settings/master/delete`;
        return axiosInstance.request({
            method: 'delete',
            url: uri,
            data: data
        });
    }

    static saveOrderDetails(data) {
        const uri = `/order/update`;
        return axiosInstance.request({
            method: 'put',
            url: uri,
            data: data
        });
    }

    static getAllGstOrdersData(data) {
        let { search, gstValid } = data.request.filter;
        const uri = `/gst/getAllGstOrders?search=${search}&gstValid=${gstValid}`;
        return axiosInstance.get(uri);
    }

    static actionOnGstOrder(data) {
        const uri = `/gst/actionOnGstOrder`;
        return axiosInstance.request({
            method: 'post',
            url: uri,
            data: data.request
        })
    }

}