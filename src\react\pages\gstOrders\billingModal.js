import { Heading, Modal, Select, Stack, TextField } from "@shopify/polaris";
import React, { Component } from "react";
import './gstOrders.css'
import { checkNullOrUndefined, statesArr, textFieldArr } from "../../helpers/appHelpers";
class BillingModal extends Component {
    constructor(props) {
        super(props);
        this.state = {
            textFieldObj: {
                'billing_first_name': "",
                'billing_last_name': "",
                'billing_province_code': "",
                'billing_city': "",
                'billing_company': "",
                'billing_phone': "",
                'billing_zip_code': "",
                'billing_address1': "",
                'billing_address2': "",
            }
        }
    }

    componentDidMount() {
        document.title = "gstOrders";
        this.setDefaultStates()
    }

    setDefaultStates() {
        this.setState({
            textFieldObj: {
                'billing_first_name': checkNullOrUndefined(this.props.billingModalActive.billing_first_name),
                'billing_last_name': checkNullOrUndefined(this.props.billingModalActive.billing_last_name),
                'billing_province_code': checkNullOrUndefined(this.props.billingModalActive.billing_province_code),
                'billing_city': checkNullOrUndefined(this.props.billingModalActive.billing_city),
                'billing_company': checkNullOrUndefined(this.props.billingModalActive.billing_company),
                'billing_phone': checkNullOrUndefined(this.props.billingModalActive.billing_phone),
                'billing_zip_code': checkNullOrUndefined(this.props.billingModalActive.billing_zip_code),
                'billing_address1': checkNullOrUndefined(this.props.billingModalActive.billing_address1),
                'billing_address2': checkNullOrUndefined(this.props.billingModalActive.billing_address2),
            }
        })
    }

    clearTextFieldState() {
        this.props.handleBillingModal(false)
        this.setDefaultStates()
    }

    callTextField = () => {
        let arr = Object.keys(this.state.textFieldObj);
        let elements = []
        for (let index = 0; index < arr.length; index += 2) {
            elements.push(<div id="billing_fields">
                <Stack spacing="loose" key={index} distribution="fill" vertical={false}>
                    {arr[index] == "billing_province_code" ?
                        <Select
                            label={"State"}
                            options={statesArr}
                            onChange={(val) => this.handleTextFieldArr(val, arr[index])}
                            value={this.state.textFieldObj[arr[index]]}
                        /> :
                        <TextField label={textFieldArr[arr[index]]} value={this.state.textFieldObj[arr[index]]} onChange={(val) => this.handleTextFieldArr(val, arr[index])} />}
                    {arr[index + 1] ? <TextField label={textFieldArr[arr[index + 1]]} value={this.state.textFieldObj[arr[index + 1]]} onChange={(val) => this.handleTextFieldArr(val, arr[index + 1])} /> : ""}
                </Stack>
            </div>)
        }
        return elements
    }

    handleTextFieldArr = (val, valueType) => {
        this.setState({ textFieldObj: { ...this.state.textFieldObj, [valueType]: val } })
    }

    callToEditBilling = async () => {
        let textFieldValues = this.state.textFieldObj
        let req = {
            billing_address1: textFieldValues.billing_address1,
            billing_address2: textFieldValues.billing_address2,
            billing_city: textFieldValues.billing_city,
            billing_company: textFieldValues.billing_company,
            billing_first_name: textFieldValues.billing_first_name,
            billing_last_name: textFieldValues.billing_last_name,
            billing_phone: textFieldValues.billing_phone,
            billing_province_code: textFieldValues.billing_province_code,
            billing_zip_code: textFieldValues.billing_zip_code,
        }
        await this.props.callActionOfOrders(this.props.billingModalActive?.shopify_order_id, 'EditBilling', req);
        this.clearTextFieldState()
    }

    render() {
        return (
            <Modal
                title={<Heading element="h2" >Edit Billing Address</Heading>}
                open={this.props.billingModalActive}
                onClose={() => this.clearTextFieldState()}
                primaryAction={{
                    content: 'Save',
                    onAction: this.callToEditBilling,
                    loading: this.props.modalBtnLoading,
                }}
                secondaryActions={{
                    content: 'Cancel',
                    onAction: () => this.clearTextFieldState()
                }}
            >
                <Modal.Section>
                    {this.callTextField()}
                </Modal.Section>
            </Modal>
        )
    }
}

export default BillingModal;
